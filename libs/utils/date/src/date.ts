import { DateTime } from 'luxon';

const TIMEZONE = 'America/New_York';

export const formatEDT = (timestamp: number, longDate?: boolean) => {
  const date = DateTime.fromMillis(timestamp);
  let result = date.setZone(TIMEZONE).toFormat(longDate ? 'MMM d, h:mm a' : 'h:mm a');
  const diff = DateTime.now().diff(date, ['days']);
  if (diff.days > 1) {
    result = date.setZone(TIMEZONE).toFormat('MMM d, h:mm a');
  }
  return result;
};

export const formatISO = (isoDate: string, longDate?: boolean) => {
  const date = DateTime.fromISO(isoDate);
  let result = date.setZone(TIMEZONE).toFormat(longDate ? 'MMM d, h:mm a' : 'h:mm a');
  const diff = DateTime.now().diff(date, ['days']);
  if (diff.days > 1) {
    result = date.setZone(TIMEZONE).toFormat('MMM d, h:mm a');
  }
  return result;
};

export const formatAfterHours = (timestamp: number, longDate?: boolean) => {
  const date = DateTime.fromMillis(timestamp);
  let result = date.setZone(TIMEZONE).toFormat(longDate ? 'MMM d, h:mm a' : 'MMM d');
  const diff = DateTime.now().diff(date, ['days']);
  if (diff.days > 1) {
    result = date.setZone(TIMEZONE).toFormat('MMM d, h:mm a');
  }
  return result;
};

export const isToday = (date: string): boolean => {
  const now = DateTime.local();
  const datetime = DateTime.fromFormat(`${date}`, 'yyyy-MM-dd');
  const isToday = datetime.hasSame(now, 'day');
  return isToday;
};

export const isTodayDate = (date: Date): boolean => {
  const today = new Date();
  return (
    today.getFullYear() === date.getFullYear() &&
    today.getMonth() === date.getMonth() &&
    today.getDate() === date.getDate()
  );
};

// timestamp of last easter timezone midnight
export const getPreviousMidNightAtTimeZone = (timeZone = 'America/New_York') => {
  const now = new Date();
  const time = now.getTime();
  const nowInTimezone = new Date(
    now.toLocaleString('en-US', {
      timeZone,
    }),
  ).getTime();
  const midnight = new Date(nowInTimezone).setHours(0, 0, 0, 0);
  return midnight + (time - nowInTimezone);
};

// timestamp of last easter timezone midnight
export const getNextMidNightAtTimeZone = (timeZone = 'America/New_York') =>
  getPreviousMidNightAtTimeZone(timeZone) + 24 * 60 * 60 * 1000;

export const formatNumericDateTime = (timestamp: number, longDate?: boolean) => {
  const date = DateTime.fromFormat(timestamp.toString(), 'yyyyMMddHHmmss');
  let result = date.toFormat(longDate ? 'MMM d, h:mm a' : 'h:mm a');
  const diff = DateTime.now().diff(date, ['days']);
  if (diff.days > 1) {
    result = date.toFormat('MMM d, h:mm a');
  }
  return result;
};

export const convertToAmPm = (timeString: string) => {
  return DateTime.fromFormat(timeString, 'HH:mm:ss').toFormat('hh:mm a');
};
