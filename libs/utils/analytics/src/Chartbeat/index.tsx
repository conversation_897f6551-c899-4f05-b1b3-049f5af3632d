import { AuthenticationManager, createSession } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import React from 'react';

const devDomains = ['zingbot', 'local.', 'bzsbx.', 'moneysbx.'];

export type ChartbeatUserType = 'anon' | 'lgdin' | 'paid';

export interface ChartbeatAnalyticsProps {
  authorName?: string;
  chartBeatDomain?: string;
  sections?: string[];
  uid: string;
}

export const ChartbeatAnalytics: React.FC<ChartbeatAnalyticsProps> = ({
  authorName = '',
  chartBeatDomain = '',
  sections = [],
  uid,
}) => {
  const chartbeatConfig = `
    (function() {
      var pageDomain = window.location.hostname || "";

      // Check if current domain is a test domain
      if (pageDomain === "" || ${devDomains.map(domain => `pageDomain.includes("${domain}")`).join(' || ')}) {
        pageDomain = 'zingbot.bz';
      } else if('${chartBeatDomain}' === '') {
        pageDomain = pageDomain.replace('www.', '');
      } else {
        pageDomain = '${chartBeatDomain}';
      }

      var _sf_async_config = window._sf_async_config = (window._sf_async_config || {});
      _sf_async_config.uid = '${uid}';
      _sf_async_config.domain = pageDomain;
      _sf_async_config.useCanonical = true;
      _sf_async_config.useCanonicalDomain = false;
      _sf_async_config.sections =  '${sections?.map(section => section.replace(/'/g, "\\'")).join(', ')}';
      _sf_async_config.authors = '${authorName?.replace(/'/g, "\\'")}';
      _sf_async_config.flickerControl = false;

      var _cbq = window._cbq = (window._cbq || []);

      function loadChartbeat(userType) {
        _cbq.push(['_acct', userType]);

        var e = document.createElement('script');
        var n = document.getElementsByTagName('script')[0];
        e.type = 'text/javascript';
        e.async = true;
        e.src = 'https://static.chartbeat.com/js/chartbeat.js';
        n.parentNode.insertBefore(e, n);
      }
      window.loadChartbeat = loadChartbeat;
    })();
  `;

  return (
    <>
      <link as="script" href="https://static.chartbeat.com/js/chartbeat.js" rel="preload" />
      <script dangerouslySetInnerHTML={{ __html: chartbeatConfig }} data-nscript="beforeInteractive" id="chartbeat" />
      <script async src="//static.chartbeat.com/js/chartbeat_mab.js"></script>
    </>
  );
};

export default ChartbeatAnalytics;
