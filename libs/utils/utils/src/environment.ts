export interface AppNamesI {
  bz: string;
  india: string;
  money: string;
  korea: string;
  proto: string;
}

export const appName: AppNamesI = {
  bz: 'default',
  india: 'india',
  korea: 'korea',
  money: 'money',
  proto: 'proto',
};

export const appCode: AppNamesI = {
  bz: 'bz',
  india: 'in',
  korea: 'kr',
  money: 'money',
  proto: 'proto',
};

export interface AppConfigI {
  app: AppNamesI[keyof AppNamesI];
  url: string;
  zone: string;
}

export const appConfig: AppConfigI[] = [
  { app: appName.bz, url: `${process.env.BASE_URL}`, zone: 'America/New_York' },
  { app: appName.money, url: `${process.env.BASE_URL}`, zone: 'America/New_York' },
  { app: appName.india, url: 'https://in.benzinga.com', zone: 'Asia/Kolkata' },
  { app: appName.korea, url: 'https://kr.benzinga.com', zone: 'Asia/Seoul' },
  { app: appName.proto, url: `${process.env.BASE_URL}`, zone: 'America/New_York' },
];

export const appEnvironment = () => {
  const app = () => {
    return process.env.FUSION_APP_NAME?.toLowerCase();
  };

  // Returns appConfig object
  const config = () => {
    const result = appConfig.find(item => {
      return item.app.toLowerCase() === app();
    });
    return result ? result : appConfig[0];
  };

  // Returns app type - "default" appName
  const isApp = (app: AppNamesI[keyof AppNamesI] = appName.bz) => {
    return config().app === app.toLowerCase();
  };

  return { app, config, isApp };
};

export const formatAPIRequestSlug = (slug: string, siteCode: AppNamesI[keyof AppNamesI]): string => {
  if (slug.match(`/^${siteCode}\\//gm`)) {
    return slug;
  }

  return `${siteCode}/${slug}`;
};
