interface ProbeImageResponse {
  width: number;
  height: number;
  type: string; // 'png'
  mime: string; // 'image/png'
  wUnits: string; // 'px'
  hUnits: string; // 'px'
  length: number;
  url: string;
}

const getFallbackValues = (src: string) => {
  return {
    hUnits: 'px',
    height: 0,
    length: 0,
    mime: '',
    type: '',
    url: src,
    wUnits: 'px',
    width: 0,
  };
};

export const probeImage = async (src: string): Promise<ProbeImageResponse | null> => {
  const fallbackValues = getFallbackValues(src);
  try {
    if (src.startsWith('/')) return fallbackValues;
    return fetch(`https://www.benzinga.com/api/probe-image?src=${encodeURIComponent(src)}`)
      .then(res => res.json())
      .then(data => {
        return data;
      });
  } catch (error) {
    return fallbackValues;
  }
};
