'use client';
import React, { useEffect, useState } from 'react';
import { DateTime } from 'luxon';
import { EChartsOption } from 'echarts';
const ReactECharts = React.lazy(() => import('echarts-for-react'));

interface MortgageChartProps {
  esimatedMonthlyPayment: number;
  loanProgram?: string;
  interestRate?: number;
  totalPrincipal: number;
}

export const MortgageChart: React.FC<MortgageChartProps> = ({
  esimatedMonthlyPayment,
  interestRate,
  loanProgram,
  totalPrincipal,
}) => {
  // Divide your interest rate by the number of payments you’ll make that year. If you have a 6 percent interest rate and you make monthly payments, you would divide 0.06 by 12 to get 0.005.

  // Multiply that number by your remaining loan balance to find out how much you’ll pay in interest that month. If you have a $5,000 loan balance, your first month of interest would be $25.

  // Subtract that interest from your fixed monthly payment to see how much in principal you will pay in the first month. If your lender has told you that your fixed monthly payment is $430.33, you will pay $405.33 toward the principal for the first month. That amount gets subtracted from your outstanding balance.

  const [interestSeries, setInterestSeries] = useState<number[]>([]);
  const [principalSeries, setPrincipalSeries] = useState<number[]>([]);
  const [prepareYearSeries, setPrepareYearSeries] = useState<number[]>([]);
  const [balanceSeries, setBalanceSeries] = useState<number[]>([]);
  const [monthSeries, setMonthSeries] = useState<string[]>([]);
  const [totalLoanYears, setTotalLoanYears] = useState<number>(0);

  const [itemsPrepared, setItemsPrepared] = useState<boolean>(false);

  useEffect(() => {
    let totalInterestPaid = 0;
    let totalPrincipalPaid = 0;
    let totalPrincipalBalance = totalPrincipal;

    const monthlyInterestSeries: number[] = [];
    const monthlyPrincipalSeries: number[] = [];
    const monthlyBalannceSeries: number[] = [];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const calculatePrepareYearArr = length => {
      const currentYear = DateTime.now().year;
      const yArr: number[] = [];
      const monthsData: string[] = [];
      yArr.push(currentYear);
      for (let index = 1; index < length; index++) {
        for (let j = 1; j <= 12; j++) {
          monthsData.push(currentYear + index + ' ' + months[j - 1]);
        }
        yArr.push(currentYear + index);
      }

      setMonthSeries(monthsData);
      return yArr;
    };

    const prepareMonthlyPaymentSeries = (rate, esimatedMonthlyPayment, totalPrincipalBalance) => {
      // const rateforYear = rate / 100 / (12 * loanYears);
      const rateforYear = rate / 100;

      const givenMonthInterest = (rateforYear * totalPrincipalBalance) / 12;

      totalInterestPaid = totalInterestPaid + givenMonthInterest;

      monthlyInterestSeries.push(totalInterestPaid);

      const givenMonthPrincipal = esimatedMonthlyPayment - givenMonthInterest;

      totalPrincipalPaid = totalPrincipalPaid + givenMonthPrincipal;
      monthlyPrincipalSeries.push(totalPrincipalPaid);

      totalPrincipalBalance = totalPrincipalBalance - givenMonthPrincipal;

      monthlyBalannceSeries.push(totalPrincipalBalance);

      // setRemainingBalance(balance - givenMonthInterest - givenMonthPrincipal);
      return totalPrincipalBalance;
    };

    const loanYears = calculateLoanLengthYear(loanProgram);
    setTotalLoanYears(loanYears);

    const yearArr = calculatePrepareYearArr(loanYears);
    setPrepareYearSeries(yearArr);

    // const totalBalance = esimatedMonthlyPayment * loanYears * 12;
    monthlyBalannceSeries.push(totalPrincipalBalance);

    for (let index = 1; index <= yearArr.length; index++) {
      for (let j = 1; j <= 12; j++) {
        totalPrincipalBalance = prepareMonthlyPaymentSeries(
          interestRate,
          esimatedMonthlyPayment,
          totalPrincipalBalance,
        );
      }
    }

    setInterestSeries(monthlyInterestSeries);
    setPrincipalSeries(monthlyPrincipalSeries);
    setBalanceSeries(monthlyBalannceSeries);
    setItemsPrepared(true);
  }, [esimatedMonthlyPayment, loanProgram, interestRate, totalPrincipal]);

  const calculateLoanLengthYear = loanProgram => {
    switch (loanProgram) {
      case 'Fixed30Year':
        return 30;
        break;

      case 'Fixed15Year':
        return 15;
        break;

      case 'ARM7':
        return 7;
        break;

      case 'ARM5':
        return 5;
        break;

      default:
        return 30;
        break;
    }
  };

  const topChartOptions = React.useMemo(() => {
    if (itemsPrepared) {
      let balanceIndex = 0;
      let principalIndex = 0;
      interestSeries.map((interest, i) => {
        if (interest <= balanceSeries[i]) {
          balanceIndex = i;
        }
        if (balanceSeries[i] >= principalSeries[i]) {
          principalIndex = i;
        }
      });

      let TheMaxEqBar =
        balanceSeries[0] > interestSeries[interestSeries.length - 1]
          ? balanceSeries[0]
          : interestSeries[interestSeries.length - 1];

      TheMaxEqBar = Math.round(Number(TheMaxEqBar) / 5000) * 5000;

      return {
        grid: { containLabel: true, left: 0 },
        legend: {
          bottom: 0,
          data: ['Principal', 'Interest', 'Balance'],
        },
        series: [
          {
            data: principalSeries,
            name: 'Principal',
            tooltip: {
              valueFormatter: function (value) {
                return '$' + Number(value.toFixed(2)).toLocaleString('en');
              },
            },
            type: 'line',
          },
          {
            data: interestSeries,
            markLine: {
              data: [
                {
                  label: {
                    borderColor: 'black',
                    borderRadius: 4,
                    borderType: 'solid',
                    borderWidth: 0,
                    formatter: 'N1*',
                    padding: 8,
                  },
                  name: 'Principal',
                  symbol: 'none',
                  xAxis: monthSeries[balanceIndex + 1] ?? 0,
                },
              ],
            },
            name: 'Interest',
            tooltip: {
              valueFormatter: function (value) {
                return '$' + Number(value.toFixed(2)).toLocaleString('en');
              },
            },
            type: 'line',
          },
          {
            data: balanceSeries,
            markLine: {
              data: [
                {
                  label: {
                    borderColor: 'black',
                    borderRadius: 4,
                    borderType: 'solid',
                    borderWidth: 0,
                    formatter: 'N2*',
                    padding: 8,
                  },
                  name: 'Balance',
                  symbol: 'none',
                  xAxis: monthSeries[principalIndex + 1] ?? 0,
                },
              ],
            },
            name: 'Balance',
            tooltip: {
              valueFormatter: function (value) {
                return '$' + Number(value.toFixed(2)).toLocaleString('en');
              },
            },
            type: 'line',
            yAxisIndex: 1,
          },
        ],
        tooltip: {
          axisPointer: {
            type: 'cross',
          },
          trigger: 'axis',
        },
        xAxis: [
          {
            axisPointer: {
              type: 'shadow',
            },
            data: monthSeries,
            type: 'category',
          },
        ],
        yAxis: [
          {
            max: TheMaxEqBar,
            min: 0,
            name: 'Interest',
            splitLine: { show: false },
            type: 'value',
          },
          {
            max: TheMaxEqBar,
            min: 0,
            name: 'Balance',
            splitLine: { show: false },
            type: 'value',
          },
        ],
      };
    } else {
      return {};
    }
  }, [itemsPrepared, interestSeries, principalSeries, balanceSeries, monthSeries]);

  return (
    <>
      {itemsPrepared && (
        <>
          <ReactECharts
            className="chart-body mt-4 z-[1]"
            key="performance"
            notMerge={true}
            option={topChartOptions as EChartsOption}
            style={{ height: '325px', overflow: 'hidden', width: '100%' }}
          />
          <div className="italic">
            <p>
              <small>
                <b>N1*: </b>Balance drops as Interest is paid.
              </small>
            </p>
            <p>
              <small>
                <b>N2*: </b>Balance drops as principal is paid.
              </small>
            </p>
            <p>
              <small>
                <b>Disclaimer: </b>This data is an estimate and may not reflect the terms of your individual mortgage.
              </small>
            </p>
          </div>

          {/* <table className="table-auto table">
            <thead>
              <tr>
                <td>Sch</td>
                <td>Interest</td>
                <td>Principal</td>
                <td>Balance</td>
              </tr>
            </thead>

            <tbody>
              {InterestSeries &&
                InterestSeries.map((interest, i) =>
                  interest ? (
                    <tr key={`quote-${i}`}>
                      <td>{monthSeries[i]}</td>
                      <td>{InterestSeries[i].toFixed(2)}</td>
                      <td>{PrincipalSeries[i].toFixed(2)}</td>
                      <td>{balanceSeries[i].toFixed(2)}</td>
                    </tr>
                  ) : null,
                )}
            </tbody>
          </table> */}
        </>
      )}
    </>
  );
};
