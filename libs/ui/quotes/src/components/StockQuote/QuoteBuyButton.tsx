import React from 'react';
import { Button, ButtonSize, ButtonVariant } from '@benzinga/core-ui';
import i18n from '@benzinga/translate';
import { useTranslation } from 'react-i18next';

interface Props {
  link?: string;
  size?: ButtonSize;
  symbol?: string;
  onClick?: (e: Event | React.MouseEvent<HTMLButtonElement>, id: string) => void;
  variant?: ButtonVariant;
}

export const QuoteBuyButton: React.FC<Props> = ({
  link = 'https://www.benzinga.com/go/start-investing/?pl=buy',
  onClick,
  size,
  variant,
}) => {
  const { t } = useTranslation('common', { i18n });

  const handleClick = (event: Event | React.MouseEvent<HTMLButtonElement>) => {
    onClick && onClick(event, 'Quote Buy Button');
  };

  return (
    <Button
      as="a"
      className="buy-button block"
      href={link}
      layout="full"
      onClick={handleClick}
      rel="noreferrer"
      size={size}
      target="_blank"
      variant={variant || 'success'}
    >
      {t('Buttons.trade-with-public')}
    </Button>
  );
};

export default QuoteBuyButton;
