'use client';
import React from 'react';
import styled, { useTheme } from '@benzinga/themetron';
import { DelayedQuote, QuotesManager, AffiliateLinks } from '@benzinga/quotes-manager';
import { Ticker } from '@benzinga/ticker-ui';
import { getColorByValue } from '@benzinga/frontend-utils';
import { SessionContext } from '@benzinga/session-context';
import { ScreenerTable } from './ScreenerTable';
import { LayoutDividend, LayoutDefault, LayoutStockAlert } from './PortfolioQuoteTable/Index';
import { Dividend, TradeAlertsData } from '@benzinga/calendar-manager';
import { TradeAlertVideos } from '@benzinga/plus-manager';
import { RankingQuotesTable, RankingTable } from './RankingQuotesTable';

export interface StockQuotesTableProps {
  affiliateLinks?: AffiliateLinks;
  bypassPaywall?: boolean;
  key?: string;
  quotes?: Record<string, DelayedQuote> | null;
  initialDividends?: Dividend[];
  symbols?: string[];
  title?: string;
  layout?: string;
  paywall_label?: string;
  tradeAlertsData?: TradeAlertsData[];
  tradeAlertVideos?: TradeAlertVideos;
  rankingTable?: RankingTable;
}

interface StockQuotesRowProps {
  affiliateLink?: string;
  quote?: DelayedQuote;
}

const StockQuoteRow: React.FC<StockQuotesRowProps> = ({ affiliateLink: initialAffiliateLink, quote }) => {
  const theme = useTheme();

  if (!quote) return null;

  const affiliateLink = initialAffiliateLink || 'https://www.benzinga.com/go/webull-how-to-buy/';
  return (
    <tr>
      <td className="ticker" colSpan={1} title={quote.symbol}>
        <Ticker
          hideChange={true}
          symbol={quote.symbol}
          targetElement={
            <a href={`https://www.benzinga.com/quote/${quote.symbol}`} rel="noreferrer" target="_blank">
              {quote.symbol}
            </a>
          }
        />
      </td>
      <td className="company" colSpan={3} title={quote.name}>
        <Ticker
          hideChange={true}
          symbol={quote.symbol}
          targetElement={
            <a href={`https://www.benzinga.com/quote/${quote.symbol}`} rel="noreferrer" target="_blank">
              {quote.name}
            </a>
          }
        />
      </td>
      <td
        className="change-percent"
        colSpan={1}
        style={{ color: getColorByValue(theme, quote.changePercent) }}
        title={quote.changePercent?.toString()}
      >
        {quote.changePercent ?? '– '}%
      </td>
      <td className="price" colSpan={1} title={quote.lastTradePrice?.toString()}>
        ${quote.lastTradePrice?.toFixed(2) ?? ' –'}
      </td>
      {/* <td className="dividend-yield" colSpan={2} title="">
        N/A
      </td> */}
      <td className="invest" colSpan={1} title="Invest">
        <a href={affiliateLink} rel="noreferrer" target="_blank">
          Buy stock
        </a>
      </td>
    </tr>
  );
};

export const StockQuotesTable: React.FC<StockQuotesTableProps> = ({
  affiliateLinks = {},
  bypassPaywall = false,
  initialDividends,
  layout = '',
  paywall_label = '',
  quotes: initialQuotes,
  rankingTable,
  symbols,
  tradeAlertsData,
  tradeAlertVideos,
}) => {
  const session = React.useContext(SessionContext);
  const quoteManager = session.getManager(QuotesManager);
  const [quotes, setQuotes] = React.useState<DelayedQuote[]>(() => {
    if (!initialQuotes) return [];
    return Object.values(initialQuotes);
  });

  React.useEffect(() => {
    if ((Array.isArray(symbols) && !symbols.length) || !symbols || initialQuotes) return;
    quoteManager.getDelayedQuotes(symbols).then(res => {
      if (res?.ok) {
        const _quotes = Object.values(res?.ok).filter(quote => quote?.symbol);
        setQuotes(_quotes);
      }
    });
  }, [quoteManager, symbols, initialQuotes]);

  return (
    <>
      {(() => {
        if (layout === 'screener') {
          return <ScreenerTable bypassPaywall={bypassPaywall} quotes={quotes} />;
        } else if (layout === 'rankings') {
          return <RankingQuotesTable bypassPaywall={bypassPaywall} table={rankingTable} />;
        } else if (layout === 'portfolio_default') {
          return (
            <LayoutDefault
              bypassPaywall={bypassPaywall}
              paywall_label={paywall_label}
              quotes={quotes}
              symbols={symbols}
            />
          );
        } else if (layout === 'portfolio_dividend') {
          return (
            <LayoutDividend
              bypassPaywall={bypassPaywall}
              initialDividends={initialDividends}
              paywall_label={paywall_label}
              quotes={quotes}
              symbols={symbols}
            />
          );
        } else if (layout == 'portfolio_alert') {
          return (
            <LayoutStockAlert
              bypassPaywall={bypassPaywall}
              paywall_label={paywall_label}
              quotes={quotes}
              symbols={symbols}
              tradeAlertsData={tradeAlertsData}
              tradeAlertVideos={tradeAlertVideos}
            />
          );
        } else {
          return (
            <Container className="stock-quotes-table-container">
              <table>
                <thead>
                  <tr>
                    <th colSpan={1}>Ticker</th>
                    <th colSpan={3}>Company</th>
                    <th colSpan={1}>±%</th>
                    <th colSpan={1}>Price</th>
                    {/* <th colSpan={2}>Dividend Yield</th> */}
                    <th colSpan={1}>Invest</th>
                  </tr>
                </thead>
                <tbody>
                  {quotes &&
                    quotes.map((quote, i) =>
                      quote ? (
                        <StockQuoteRow affiliateLink={affiliateLinks[quote.symbol]} key={`quote-${i}`} quote={quote} />
                      ) : null,
                    )}
                </tbody>
              </table>
              <div className="bz-pro-action">
                <a href="https://www.benzinga.com/go/benzinga-pro" rel="noreferrer" target="_blank">
                  Start Your Free 14-Day Benzinga Pro Trial
                </a>
              </div>
            </Container>
          );
        }
      })()}
    </>
  );
};

const Container = styled.div`
  &.stock-quotes-table-container {
    width: 100%;
    display: block;
    .bz-pro-action {
      font-size: 15px;
      font-weight: 500;
    }

    table {
      width: 100%;
      margin-bottom: 0.2rem;
      table-layout: fixed;
      display: table;
      text-align: left;
      font-size: 0.875rem;
      border: 1px solid #ceddf2;
    }

    thead {
      display: table;
      table-layout: fixed;
      width: 100%;
      color: ${({ theme }) => theme.colorPalette.black};
      /* border-bottom: 2px solid ${({ theme }) => theme.colors.border}; */
      font-size: 0.875rem;

      tr {
        text-align: left;
        border-collapse: collapse;
        color: ${({ theme }) => theme.colorPalette.white};
        background-color: ${({ theme }) => theme.colorPalette.blue500};
        th {
          padding: 4px 8px;
        }
        th:last-of-type {
          text-align: right;
        }
      }
    }

    tbody {
      overflow: auto;
      display: block;
      width: 100%;
      font-size: 0.8rem;

      tr {
        border-collapse: collapse;
        box-sizing: inherit;
        display: table;
        table-layout: fixed;
        width: 100%;
        /* border-bottom: 1px solid ${({ theme }) => theme.colors.border}; */
        font-size: 0.875rem;
        &:nth-child(odd) {
          background: #f9f9f9;
        }

        td {
          padding: 4px 8px;
          color: ${({ theme }) => theme.colorPalette.black};

          &.ticker {
            font-weight: ${({ theme }) => theme.fontWeight.semibold};
          }

          &.company {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-weight: ${({ theme }) => theme.fontWeight.semibold};
          }

          &.change-percent {
            font-weight: ${({ theme }) => theme.fontWeight.bold};
          }

          a {
            color: #2ca2d1;
          }

          &:last-of-type {
            text-align: right;
            font-weight: ${({ theme }) => theme.fontWeight.semibold};
          }
        }
      }
    }
  }
`;
