import React from 'react';
import styled from '@benzinga/themetron';
import { Rate } from '@benzinga/core-ui';
import { SmartLink } from '@benzinga/analytics';
import { Product } from '@benzinga/content-manager';
import { ProductFeatures } from '../Product/ProductFeatures';
import { ProductGetStarted } from '../Product/ProductGetStarted';
import { BzImage } from '@benzinga/image';
import { sanitizeHTML } from '@benzinga/frontend-utils';

interface ProductCardOptions {
  cta_button_text?: string;
  button_text?: string;
  hide_attributes?: boolean;
  layout?: string;
  product?: boolean;
  product_link_type?: string;
  show_best_for?: boolean | string;
  show_commissions?: boolean | string;
  show_company_name?: boolean | string;
  show_cons?: boolean | string;
  show_desc?: boolean | string;
  show_pros?: boolean | string;
  show_review?: boolean | string;
  show_star_rating?: boolean | string;
}

export interface MoneyProductCardProps {
  options?: ProductCardOptions;
  product: Product;
  review_label?: string;
  variant?: 'default' | 'detailed' | 'header';
  is_braded_review?: boolean;
}

// const ProductCardButton = ({ buttonText, link }) => {
//   return (
//     <SmartLink className="cta-link-button" href={link} label="Product Card CTA">
//       <Button variant="cta">
//         {buttonText ? (
//           buttonText
//         ) : (
//           <>
//             Open an Account <Icon icon={faLock} />
//           </>
//         )}
//       </Button>
//     </SmartLink>
//   );
// };

export const ProductRating: React.FC<MoneyProductCardProps> = ({ product }) => {
  if ((product.rating ?? 0) > 0) {
    return <Rate allowHalf className="rating-component" readOnly={true} value={product.rating} />;
  } else {
    return null;
  }
};

const isFalse = (value: boolean | string | number | undefined) => {
  return value === 0 || value === false || value === '0' || value === undefined;
};

export const MoneyProductCard: React.FC<MoneyProductCardProps> = ({
  is_braded_review = false,
  options,
  product,
  review_label = 'Overall Rating:',
  variant = 'default',
}) => {
  const showSingleItem = isFalse(options?.show_best_for) && isFalse(options?.show_pros) && isFalse(options?.show_cons);
  const detailedVariant = variant === 'detailed';
  const buttonText = product.button_text || options?.cta_button_text || options?.button_text;
  return (
    <Container className={`money-product-card ${variant}`}>
      <>
        {detailedVariant && (
          <SmartLink dofollow={product?.go_link_dofollow} href={product.link}>
            <div className="top-section">
              {product.name && (
                <h4 className="product-card-title" dangerouslySetInnerHTML={{ __html: sanitizeHTML(product.name) }} />
              )}
              {product.subtitle && (
                <div className="product-card-subtitle" dangerouslySetInnerHTML={{ __html: product.subtitle }} />
              )}
              <ProductRating product={product} />
              <div className="product-card-price-image">
                <BzImage alt={product.name} height={30} src={product.image} width={75} />
                {product.price && <div className="product-card-price">{product.price}</div>}
              </div>
            </div>
          </SmartLink>
        )}
        {variant === 'default' && (
          <>
            <div className="top-section">
              <SmartLink dofollow={product?.go_link_dofollow} href={product.link} label="Product Card Image">
                {product.image ? (
                  <BzImage alt={product.name} height={75} src={product.image} width={100} />
                ) : (
                  product.name
                )}
              </SmartLink>
              <ProductRating product={product} />
              {product.review && !isFalse(options?.show_review) ? <a href={product.review}>READ REVIEW</a> : null}
            </div>
            {showSingleItem && product.best_for && (
              <div className="single-item-view">
                <ProductFeatures className="best-for" features={[product.best_for]} type="best-for" />
              </div>
            )}
            {!isFalse(options?.show_desc) && product.details?.desc?.length ? (
              <div className="desc" dangerouslySetInnerHTML={{ __html: product.details.desc }} />
            ) : null}
            {!isFalse(options?.show_best_for) && product.details?.best_for?.length ? (
              <ProductFeatures className="best-for" features={product.details.best_for} type="best-for" />
            ) : null}
            {!isFalse(options?.show_pros) && product.details?.pros?.length ? (
              <ProductFeatures className="pros" features={product.details.pros} type="pros" />
            ) : null}
            {!isFalse(options?.show_cons) && product.details?.cons?.length ? (
              <ProductFeatures className="cons" features={product.details.cons} type="cons" />
            ) : null}
          </>
        )}
        {variant === 'header' && (
          <div className="review-wrapper">
            <div className="flex items-center justify-between review-header">
              <div>
                <SmartLink dofollow={product?.go_link_dofollow} href={product.link} label="Product Card Image">
                  {product.image && <BzImage alt={product.name} height={75} src={product.image} width={100} />}
                </SmartLink>
              </div>
              <div className="product-info">
                {is_braded_review ? (
                  <div className="product-promo">
                    <h4>{product.name}</h4>
                    <p>{product.current_promotion}</p>
                  </div>
                ) : (
                  <>
                    <div className={product.image ? 'has-thumbnail' : 'no-thumbnail'}>
                      <SmartLink dofollow={product?.go_link_dofollow} href={product.link} label="Product Card Title">
                        {product.name}
                      </SmartLink>
                    </div>
                    <div className="flex items-center">
                      <span>{review_label}</span> <ProductRating product={product} />
                    </div>
                  </>
                )}
              </div>
              <div className="review-cta">
                <ProductGetStarted buttonText={buttonText} label="Product Card CTA" product={product} />
              </div>
            </div>
          </div>
        )}
      </>
      {variant !== 'header' && (
        <div className="bottom-section">
          <ProductGetStarted buttonText={buttonText} label="Product Card CTA" product={product} />
        </div>
      )}
    </Container>
  );
};

const Container = styled.div`
  &.money-product-card {
    background-color: ${({ theme }) => theme.colorPalette.white};
    border: solid 1px ${({ theme }) => theme.colors.border};
    border-radius: ${({ theme }) => theme.borderRadius.md};
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    max-width: 350px;
    min-width: 300px;
    font-size: 14px;

    &.default {
      .top-section {
        align-items: center;
        padding: 1rem;
      }
    }

    &.detailed {
      .top-section {
        padding: 1rem;
      }
      .bottom-section {
        border-top: none;
        padding: 0 14px 14px;
      }
    }
    &.header {
      color: #5b7292;
      max-width: 100%;
      border: 0;
      margin-top: 2rem;
      .review-wrapper {
        margin-bottom: -20px;
        background: white;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        .review-header {
          margin-bottom: -20px;
          background: white;
          border-bottom: 1px solid #e1ebfa;
          margin: 1rem 2rem 0;
          > div {
            padding: 0 1.5rem;
          }
          .product-info {
            flex: 1 1 auto;
            a {
              color: #192940;
              font-size: 16px;
            }
            .rate-container {
              height: auto;
              margin-left: 5px;
            }
            .product-promo {
              h4 {
                font-size: 1.2rem;
              }
              p {
                font-size: 14px;
              }
            }
          }
          @media (max-width: 576px) {
            flex-direction: column;
            text-align: center;
            margin-top: 0.5rem;
            padding-bottom: 12px;
            .product-info {
              margin-bottom: 8px;
              a {
                font-size: 22px;
              }
              .has-thumbnail {
                display: none;
              }
            }
          }
        }
      }
    }

    &:hover {
      box-shadow: ${({ theme }) => theme.shadow.lg};
    }

    .top-section {
      display: flex;
      flex-direction: column;

      img {
        width: 200px;
      }
      a {
        color: #2ca2d1;
        font-size: 0.9rem;
        font-weight: bold;
      }
    }

    h4 {
      font-size: ${({ theme }) => theme.fontSize.sm};
      margin-bottom: 0 !important;
      font-weight: ${({ theme }) => theme.fontWeight.semibold};
    }

    .desc {
      padding: 0 1rem;
      text-align: left;
      p {
        margin-bottom: 0.5rem;
      }
    }

    .product-card-title {
      font-size: ${({ theme }) => theme.fontSize.lg};
      font-weight: 700;
      margin-top: 0;
      color: #000;
    }

    .product-card-subtitle {
      margin: 8px 0;
      font-size: ${({ theme }) => theme.fontSize.base};
      color: ${({ theme }) => theme.colorPalette.gray600};
    }

    .product-card-price-image {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 5px;
    }

    .product-card-price {
      font-size: ${({ theme }) => theme.fontSize['2xl']};
    }

    .single-item-view {
      ul {
        list-style-type: none;
        margin-left: 0;
        margin-bottom: 0;
      }
    }

    .product-features {
      padding: 10px 14px;
      border-top: solid 1px ${({ theme }) => theme.colors.border};
    }

    .bottom-section {
      border-top: solid 1px ${({ theme }) => theme.colors.border};
      padding: 14px;
    }

    .best-for-span {
      font-size: ${({ theme }) => theme.fontSize.lg};
    }

    .cta-link-button {
      > button {
        text-transform: uppercase;
        width: 100%;
      }
    }
  }
`;
