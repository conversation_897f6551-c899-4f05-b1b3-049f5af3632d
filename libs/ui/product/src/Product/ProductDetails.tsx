import React from 'react';

import styled from '@benzinga/themetron';
import { Product } from '@benzinga/content-manager';
import { BzImage } from '@benzinga/image';
import { Button } from '@benzinga/core-ui';
import { ProductFeatures } from '../Product/ProductFeatures';
import { sanitizeHTML } from '@benzinga/frontend-utils';

interface ProductDetailsProps {
  product: Product;
  force_show_review?: boolean;
  is_branded_review?: boolean;
  hide_leave_review?: boolean;
  pros_title?: string;
  cons_title?: string;
}

export const ProductDetails: React.FC<ProductDetailsProps> = ({
  cons_title = '',
  force_show_review = false,
  hide_leave_review = false,
  is_branded_review,
  product,
  pros_title = '',
}) => {
  return (
    <ProductDetailsWrapper className="product-more-details">
      {product.disclosure && (
        <span className="disclosure" dangerouslySetInnerHTML={{ __html: sanitizeHTML(product.disclosure) }} />
      )}
      {force_show_review && product.details?.desc ? (
        <div className="product-details-description">
          <div dangerouslySetInnerHTML={{ __html: product.details.desc }} />
          {product.review && (
            <div className="review-button-wrapper">
              {is_branded_review ? (
                <>
                  <Button as="a" href={`${product.review}`} variant="flat-light-blue">
                    Read Our {product.name} Review
                  </Button>
                </>
              ) : (
                <>
                  {!hide_leave_review ? (
                    <Button as="a" href={`${product.review}#one-minute-opinion`} variant="flat-light-blue">
                      Leave Your Own 1 Minute Review!
                    </Button>
                  ) : null}
                </>
              )}
            </div>
          )}
        </div>
      ) : null}
      {product?.product_badge_image && (
        <div className="product-badge">
          <BzImage alt={product.name} height={200} src={product.product_badge_image} width={200} />
        </div>
      )}
      <div className="product-features-section">
        {is_branded_review ? (
          <div className="product-highlights pt-0">
            <h3 className="pb-2">{product.details?.highlight_label}</h3>
            {!!product.details?.highlights?.length && (
              <ProductFeatures features={product.details.highlights} hideTitle={true} type="pros" />
            )}
          </div>
        ) : (
          <>
            {!!product.details?.pros?.length && (
              <ProductFeatures customTitle={pros_title} features={product.details.pros} type="pros" />
            )}
            {!!product.details?.cons?.length && (
              <ProductFeatures customTitle={cons_title} features={product.details.cons} type="cons" />
            )}
            {!!product.details?.custom_sec?.length && (
              <ProductFeatures features={product.details.custom_sec} type="details" />
            )}
          </>
        )}
      </div>
    </ProductDetailsWrapper>
  );
};

const ProductDetailsWrapper = styled.div`
  .section-title {
    margin-top: 0;
    margin-bottom: 0.35rem;
  }
  .disclosure {
    color: rgb(91, 114, 146);
    line-height: 1.375;
    font-weight: 500;
    font-size: 0.75rem;
    line-height: 1rem;
    display: block;
    margin: 0.5rem 0;
    border-bottom: 1px solid #e1ebfa;
    padding: 5px 20px 10px;
    margin: 0;
  }
  .product-details-description {
    ul {
      font-size: 1.1rem;
      list-style: disc;
      margin-left: 1.1rem;
      margin-bottom: 1.5rem;
      li {
        margin-bottom: 0.35rem;
      }
    }
  }
  .product-badge {
    padding: 1.25rem;
    border-bottom: 1px solid #e1ebfa;
  }
  .leave-review {
    font-size: 1rem;
    font-weight: bold;
    color: ${({ theme }) => theme.colorPalette.blue600};
    &:hover {
      color: ${({ theme }) => theme.colorPalette.blue800};
    }
  }
  > div {
    display: flex;
    flex-direction: column;
    background-color: ${({ theme }) => theme.colorPalette.white};

    &:first-of-type {
      margin: 1.25rem;
      border-bottom-width: 1px;
      border-color: #e1ebfa;
      padding-bottom: 1.25rem;

      h2 {
        font-size: 1.25rem;
        line-height: 1.75rem;
        font-weight: 700;
      }

      p {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 1rem;
        text-rendering: optimizeLegibility;
      }
    }

    &.product-features-section {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      width: 100%;
      border-bottom: none;
      margin: 0;

      > div {
        padding: 0.875rem;
        flex-grow: 0;
        flex-shrink: 0;
        flex: 1 1 auto;
        min-width: 0;
        width: 100%;

        h4 {
          display: inline-block;
          font-weight: 700;
          font-size: 1rem;
          line-height: 1.5rem;
          margin-bottom: 0.5rem;
        }

        @media (min-width: 640px) {
          padding: 1.25rem;
          width: 33.333333%;
          border-right: 1px solid ${({ theme }) => theme.colors.border};

          &:last-of-type {
            border-right: none;
          }
        }
      }
      .product-highlights {
        padding-top: 0;
      }
    }
  }
`;
