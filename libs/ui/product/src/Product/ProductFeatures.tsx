import React from 'react';
import { SectionTitle } from '@benzinga/core-ui';
import styled from '@benzinga/themetron';
import classNames from 'classnames';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { ProductCustomSection } from '@benzinga/content-manager';

export interface ProductFeaturesProps {
  className?: string;
  customTitle?: string;
  type: 'best-for' | 'cons' | 'pros' | 'details';
  features: string[] | ProductCustomSection[];
  hideTitle?: boolean;
  enableToggle?: boolean;
}

export const ProductFeatures: React.FC<ProductFeaturesProps> = ({
  className,
  customTitle = '',
  enableToggle = false,
  features,
  hideTitle = false,
  type,
}) => {
  let title = '';
  if (!hideTitle) {
    if (customTitle != '') {
      title = customTitle;
    } else {
      switch (type) {
        case 'best-for':
          title = 'Best For';
          break;
        case 'cons':
          title = 'Cons';
          break;
        case 'pros':
          title = 'Pros';
          break;
        case 'details':
          title = 'Details';
          break;
      }
    }
  }
  const [toggleStatus, setToggleStatus] = React.useState<boolean>(!enableToggle);

  return (
    <ProductFeaturesListWrapper
      className={classNames('product-features', { [`${className}`]: !!className })}
      toggleStatus={toggleStatus}
    >
      {title && <SectionTitle level={0}>{title}</SectionTitle>}
      <ul className={`product-feature-list ${type} ${enableToggle ? 'enable-toggle' : ''}`}>
        {type !== 'details'
          ? features?.map((feature, index) => (
              <li dangerouslySetInnerHTML={{ __html: sanitizeHTML(feature) }} key={index} />
            ))
          : features?.map(feature => (
              <li key={feature.label}>
                {feature.label}: <span dangerouslySetInnerHTML={{ __html: sanitizeHTML(feature.value) }} />{' '}
              </li>
            ))}
      </ul>
      {features && features.length > 0 && enableToggle && (
        <span className="toggle" onClick={() => setToggleStatus(!toggleStatus)}>
          Show {!toggleStatus ? 'More' : 'Less'}
        </span>
      )}
    </ProductFeaturesListWrapper>
  );
};

const ProductFeaturesListWrapper = styled.div<{ toggleStatus }>`
  .section-title {
    margin-top: 0;
    margin-bottom: 0.35rem;
  }
  .product-feature-list {
    list-style: disc;
    margin-left: 18px;
    margin-bottom: 4px;
    li {
      margin-bottom: 5px;
      &:before {
        font-weight: 700;
        margin: 0 5px 0 -16px;
      }
      &:last-of-type {
        margin-bottom: 0;
      }
    }
    &.enable-toggle {
      li {
        &:nth-child(n + 2) {
          display: ${props => (props.toggleStatus ? 'block' : 'none')};
        }
      }
    }
    &.pros {
      list-style: none;
      li::before {
        content: '✓';
        color: #3adb76;
      }
    }

    &.cons {
      list-style: none;
      li::before {
        content: '✕';
        color: #cc4b37;
      }
    }
  }
  .toggle {
    cursor: pointer;
    text-align: right;
    display: block;
    font-size: 14px;
  }
`;
