'use client';

import React from 'react';
import classNames from 'classnames';

export interface AvatarProps {
  id?: number;
  name?: string;
  photo?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  textSize?: 'xs' | 'sm' | 'md' | 'lg';
  className?: string;
  imageClassName?: string;
  pixels?: number;
}

const photoSizes = {
  lg: 48,
  md: 40,
  sm: 32,
  xs: 24,
};

export const Avatar: React.FC<AvatarProps> = ({
  className,
  imageClassName,
  name,
  photo,
  pixels,
  size = 'md',
  textSize = 'sm',
}) => {
  if (photo || name) {
    let picSize = 32;
    if (size) {
      picSize = photoSizes[size];
    }
    if (pixels) {
      picSize = pixels;
    }

    return (
      <div className={classNames('inline-flex items-center space-x-2', { [`${className}`]: !!className })}>
        {photo || name ? (
          <div
            className={classNames(`font-bold inline-block text-gray-700 ${imageClassName}`, {
              'text-base': textSize === 'md',
              'text-lg': textSize === 'lg',
              'text-sm': textSize === 'sm',
              'text-xs': textSize === 'xs',
            })}
            style={{
              backgroundImage: photo
                ? `url(${photo})`
                : `url(https://ui-avatars.com/api/?name=${name}&background=0075cd&color=fff&size=128)`,
              backgroundPosition: 'center',
              backgroundSize: 'cover',
              height: `${picSize}px`,
              width: `${picSize}px`,
            }}
          />
        ) : null}
      </div>
    );
  }

  return null;
};
