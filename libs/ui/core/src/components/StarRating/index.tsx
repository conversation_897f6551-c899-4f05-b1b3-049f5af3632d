'use client';

import React from 'react';
import styled from '@benzinga/themetron';
import { Rate } from '../Rate';

type HeadingLevel = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

export interface StarRatingProps {
  id?: string;
  heading: string;
  headingClassname?: string;
  level: HeadingLevel;
  rating: number;
}

export const StarRating: React.FC<StarRatingProps> = ({ heading, headingClassname = '', id, level, rating }) => {
  const HeadingTag = level;
  return (
    <HeaderRatingWrapper className="header-rating" id={id}>
      <div className="header-rating-wrapper">
        <HeadingTag className={`mr-4 ${headingClassname}`}>{heading}</HeadingTag>
        <Rate allowHalf className="rating-component" readOnly={true} value={rating} />
      </div>
    </HeaderRatingWrapper>
  );
};

const HeaderRatingWrapper = styled.div`
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  .core-block {
    margin-top: 0rem !important;
    margin-bottom: 0rem !important;
  }
`;
