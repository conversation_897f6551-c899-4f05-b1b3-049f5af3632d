'use client';

import React from 'react';
import { numberRange } from '@benzinga/utils';
import classNames from 'classnames';

export interface StepNumberInputProps {
  max: number;
  min: number;
  onChange?: (value: number) => void;
  step?: number;
  value?: number;
}

export const StepNumberInput: React.FC<StepNumberInputProps> = ({ max, min, onChange, step = 1, value = 0 }) => {
  const range = React.useMemo(() => numberRange(min, max + 1, step), [min, max, step]);

  const currentPercentage = React.useMemo(() => {
    const count = range.length - 1;
    const index = range.indexOf(value);
    const res = (index / count) * 100;

    return res > 0 ? res : 0;
  }, [range, value]);

  const isSelected = React.useCallback((num: number) => num === value, [value]);

  const isActive = React.useCallback((num: number) => num < value, [value]);

  const handleChange = React.useCallback(
    (val: number) => {
      if (typeof onChange === 'function') {
        onChange(val);
      }
    },
    [onChange],
  );

  return (
    <div className="inline-flex relative">
      <div className="left-1.5 right-1.5 absolute bg-gray-200 bottom-0" mb-2 style={{ height: 2 }} z-0 />
      <div className="left-1.5 right-1.5 absolute bottom-0 mb-2 z-10">
        <div className="bg-blue-500" style={{ height: 2, width: `${currentPercentage}%` }} />
      </div>
      <div className="inline-flex justify-between relative w-full z-10">
        {range.map(num => (
          <div
            className={classNames(
              'py-0.5 bg-blue-500 cursor-pointer flex flex-col h-9 items-center rounded-full select-none text-sm w-5',
              {
                'bg-opacity-5': !isSelected(num),
                'font-bold': isSelected(num),
                'hover-bg-opacity-10': !isSelected(num),
                'ml-2': num !== min,
                'mr-2': num !== max,
                'text-gray-700': !isSelected(num),
                'text-white': isSelected(num),
              },
            )}
            key={num}
            onClick={() => handleChange(num)}
          >
            {num}
            <div
              className={classNames('h-2 rounded-full w-2', {
                'bg-blue-500': isActive(num),
                'bg-gray-300': !isSelected(num) && !isActive(num),
                'bg-white': isSelected(num),
              })}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
