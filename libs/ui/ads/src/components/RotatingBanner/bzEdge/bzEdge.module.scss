.edge-v1 {
  background-color: #0B131D;
  background-image: url('/next-assets/images/banners/edge/edge-bg.png');
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;

  .banner {
    .banner-content {
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;

      @media (max-width: 800px) {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    .logo-wrapper {
      width: 278px;
      height: 27px;
      padding-right: 1.5rem;
      @media (max-width: 800px) {
        width: 160px;
      }
    }
    h3 {
      color: white;
      font-size: 34px;
      line-height: 40px;
      @media (max-width: 1100px) {
        font-size: 24px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 18px;
        line-height: 18px;
      }
    }
  }

  .banner-bg-end {
    width: 184px;
    height: 80px;
    background-image: url('/next-assets/images/banners/edge/edge-v1-bg-end.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    @media (max-width: 800px) {
      margin-right: -50px;
    }
  }
}

.edge-v2 {
  background-color: #0B131D;
  background-image: url('/next-assets/images/banners/edge/edge-bg.png');
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;

  .banner {
    .banner-content {
      flex-direction: row-reverse;
      align-items: center;
      justify-content: space-between;
      gap: 2rem;
      width: 100%;
      @media (max-width: 1000px) {
        flex-direction: column-reverse;
        align-items: flex-start;
        gap: 0.5rem;
      }
    }

    .content {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 700;
      @media (max-width: 800px) {
        flex-direction: column;
        gap: 0;
      }
    }
    .logo-wrapper {
      width: 254px;
      height: 24px;
      @media (max-width: 800px) {
        width: 160px;
      }
    }
    h3 {
      color: white;
      font-size: 34px;
      line-height: 40px;
      @media (max-width: 1325px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1124px) {
        font-size: 24px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 18px;
        line-height: 18px;
      }
    }
    p {
      font-size: 24px;
      line-height: 40px;
      @media (max-width: 1325px) {
        font-size: 20px;
        line-height: 22px;
      }
      @media (max-width: 1124px) {
        font-size: 18px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 16px;
        margin-right: 2rem;
      }
    }
  }
}

.edge-v3 {
  background-color: #0B131D;
  background-image: url('/next-assets/images/banners/edge/edge-bg.png');
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;

  .banner {
    .banner-content {
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      @media (max-width: 800px) {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    .content {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 700;
    }
    .logo-wrapper {
      width: 278px;
      height: 27px;
      padding-right: 1.5rem;
      @media (max-width: 800px) {
        width: 140px;
        height: 20px;
      }
    }
    h3 {
      color: white;
      font-size: 34px;
      line-height: 40px;
      @media (max-width: 1325px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1100px) {
        font-size: 24px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 18px;
        line-height: 18px;
      }
    }
    .button-wrapper {
      align-items: center;
      justify-content: center;

      .banner-button {
        font-size: 18px;
        background-color: #3F83F8;
        &:hover {
          background-color:rgba(63, 131, 248, 0.8)
        }
        @media (max-width: 800px) {
          font-size: 14px
        }
      }
    }
  }

  .banner-bg-end {
    width: 461px;
    height: 80px;
    background-image: url('/next-assets/images/banners/edge/edge-v3-bg-end.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.edge-v4 {
  background-color: #0B131D;
  background-image: url('/next-assets/images/banners/edge/edge-bg.png');
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;

  .banner {
    .banner-content {
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      @media (max-width: 800px) {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    .content {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 700;
    }
    .logo-wrapper {
      width: 278px;
      height: 27px;
      padding-right: 1.5rem;
      @media (max-width: 800px) {
        width: 160px;
      }
    }
    h3 {
      color: white;
      font-size: 34px;
      line-height: 40px;
      @media (max-width: 1325px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1100px) {
        font-size: 24px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 18px;
        line-height: 18px;
      }
    }
    .button-wrapper {
      align-items: center;
      justify-content: center;

      .banner-button {
        background-color: #3F83F8;
        font-size: 18px;
        &:hover {
          background-color:rgba(63, 131, 248, 0.8)
        }

        @media (max-width: 800px) {
          font-size: 14px
        }
      }
    }
  }

  .banner-bg-end {
    width: 234px;
    height: 80px;
    background-image: url('/next-assets/images/banners/edge/edge-v4-bg-end.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    @media (max-width: 800px) {
      margin-right: -100px;
    }
  }
}

// light version used for v6, v7, v8
.edge-v6 {
  background-color: white;

  .banner {
    .banner-content {
      color: black;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;

      @media (max-width: 800px) {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    .logo-wrapper {
      width: 178px;
      height: 27px;
      padding-right: 1.5rem;
      svg {
        fill: black;
      }
      @media (max-width: 800px) {
        width: 100px;
        height: 13px;
        margin-bottom: 0.5rem;
      }
    }

    h3 {
      color: black;
      font-size: 24px;
      line-height: 28px;
      @media (max-width: 1100px) {
        font-size: 20px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 15px;
        line-height: 18px;
        margin-top: 0.25rem;
      }
    }

    p {
      font-size: 15px;
      @media (max-width: 1100px) {
        font-size: 12px;
      }
      @media (max-width: 800px) {
        display: none;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;

      .banner-button {
        background-color: #3F83F8;
        font-size: 18px;
        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem;
          font-size: 12px
        }
      }
    }
  }

  .banner-bg-start {
    width: 131px;
    height: 80px;
    background-image: url('/next-assets/images/banners/edge/edge-light-bg-start.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
  }

  .banner-bg-end {
    width: 497px;
    height: 80px;
    background-image: url('/next-assets/images/banners/edge/edge-light-bg-end.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    @media (max-width: 800px) {
      margin-right: -100px;
    }
  }
}

.edge-black-friday-v1 {
  background: linear-gradient(0deg, #020D1D, #020D1D), linear-gradient(270deg, #ED5735 7.7%, rgba(2, 13, 29, 0) 51.79%);
  border-left: 6px solid #ED5735;

  .banner {
    padding-right: 0 !important;
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    h3 {
      color: white;
      font-size: 33px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      @media (max-width: 1327px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1000px) {
        font-size: 24px;
        line-height: 26px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 20px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      margin-left: 200px;
      padding-right: 3px;

      .banner-button {
        background-color: #ED5735;
        font-size: 24px;
        white-space: nowrap;
        height: 100%;
        border-radius: 0px !important;

        &:hover {
          background-color:#ED5735;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 14px
        }
      }
      .banner-button-bg {
        background-image: url('/next-assets/images/banners/edge/black-friday.png');
        position: absolute;
        background-size: contain;
        width: 533px;
        height: 80px;
        top: 0;
        left: -500px;
        background-position: right;
      }
      @media(max-width: 1416px) {
        padding-right: 0px;
      }
      @media (max-width: 1280px) {
       margin-left: 4rem;
       .banner-button-bg {
        left: -433px;
       }
      }
      @media (max-width: 800px) {
        margin-left: 2rem;
        .banner-button-bg {
          left: -400px;
        }
        .banner-button {
          white-space: wrap;
        }
      }
    }
  }
}

.edge-black-friday-v2 {
  background: linear-gradient(0deg, #020D1D, #020D1D), linear-gradient(270deg, #ED5735 7.7%, rgba(2, 13, 29, 0) 51.79%);
  border-left: 6px solid #ED5735;

  .banner {
    padding-right: 0 !important;
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    h3 {
      color: white;
      font-size: 34px;
      line-height: 32px;
      font-weight: 400;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      letter-spacing: 0.18em;

      @media (max-width: 1280px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1000px) {
        font-size: 24px;
        line-height: 26px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 20px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      margin-left: 200px;
      padding-right: 3px;

      .banner-button {
        background-color: #ED5735;
        font-size: 24px;
        white-space: nowrap;
        height: 100%;
        border-radius: 0px !important;

        &:hover {
          background-color:#ED5735;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 14px
        }
      }
      .banner-button-bg {
        background-image: url('/next-assets/images/banners/edge/black-friday.png');
        position: absolute;
        background-size: contain;
        width: 533px;
        height: 80px;
        top: 0;
        left: -500px;
        background-position: right;
      }
      @media(max-width: 1416px) {
        padding-right: 0px;
      }
      @media (max-width: 1280px) {
       margin-left: 4rem;
       .banner-button-bg {
        left: -433px;
       }
      }
      @media (max-width: 800px) {
        margin-left: 2rem;
        .banner-button-bg {
          left: -400px;
        }
        .banner-button {
          white-space: wrap;
        }
      }
    }
  }
}

.edge-cyber-monday-v1 {
  background-color: #0C0E13;
  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    h3 {
      color: white;
      font-size: 34px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      @media (max-width: 1327px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1080px) {
        font-size: 24px;
        line-height: 26px;
      }
      @media (max-width: 800px) {
        font-size: 20px;
        line-height: 20px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      margin-left: 1rem;

      .banner-button {
        background-color: #F83F3F;
        font-size: 24px;
        white-space: nowrap;
        height: 100%;
        margin: 0.5rem 0;
        border-radius: 4px !important;

        &:hover {
          background-color:#F83F3F;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 14px
        }
      }
      .banner-button-bg {
        background-image: url('/next-assets/images/banners/cyber-monday.png');
        position: absolute;
        background-size: contain;
        background-repeat: no-repeat;
        width: 225px;
        height: 80px;
        top: 0;
        left: -180px;
        background-position: right;
      }
      @media (max-width: 1280px) {
       .banner-button-bg {
        left: -160px;
       }
      }
      @media (max-width: 800px) {
        max-width: 100px;
        .banner-button-bg {
          left: -140px;
        }
        .banner-button {
          white-space: wrap;
        }
      }
    }
  }

  .banner-bg-start {
    width: 182px;
    height: 80px;
    background-image: url('/next-assets/images/banners/cyber-monday-start-bg.png');
    background-position: left;
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.edge-cyber-monday-v2 {
  background-color: #0C0E13;
  .banner {
    .content {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      gap: 1rem;
      @media (max-width: 900px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
      }
    }

    h3 {
      color: white;
      font-size: 42px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      @media (max-width: 1140px) {
        font-size: 36px;
        line-height: 28px;
      }
      @media (max-width: 1000px) {
        font-size: 30px;
        line-height: 28px;
      }
      @media (max-width: 800px) {
        font-size: 22px;
        line-height: 20px;
      }
    }

    p {
      font-family: Inter;
      font-size: 42px;
      font-weight: 900;
      line-height: 40px;
      letter-spacing: 0.1em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #F83F3F;
      @media (max-width: 1140px) {
        font-size: 36px;
        line-height: 28px;
      }
      @media (max-width: 1000px) {
        font-size: 30px;
        line-height: 28px;
      }
      @media (max-width: 800px) {
        font-size: 22px;
        line-height: 20px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      margin-left: 200px;

      .banner-button {
        background-color: #F83F3F;
        font-size: 24px;
        white-space: nowrap;
        height: 100%;
        margin: 0.5rem 0;
        border-radius: 4px !important;

        &:hover {
          background-color:#F83F3F;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 14px
        }
      }
      .banner-button-bg {
        background-image: url('/next-assets/images/banners/cyber-monday.png');
        position: absolute;
        background-size: contain;
        background-repeat: no-repeat;
        width: 225px;
        height: 80px;
        top: 0;
        left: -180px;
        background-position: right;
      }
      @media (max-width: 1280px) {
       margin-left: 2rem;
       .banner-button-bg {
        left: -160px;
       }
      }
      @media (max-width: 800px) {
        margin-left: 0rem;
        max-width: 100px;
        .banner-button-bg {
          left: -140px;
        }
        .banner-button {
          white-space: wrap;
        }
      }
    }
  }

  .banner-bg-start {
    width: 182px;
    height: 80px;
    background-image: url('/next-assets/images/banners/cyber-monday-start-bg.png');
    background-position: left;
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.edge-eoy-v1 {
  background-color: #020D1D;
  background-image: url('/next-assets/images/banners/edge/eoy-bg.png');

  .banner {
    .banner-content {
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      height: 100%;
    }

    h3 {
      color: white;
      font-size: 32px;
      line-height: 28px;
      font-weight: 800;
      font-family: Inter, sans-serif;
      @media (max-width: 1210px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1000px) {
        font-size: 18px;
        line-height: 18px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 16px;
        margin-bottom: 0.25rem;
      }
    }

    p {
      color: white;
      font-family: Inter, sans-serif;
      font-size: 16px;
      font-weight: 500;
      line-height: 20px;
      letter-spacing: -0.04em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      @media (max-width: 1210px) {
        font-size: 14px;
        line-height: 14px;
      }
      @media (max-width: 1000px) {
        font-size: 12px;
        line-height: 12px;
      }
      @media (max-width: 800px) {
        font-size: 10px;
        line-height: 10px;
      }
    }

    .sale-badge {
      font-family: Inter, sans-serif;
      font-size: 18px;
      font-weight: 800;
      line-height: 24px;
      letter-spacing: 0.44em;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      white-space: nowrap;
      text-transform: uppercase;
      height: 100%;
      padding-right: 0.5rem;
      border-right: 1px solid rgba(223, 234, 255, 0.1);
      margin-right: 1rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #DFEAFF;
      @media (max-width: 1200px) {
        font-size: 16px;
        line-height: 16px;
        white-space: wrap;
        max-width: 150px;
      }
      @media (max-width: 800px) {
        font-size: 12px;
        line-height: 12px;
        padding-right: 0.25rem;
        margin-right: 0.5rem;
        letter-spacing: 0.22em;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/edge/eoy-btn-bg.png');
        background-size: contain;
        width: 728px;
        height: 80px;
        margin-left: -700px;
        @media (max-width: 1100px) {
          margin-left: -600px;
        }
        @media (max-width: 800px) {
          margin-left: -500px;
        }
      }

      .banner-button {
        background-color: #3F83F8;
        font-size: 22px;
        white-space: nowrap;
        border-radius: 2px !important;
        text-transform: uppercase;
        height: 80%;

        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1200px) {
          font-size: 18px;
        }
        @media (max-width: 1000px) {
          font-size: 16px;
        }
        @media (max-width: 800px) {
          padding: 0.5rem 1rem;
          font-size: 12px;
          white-space: wrap;
        }

      }
    }
  }
}

.edge-eoy-v2 {
  background: linear-gradient(0deg, #000612, #000612),
linear-gradient(0deg, #000612, #000612);

  .banner {
    .banner-content, .content {
      display: flex;
      flex-direction: row-reverse;
      justify-content: center;
      align-items: center;
      height: 100%;
      gap: 1rem;
    }

    h3 {
      background: -webkit-linear-gradient(180deg, #E09D39 0%, #EAB45A 15.5%, #FEE192 33.33%, #ECB964 53%, #FEE398 74%, #CC6F1A 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 31px;
      line-height: 28px;
      font-weight: 800;
      font-family: Inter, sans-serif;
      @media (max-width: 1210px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1000px) {
        font-size: 18px;
        line-height: 18px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 16px;
        margin-bottom: 0.25rem;
      }
    }

    p {
      font-family: Inter, sans-serif;
      font-size: 18px;
      font-weight: 800;
      line-height: 24px;
      letter-spacing: 0.44em;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      white-space: nowrap;
      text-transform: uppercase;
      height: 100%;
      padding-right: 0.5rem;
      border-right: 1px solid rgba(223, 234, 255, 0.1);
      margin-right: 1rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #DFEAFF;
      @media (max-width: 1200px) {
        font-size: 16px;
        line-height: 16px;
        white-space: wrap;
        max-width: 150px;
      }
      @media (max-width: 800px) {
        font-size: 12px;
        line-height: 12px;
        padding-right: 0.25rem;
        margin-right: 0.5rem;
        letter-spacing: 0.22em;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/edge/eoy-btn-bg-2.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 551px;
        height: 80px;
        margin-left: -300px;
        @media (max-width: 800px) {
          margin-left: -100px;
        }
      }

      .banner-button {
        background-color: #3F83F8;
        font-size: 22px;
        white-space: nowrap;
        border-radius: 2px !important;
        text-transform: uppercase;
        height: 80%;
        padding: 1rem !important;

        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1200px) {
          font-size: 18px;
        }
        @media (max-width: 1000px) {
          font-size: 16px;
        }
        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 12px;
          white-space: wrap;
        }

      }
    }
  }
}

.edge-v15 {
  background: linear-gradient(180deg, #00406D 0%, #000000 100%);

  .banner {
    h3 {
      color: white;
      font-size: 32px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      padding-right: 2rem;

      @media (max-width: 1370px) {
        font-size: 28px;
        line-height: 24px;
      }
      @media (max-width: 1220px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 18px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/edge/edge-v15-btn.png');
        background-size: contain;
        background-position: right;
        width: 1270px;
        height: 80px;
        margin-left: -1270px;
        @media (max-width: 1200px) {
          margin-left: -1200px;
        }
        @media (max-width: 1100px) {
          margin-left: -1120px;
        }
        @media (max-width: 800px) {
          margin-left: -1020px;
        }
      }

      .banner-button {
        background-color: #3F83F8;
        font-size: 22px;
        white-space: nowrap;
        border-radius: 2px !important;
        font-weight: 700;
        min-height: 70%;
        text-transform: uppercase;
        font-family: Manrope, sans-serif;

        &:hover {
          background-color:#3a74da;
        }
        @media (max-width: 1370px) {
          font-size: 20px;
        }
        @media (max-width: 1220px) {
          padding: 0.5rem 1rem !important;
          font-size: 16px;
          white-space: wrap;
        }
        @media (max-width: 800px) {
          font-size: 12px;
        }
      }
    }
  }
}

.edge-v12 {
  background: linear-gradient(90deg, #003865 0%, #192940 100%);

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    .logo-wrapper {
      width: 160px;
      height: 20px;
      @media (max-width: 1100px) {
        width: 140px;
        height: 18px;
      }
      @media (max-width: 800px) {
        width: 100px;
        height: 13px;
      }
    }

    h3 {
      color: white;
      font-size: 32px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      @media (max-width: 1100px) {
        font-size: 24px;
        line-height: 26px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 22px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;

      .banner-button {
        background-color: #3F83F8;
        font-size: 22px;
        white-space: nowrap;
        border-radius: 2px !important;
        font-weight: 700;
        padding: 1.25rem !important;

        &:hover {
          background-color:#3a74da;
        }
        @media (max-width: 1200px) {
          font-size: 18px;
        }

        @media (max-width: 1000px) {
          white-space: wrap;
          width: 160px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem !important;
          font-size: 14px;
          min-width: 120px;
        }
      }
    }
  }

  .banner-bg-end {
    width: 561px;
    height: 80px;
    background-image: url('/next-assets/images/banners/edge/discover-globe.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    @media (max-width: 800px) {
      margin-right: -100px;
    }
  }
}

.edge-v13 {
  background-color: #010C1C;
  background-image: url('/next-assets/images/banners/edge/orange-gradient.png');

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    .logo-wrapper {
      width: 160px;
      height: 20px;
      @media (max-width: 1100px) {
        width: 140px;
        height: 18px;
      }
      @media (max-width: 800px) {
        width: 100px;
        height: 13px;
      }
    }

    h3 {
      color: white;
      font-size: 32px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-right: 8rem;
      @media (max-width: 1000px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 18px;
        line-height: 18px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/edge/golden-bull-2.png');
        background-size: contain;
        width: 192px;
        height: 80px;
        margin-left: -400px;
        @media (max-width: 1100px) {
          margin-left: -200px;
        }
        @media (max-width: 800px) {
          margin-left: -80px;
        }
      }

      .banner-button {
        background-color: #3F83F8;
        font-size: 22px;
        white-space: nowrap;
        border-radius: 2px !important;
        font-weight: 700;
        padding: 1.25rem 3rem !important;

        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1100px) {
          padding: 1.25rem !important;
          font-size: 18px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 14px
        }
      }
    }
  }
}

.edge-v17 {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/edge/edge-v17-bg.png');
  border-left: 4px solid  #479CFF;
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }
    .logo-wrapper {
      width: 160px;
      height: 20px;
      @media (max-width: 1100px) {
        width: 140px;
        height: 18px;
      }
      @media (max-width: 800px) {
        width: 100px;
        height: 13px;
      }
    }

    h3 {
      color: white;
      font-size: 32px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-right: 1rem;
      text-transform: uppercase;

      @media (max-width: 1200px) {
        font-size: 28px;
        line-height: 26px;
      }

      @media (max-width: 1000px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 18px;
        line-height: 18px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background-color: #479CFF;
        font-size: 22px;
        white-space: nowrap;
        border-radius: 2px !important;
        font-weight: 700;
        padding: 1.25rem 2rem !important;
        text-transform: uppercase;

        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1100px) {
          padding: 1.25rem !important;
          font-size: 18px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }
    }
  }
}

.edge-rankings-orange {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/edge/edge-rankings-bg.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;
  @media (max-width: 800px) {
    background-position: center;
  }

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    p {
      color: white;
      font-size: 32px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-right: 1rem;
      text-transform: uppercase;

      @media (max-width: 1350px) {
        font-size: 28px;
        line-height: 26px;
      }

      @media (max-width: 1200px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 18px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background: linear-gradient(90deg, #FF9344 0%, #EE6500 64.75%);
        font-size: 22px;
        white-space: nowrap;
        border-radius: 12px !important;
        font-weight: 700;
        padding: 1rem 2rem !important;
        text-transform: uppercase;

        @media (max-width: 1100px) {
          padding: 1rem !important;
          font-size: 18px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem !important;
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }
    }
  }
}

.edge-rankings-red {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/edge/edge-rankings-bg.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;
  @media (max-width: 800px) {
    background-position: center;
  }

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    p {
      color: white;
      font-size: 32px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-right: 1rem;
      text-transform: uppercase;

      @media (max-width: 1415px) {
        font-size: 28px;
        line-height: 26px;
      }

      @media (max-width: 1300px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 18px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background: linear-gradient(90deg, #F41100 0%, #980C01 76.61%);
        font-size: 18px;
        white-space: nowrap;
        border-radius: 12px !important;
        font-weight: 700;
        padding: 1rem 2rem !important;
        text-transform: uppercase;

        @media (max-width: 1100px) {
          padding: 1rem !important;
          font-size: 18px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem !important;
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }
    }
  }
}

.edge-rankings-blue {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/edge/edge-rankings-bg.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;
  @media (max-width: 800px) {
    background-position: center;
  }

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    p {
      color: white;
      font-size: 24px;
      line-height: 22px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-right: 1rem;
      text-transform: uppercase;


      @media (max-width: 1300px) {
        font-size: 18px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 18px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background: linear-gradient(90deg, #3F83F8 0%, #0443AF 100%);
        font-size: 18px;
        white-space: nowrap;
        border-radius: 12px !important;
        font-weight: 700;
        padding: 1rem 2rem !important;
        text-transform: uppercase;

        @media (max-width: 1100px) {
          padding: 1rem !important;
          font-size: 18px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem !important;
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }
    }
  }
}

.edge-v14 {
  background-color: #191C20;
  background-image: url('/next-assets/images/banners/edge/edge-v15-bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: left;

  .banner {
    h3 {
      color: white;
      font-size: 36px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: uppercase;

      @media (max-width: 1270px) {
        font-size: 32px;
        line-height: 26px;
      }
      @media (max-width: 1130px) {
        font-size: 28px;
        line-height: 24px;
      }
      @media (max-width: 1000px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 18px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background-color: #3F83F8;
        font-size: 22px;
        white-space: nowrap;
        border-radius: 2px !important;
        font-weight: 700;
        min-height: 60%;
        text-transform: uppercase;
        font-family: Manrope, sans-serif;

        &:hover {
          background-color:#3a74da;
        }
        @media (max-width: 1270px) {
          font-size: 20px;
        }
        @media (max-width: 1100px) {
          padding: 0.5rem 1rem !important;
          font-size: 16px;
          white-space: wrap;
        }
        @media (max-width: 800px) {
          height: fit-content;
          font-size: 12px;
        }
      }
    }
  }
}

.edge-v16 {
  background: linear-gradient(180deg, #00406D 0%, #000000 100%);

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    h3 {
      color: white;
      font-size: 38px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-right: 1rem;
      text-transform: uppercase;

      @media (max-width: 1300px) {
        font-size: 32px;
        line-height: 28px;
      }

      @media (max-width: 1160px) {
        font-size: 28px;
        line-height: 26px;
      }

      @media (max-width: 1000px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 18px;
        line-height: 18px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/edge/edge-v16-btn.png');
        background-size: contain;
        width: 700px;
        height: 80px;
        margin-left: -500px;
      }

      .banner-button {
        background-color: #3F83F8;
        font-size: 22px;
        white-space: nowrap;
        border-radius: 2px !important;
        font-weight: 700;
        padding: 1.25rem 2rem !important;
        text-transform: uppercase;

        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1100px) {
          padding: 1.25rem !important;
          font-size: 18px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 14px
        }
      }
    }
  }
}

.edge-v19 {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/edge/edge-v19-bg.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    p {
      display: flex;
      flex-direction: row;
      gap: 1rem;
      align-items: center;
      color: white;
      font-size: 32px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-right: 1rem;
      text-align: center;
      height: 100%;

      @media (max-width: 1470px) {
        font-size: 28px;
        line-height: 26px;
      }

      @media (max-width: 1280px) {
        font-size: 24px;
        line-height: 22px;
      }

      @media (max-width: 1140px) {
        font-size: 20px;
        line-height: 22px;
      }

      @media (max-width: 1000px) {
        flex-wrap: wrap;
        text-align: left;
        gap: 0.25rem;
        font-size: 16px;
        line-height: 18px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 16px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        font-size: 22px;
        font-family: Inter, sans-serif;
        border-radius: 21px !important;
        text-transform: uppercase;
        background: linear-gradient(90deg, #2172FF 0%, #479CFF 100%);
        border: 0.99px solid #479CFF;
        font-family: Inter;
        font-weight: 800;
        font-size: 20.06px;
        line-height: 24px;
        letter-spacing: 2%;
        text-align: center;
        text-transform: uppercase;


        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1100px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          border-radius: 8px !important;
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }
    }
  }
}

.edge-v20 {
  background: linear-gradient(180deg, #FFFFFF 0%, #E3E3E3 100%);

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    h3 {
      background: linear-gradient(180deg, #192940 0%, #143B71 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

      font-family: Inter;
      font-weight: 800;
      font-size: 38px;
      line-height: 40px;
      letter-spacing: -2%;
      vertical-align: middle;
      text-transform: uppercase;

      @media screen and (max-width: 1300px) {
        font-size: 32px;
        line-height: 32px;
      }

      @media (max-width: 1140px) {
        font-size: 28px;
        line-height: 28px;
      }

      @media (max-width: 800px) {
        font-size: 20px;
        line-height: 18px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        font-family: Inter, sans-serif;
        font-weight: 800;
        font-size: 20.06px;
        line-height: 24px;
        letter-spacing: 2%;
        text-align: center;
        text-transform: uppercase;
        background-color: #3F83F8;

        &:hover {
          background-color:#3a74da;
        }

        @media(max-width: 1575px) {
          margin-right: 40px;
        }

        @media (max-width: 1100px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          margin-right: 30px;
          height: fit-content;
          padding: 0.5rem !important;
          white-space: nowrap;
        }
      }
    }
  }

  .banner-bg-end {
    background-image: url('/next-assets/images/banners/mrd-flag.png');
    background-position: right;
    background-size: cover;
    background-repeat: no-repeat;
    width: 60px;
    height: 100%;
    @media (max-width: 800px) {
      margin-left: -20px;
    }
  }
}

.edge-mwd-v1 {
  background-image: url('/next-assets/images/banners/edge/mwd-bg.png');
  background-position: center;
  background-size: contain;
  width: 100%;

  .banner {
    margin: 0.25rem auto !important;
    border-radius: 4px;
    background-color: #081A3C;
    padding: 0.5rem;
    height: 90% !important;
    @media (max-width:1410px) {
      margin: 0.25rem !important;
    }

    .content {
      display: flex;
      flex-direction: column-reverse;
      align-items: flex-start;
      justify-content: flex-start;
      gap: 0.5rem;
      @media(max-width: 800px) {
        gap: 0.25rem;
      }
    }

    h3 {
      font-family: Inter, sans-serif;
      font-weight: 800;
      font-size: 32px;
      line-height: 30px;
      letter-spacing: -2%;
      vertical-align: middle;
      text-transform: uppercase;
      color: white;

      @media (max-width:1410px) {
        font-size: 28px;
      }

      @media (max-width: 1200px) {
        font-size: 24px;
        line-height: 24px;
      }
      @media (max-width: 1110px) {
        font-size: 20px;
        line-height: 18px;
      }

      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 16px;
        margin-right: 0.5rem;
      }
    }

    p {
      color: #68A5FF;
      font-family: Inter, sans-serif;
      font-weight: 700;
      font-size: 16px;
      line-height: 9px;
      letter-spacing: 20%;
      text-transform: uppercase;

      @media (max-width: 800px) {
        font-size: 12px;
        line-height: 14px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        font-family: Inter, sans-serif;
        font-weight: 800;
        font-size: 20.06px;
        line-height: 24px;
        letter-spacing: 2%;
        text-align: center;
        text-transform: uppercase;
        background-color: #FF1313;
        white-space: nowrap;

        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1100px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          height: fit-content;
          padding: 0.5rem !important;
          white-space: wrap;
        }
      }
    }
  }

}

.edge-mwd-v2 {
  background-image: url('/next-assets/images/banners/edge/mdw-v2-bg.png');
  background-position: left;
  background-size: cover;
  background-repeat: no-repeat;
  width: 100%;
  background-color: #051629;

  .banner {
    .content {
      display: flex;
      flex-direction: column-reverse;
      align-items: flex-start;
      justify-content: flex-start;
      gap: 0.5rem;
      @media(max-width: 800px) {
        gap: 0.25rem;
      }
    }

    h3 {
      font-family: Inter, sans-serif;
      font-weight: 700;
      font-size: 36px;
      line-height: 30px;
      vertical-align: middle;
      color: white;

      @media (max-width: 1400px) {
        font-size: 32px;
        line-height: 28px;
      }

      @media (max-width: 1250px) {
        font-size: 24px;
        line-height: 24px;
      }
      @media (max-width: 1110px) {
        font-size: 20px;
        line-height: 18px;
      }

      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 16px;
        margin-right: 0.5rem;
      }
    }

    p {
      font-family: Bebas Neue, Impact, 'Arial Narrow Bold', sans-serif;
      font-weight: 400;
      font-size: 24px;
      line-height: 14px;
      letter-spacing: 0.1em;
      text-transform: uppercase;

      @media (max-width: 1200px) {
        font-size: 18px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        font-family: Inter, sans-serif;
        font-weight: 800;
        font-size: 20.06px;
        line-height: 24px;
        letter-spacing: 2%;
        text-align: center;
        text-transform: uppercase;
        background-color: #FF1313;
        white-space: nowrap;
        height: 80%;

        @media (max-width: 1100px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          height: fit-content;
          padding: 0.5rem !important;
          white-space: wrap;
        }
      }
    }
  }

}

.edge-v18 {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/edge/edge-v18-bg.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    p {
      color: white;
      font-size: 32px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-right: 1rem;

      @media (max-width: 1350px) {
        font-size: 28px;
        line-height: 26px;
      }

      @media (max-width: 1000px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 18px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background-color: #479CFF;
        font-size: 22px;
        white-space: nowrap;
        border-radius: 2px !important;
        font-weight: 700;
        padding: 1.25rem 2rem !important;
        text-transform: uppercase;

        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1100px) {
          padding: 1.25rem !important;
          font-size: 18px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem !important;
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }
    }
  }
}
