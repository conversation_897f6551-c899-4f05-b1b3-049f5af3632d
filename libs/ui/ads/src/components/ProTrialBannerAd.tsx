import React from 'react';
import styled from '@benzinga/themetron';
import { BenzingaProLogo } from '@benzinga/logos-ui';
import { Impression } from '@benzinga/analytics';
import Image from 'next/image';

export const ProTrialBannerAd: React.FC = () => {
  return (
    <Impression campaign_id="14daytrial" unit_type="servicespagead">
      <Container>
        <a
          href="https://www.benzinga.com/pro/register/?utm_campaign=14daytrial&utm_adType=servicespagead&utm_ad=twoweektrial"
          rel="noreferrer"
          target="_blank"
        >
          <div className="banner-header">
            <BenzingaProLogo variant="blue" />
            <div className="banner-header-badge">best sale</div>
          </div>
          <div className="banner-body">
            <div className="banner-body-cta">
              <h3>Two-Week Benzinga Pro Trial</h3>
              <p>
                Get breaking news before major price moves with a free 2-week trial to our proprietary research
                terminal, Benzinga Pro! See the top stocks every trading day.
              </p>
              <button>Claim your free trial</button>
            </div>
            <div className="banner-body-graphic">
              <Image
                alt="Pro Trial Demo"
                height={232}
                src="https://www.benzinga.com/next-assets/images/services/pro-trial-services.png"
                width={390}
              />
              <p>Equip yourself with a variety of powerful tools to take your trading to the next level.</p>
            </div>
          </div>
        </a>
      </Container>
    </Impression>
  );
};

export default ProTrialBannerAd;

const Container = styled.div`
  border-radius: 4px;
  background: linear-gradient(180deg, #28313c 0%, #010101 100%);
  padding: 1rem;
  padding-left: 1.5rem;

  .banner-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    span {
      width: 180px;
    }

    .banner-header-badge {
      border: 1px solid rgba(255, 255, 255, 0.12);
      padding: 4px 16px;
      color: white;
      text-transform: uppercase;
      border-radius: 4px;
      font-size: 14px;
    }
  }

  .banner-body {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    height: 100%;

    .banner-body-cta {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      min-height: 220px;
      gap: 1rem;
      padding-top: 1rem;

      h3 {
        font-size: 24px;
        font-weight: 700;
        line-height: 26px;
        color: white;
        text-transform: uppercase;
      }

      p {
        color: #99aecc;
        max-width: 400px;
        margin: 0px;
        font-size: 16px;
        line-height: 26px;
      }

      button {
        font-size: 14px;
        padding: 12px 32px;
        background-color: #3f83f8;
        color: white;
        border-radius: 4px;
        text-align: center;
        transition: background-color 0.3s ease-in-out;
        text-transform: uppercase;
        width: fit-content;
        font-weight: bold;
        &:hover {
          background-color: #2e6ad1;
        }
      }
    }

    .banner-body-graphic {
      max-width: 390px;
      position: relative;
      display: flex;
      align-items: center;
      img {
        padding-bottom: 2rem;
      }

      p {
        font-weight: 600;
        font-size: 18px;
        padding: 1rem;
        border-radius: 4px;
        text-align: center;
        color: white;
        position: absolute;
        bottom: 0;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
        left: 0;
        right: 0;
      }
    }
  }

  @media (max-width: 780px) {
    .banner-body {
      flex-direction: column-reverse;
      align-items: center;
      gap: 1rem;
      .banner-body-graphic {
        max-width: unset;
        border-bottom: 1px solid rgba(255, 255, 255, 0.12);
        p {
          max-width: 100%;
        }
      }
      .banner-body-cta {
        h3 {
          font-size: 18px;
        }
        button {
          width: 100%;
        }
        p {
          font-size: 14px;
        }
      }
    }
  }
`;
