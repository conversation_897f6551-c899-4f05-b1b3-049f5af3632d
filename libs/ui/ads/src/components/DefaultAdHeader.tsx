import React from 'react';
import styled from '@benzinga/themetron';
// import Hooks from '@benzinga/hooks';
import { RaptiveAdPlaceholder } from './Raptive/RaptiveAdPlaceholder';
// import { InvestingChannel } from './InvestingChannel';

export const DefaultAdHeader: React.FC = () => {
  // const shorterScreenSize = '728x90';
  // const desktopScreenSize = '970x250';

  // const { height } = Hooks.useWindowSize();
  // const size = height > 960 ? desktopScreenSize : shorterScreenSize;

  return (
    <DefaultAdHeaderWrapper>
      <RaptiveAdPlaceholder className="overflow-hidden h-full" type="header" />
      {/* {!!height && (
        <InvestingChannel
          className="mb-5"
          id="above-article-ic-placeholder"
          mobileSize="300x50"
          native={{
            adSlot: 'ic_728x90_1',
          }}
          size={size}
        />
      )} */}
    </DefaultAdHeaderWrapper>
  );
};

const DefaultAdHeaderWrapper = styled.div`
  margin: auto;
  width: 970px;
  min-height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 500px) {
    width: 320px;
    min-height: 50px;
  }
`;
