import React from 'react';
import styled from '@benzinga/themetron';
import { default as RC<PERSON>lider } from 'rc-slider';

interface SliderProps extends React.ComponentProps<typeof RCSlider> {
  leftLabel: React.ReactElement<{ className?: string }>;
  rightLabel: React.ReactElement<{ className?: string }>;
}

export const Slider: React.FC<SliderProps> = props => (
  <SliderDiv>
    {props.leftLabel && <>{React.cloneElement(props.leftLabel, { className: 'leftLabel' })}</>}
    <RCSlider {...props} className="track" />
    {props.rightLabel && <>{React.cloneElement(props.rightLabel, { className: 'rightLabel' })}</>}
  </SliderDiv>
);

const SliderDiv = styled.div`
  height: 1em;
  display: flex;
  align-items: center;

  font-size: 2em;
  fill: ${props => props.theme.colors.foreground};

  .leftLabel {
    margin-right: 0.5em;
  }

  .rightLabel {
    margin-left: 0.5em;
  }

  .track {
    flex: 1;
  }
`;
