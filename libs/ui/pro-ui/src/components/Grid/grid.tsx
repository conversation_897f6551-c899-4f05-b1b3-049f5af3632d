'use client';
import React from 'react';

import {
  ColDef,
  ColGroupDef,
  GetContextMenuItemsParams,
  GetMainMenuItemsParams,
  MenuItemDef,
  ModuleRegistry,
  NavigateToNextCellParams,
  GridReadyEvent,
  GridApi,
} from '@ag-grid-community/core';

import '@ag-grid-community/styles/ag-grid.css';
import '@ag-grid-community/styles/ag-theme-quartz.css';
import { AgGridReact, AgGridReactProps, AgReactUiProps } from '@ag-grid-community/react';
import { CsvExportModule } from '@ag-grid-community/csv-export';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { LicenseManager } from '@ag-grid-enterprise/core';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ExcelExportModule } from '@ag-grid-enterprise/excel-export';
import { ClipboardModule } from '@ag-grid-enterprise/clipboard';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { FiltersToolPanelModule } from '@ag-grid-enterprise/filter-tool-panel';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { SideBarModule } from '@ag-grid-enterprise/side-bar';
import { RangeSelectionModule } from '@ag-grid-enterprise/range-selection';

import {
  getAutoSizeAllColumns,
  getAutoSizeThisColumn,
  mapContextOption,
  getExportTable,
  getResetAllColumns,
} from './utils';
import { ProContext } from '@benzinga/pro-tools';
import { StockSymbol } from '@benzinga/session';
import { GridExportParams, MenuItemParams } from './entities';
import { debounce, arrayDeepEqual } from '@benzinga/utils';
import { BZColumnState, DefaultTableParameters, DelayOfTableLifecycle, TableParameters } from '@benzinga/ag-grid-utils';
import { SessionContext } from '@benzinga/session-context';
import { TickerSelectedEvent, WidgetLinkingManager } from '@benzinga/widget-linking';
import styled, { TC, ThemeContext } from '@benzinga/themetron';
import { WatchlistManager } from '@benzinga/watchlist-manager';
import { linkingContextOption, openWithContextOption, watchlistContextOption } from '../ContextMenu';
import Hooks from '@benzinga/hooks';
import { GridTransaction } from './gridTransaction';
import { NoResults } from '../NoResults';
import { Spinner } from '@benzinga/core-ui';
import ReactDOM from 'react-dom';

ModuleRegistry.registerModules([
  ClientSideRowModelModule,
  CsvExportModule,
  ClipboardModule,
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MenuModule,
  RangeSelectionModule,
  SetFilterModule,
  SideBarModule,
]);

const agGridEnterpriseKey =
  'Using_this_AG_Grid_Enterprise_key_( AG-042627 )_in_excess_of_the_licence_granted_is_not_permitted___Please_report_misuse_to_( <EMAIL> )___For_help_with_changing_this_key_please_contact_( <EMAIL> )___( Accretive Capital )_is_granted_a_( Single Application )_Developer_License_for_the_application_( Benzinga Pro )_only_for_( 1 )_Front-End_JavaScript_developer___All_Front-End_JavaScript_developers_working_on_( Benzinga Pro )_need_to_be_licensed___( Benzinga Pro )_has_been_granted_a_Deployment_License_Add-on_for_( 1 )_Production_Environment___This_key_works_with_AG_Grid_Enterprise_versions_released_before_( 22 July 2024 )____[v2]_MTcyMTYwMjgwMDAwMA==b5faba596de44abceab7aac99d98af58';
LicenseManager.setLicenseKey(agGridEnterpriseKey);

interface Props extends AgGridReactProps, AgReactUiProps {
  className?: string;
  columnDefs?: ((ColDef & { absSorted?: boolean }) | ColGroupDef)[] | null;
  autosizeColumns?: boolean;
  getAdditionalContextMenuItems?: (params: GetContextMenuItemsParams) => (string | MenuItemDef)[];
  gridLayout?: TableParameters;
  autoSelectTickerOnSelect?: (data: unknown) => StockSymbol;
  exportParams?: GridExportParams;
  symbolColIDs?: string[];
  onGridLayoutChanged?: (parameters: Partial<TableParameters>) => void;
  onSymbolClick?: (symbol: StockSymbol) => void;
  refreshGrid?: string | number;
  transaction?: GridTransaction<any>;
  sizeColumnsToFit?: boolean;
  overlays?: {
    noData?: React.ReactNode;
    loading?: React.ReactNode;
    error?: React.ReactNode;
  };
}

export const Grid = React.memo((props: Props) => {
  const {
    agGridProps,
    autosizeColumns,
    exportParams,
    getAdditionalContextMenuItems,
    gridLayout,
    gridOptions,
    onGridLayoutChanged,
    onGridReadyProp,
    preColumnDefs,
    refreshGrid,
    sizeColumnsToFit,
    symbolColIDs,
  } = React.useMemo(() => {
    const {
      autosizeColumns,
      columnDefs,
      exportParams = {},
      getAdditionalContextMenuItems = (_params: GetContextMenuItemsParams) => [],
      gridLayout = DefaultTableParameters,
      gridOptions = {},
      onGridLayoutChanged,
      onGridReady: onGridReadyProp,
      refreshGrid,
      sizeColumnsToFit,
      symbolColIDs,

      ...agGridProps
    } = props;

    return {
      agGridProps,
      autosizeColumns,
      exportParams,
      getAdditionalContextMenuItems,
      gridLayout: { ...DefaultTableParameters, ...gridLayout },
      gridOptions,
      onGridLayoutChanged,
      onGridReadyProp,
      preColumnDefs: columnDefs,
      refreshGrid,
      sizeColumnsToFit,
      symbolColIDs,
    };
  }, [props]);

  const proContext = React.useContext(ProContext);
  const session = React.useContext(SessionContext);

  const columnDefs = React.useMemo<((ColDef & BZColumnState) | ColGroupDef)[] | null>(
    () =>
      preColumnDefs?.map<(ColDef & BZColumnState) | ColGroupDef>(c => {
        return {
          ...c,
          ...(gridLayout.bzColumnState?.find(b => b.colId === (c as ColDef).field) ?? {}),
          colId: (c as any).colId || (c as any).field || '', // Ensure colId is always assigned a string value
        };
      }) ?? null,
    [preColumnDefs, gridLayout.bzColumnState],
  );

  const prevColumnDefs = React.useRef(preColumnDefs);

  const updateColumnState = React.useCallback((gridLayout: TableParameters) => {
    gridApi.current?.applyColumnState({
      applyOrder: true,
      defaultState: { ...gridLayout.defaultState, width: 100 },
      state: gridLayout.columns,
    });
  }, []);

  React.useEffect(() => {
    if (preColumnDefs !== prevColumnDefs.current) {
      setTimeout(() => updateColumnState(gridLayout), 1);
    }
    prevColumnDefs.current = preColumnDefs;
  }, [gridLayout, gridLayout.columns, gridLayout.defaultState, preColumnDefs, updateColumnState]);

  const linkingManager = session.getManager(WidgetLinkingManager);
  const watchlistManager = session.getManager(WatchlistManager);
  const { gridOptionsProp, onGridReadyOptionsProp } = React.useMemo(() => {
    const { onGridReady: onGridReadyOptionsProp, ...gridOptionsProp } = gridOptions;
    return { gridOptionsProp, onGridReadyOptionsProp };
  }, [gridOptions]);

  const gridApi = React.useRef<GridApi | undefined>(undefined);

  const viewPortElement = React.useRef<Element | null>(null);
  const bzOverLayElement = React.useMemo<HTMLDivElement>(() => {
    const elm = document.createElement('div');
    elm.setAttribute('style', 'display:contents;');
    return elm;
  }, []);
  const refDiv = React.useCallback(
    (element: HTMLDivElement) => {
      if (!bzOverLayElement || !element || element.innerHTML.length === 0 || bzOverLayElement.innerHTML.length === 0)
        return;
      setTimeout(() => {
        viewPortElement.current?.removeChild(bzOverLayElement);
        viewPortElement.current = element?.getElementsByClassName('ag-body-viewport')?.[0];
        viewPortElement.current?.appendChild(bzOverLayElement);
      });
    },
    [bzOverLayElement],
  );

  React.useEffect(() => {
    return () => {
      viewPortElement.current?.removeChild(bzOverLayElement);
    };
  }, [bzOverLayElement]);

  const permitUpdates = React.useRef<boolean>(false);
  const modelApplied = React.useRef<boolean>(false);
  const theme = React.useContext(ThemeContext);
  const [widgetLinks, setWidgetLinks] = React.useState(() => linkingManager.getWidgetLinks());
  Hooks.useSubscriber(linkingManager, e => {
    switch (e.type) {
      case 'linking:link_updated': {
        setWidgetLinks(linkingManager.getWidgetLinks());
        break;
      }
    }
  });

  const [overlayError, setOverlayError] = React.useState('');
  const [overlay, setOverlay] = React.useState(props.transaction ? 'showLoadingOverlay' : '');
  const [gridReady, setGridReady] = React.useState(false);
  Hooks.useSubscriber(props.transaction, event => {
    switch (event.type) {
      case 'grid_transaction:overlay':
        setOverlay(event.overlay);
        switch (event.overlay) {
          case 'showErrorOverlay':
            setOverlayError(event.error);
            break;

          case 'hideOverlay':
          case 'showNoRowsOverlay':
            setOverlayError('');
            if (gridApi.current && gridLayout.filter) {
              setTimeout(() => gridApi.current?.setFilterModel(gridLayout.filter), 50);
            }
            break;
        }

        break;
      case 'grid_transaction:data':
        switch (event.dataType) {
          case 'clearing':
            permitUpdates.current = false;
            break;
          case 'flush':
            permitUpdates.current = true;
            break;
        }
    }
  });

  const getFitAllColumns = React.useCallback(
    ({ api: gridApi }: MenuItemParams) => ({
      action: () => {
        gridApi.sizeColumnsToFit();
        onGridLayoutChanged?.({
          ...gridLayout,
          columnsResized: false,
        });
      },
      name: 'Fit All Columns',
    }),
    [gridLayout, onGridLayoutChanged],
  );

  const getContextMenuItems = React.useCallback(
    (params: GetContextMenuItemsParams) => {
      const autoSizeAllColumns = getAutoSizeAllColumns(params);
      const autoSizeThisColumn = getAutoSizeThisColumn(params);
      const exportTable = getExportTable(params, exportParams);
      const fitAllColumns = getFitAllColumns(params);
      const resetColumns = getResetAllColumns(params);
      const linkTo = mapContextOption(
        [
          linkingContextOption(widgetLinks, linkId => {
            const feed = linkingManager.getWidgetLinkFeedByID(linkId);
            feed?.pushEvent({
              symbol: params.value,
              type: 'ticker_selected',
            } as TickerSelectedEvent);
          }),
        ],
        params,
        {
          colIds: symbolColIDs ?? [],
          theme,
        },
      );
      const addToWL = mapContextOption([watchlistContextOption(params.value, watchlistManager)], params, {
        colIds: symbolColIDs ?? [],
        theme,
      });
      const openWith = mapContextOption([openWithContextOption(params.value, proContext.addWidgetWithSymbol)], params, {
        colIds: symbolColIDs ?? [],
        theme,
      });

      return params.node
        ? [
            'copy',
            'copyWithHeaders',
            'separator',
            exportTable,
            ...linkTo,
            ...addToWL,
            ...openWith,
            ...getAdditionalContextMenuItems(params),
            'separator',
            autoSizeThisColumn,
            autoSizeAllColumns,
            fitAllColumns,
            resetColumns,
          ]
        : [exportTable, 'separator', autoSizeAllColumns, fitAllColumns, resetColumns];
    },
    [
      exportParams,
      getFitAllColumns,
      widgetLinks,
      symbolColIDs,
      theme,
      watchlistManager,
      proContext.addWidgetWithSymbol,
      getAdditionalContextMenuItems,
      linkingManager,
    ],
  );

  const getMainMenuItems = React.useCallback(
    (params: GetMainMenuItemsParams) => {
      return [
        'pinSubMenu',
        'autoSizeThis',
        'separator',
        'autoSizeAll',
        getResetAllColumns(params),
        'separator',
        getExportTable(params, exportParams),
      ];
    },
    [exportParams],
  );

  const navigateToNextCell = React.useCallback(
    (params: NavigateToNextCellParams) => {
      const suggestedNextCell = params.nextCellPosition;
      if (suggestedNextCell === null) {
        return params.previousCellPosition;
      }

      const autoSelectTickerOnSelect = props.autoSelectTickerOnSelect;

      if (autoSelectTickerOnSelect === undefined) {
        return suggestedNextCell;
      }

      // this is some code
      const KEY_UP = 38;
      const KEY_DOWN = 40;

      const noUpOrDownKeyPressed = +params.key !== KEY_DOWN && +params.key !== KEY_UP;
      if (noUpOrDownKeyPressed) {
        return suggestedNextCell;
      }

      params.api.forEachNode(node => {
        if (node.rowIndex === suggestedNextCell.rowIndex) {
          const ticker = autoSelectTickerOnSelect(node.data);
          const onSymbolClick = props.onSymbolClick;
          ticker && onSymbolClick?.(ticker);
        }
      });

      return suggestedNextCell;
    },
    [props.autoSelectTickerOnSelect, props.onSymbolClick],
  );

  const updateColumnParameters = React.useCallback(() => {
    if (gridApi.current && onGridLayoutChanged) {
      const columnState = gridApi.current.getColumnState();
      if (columnState?.length && !arrayDeepEqual(gridLayout.columns, columnState)) {
        const reorderedColumnState = columnState.sort((a, b) => (a.hide ? 1 : 0) - (b.hide ? 1 : 0));
        gridApi.current.applyColumnState({ applyOrder: true, state: reorderedColumnState });
        onGridLayoutChanged({
          ...gridLayout,
          columns: reorderedColumnState,
        });
      }
    }
  }, [gridLayout, onGridLayoutChanged]);

  const updateColumnsResized = React.useMemo(
    () =>
      debounce(() => {
        if (onGridLayoutChanged && !gridLayout.columnsResized) {
          onGridLayoutChanged({
            ...gridLayout,
            columnsResized: true,
          });
        }
      }, DelayOfTableLifecycle.set),
    [gridLayout, onGridLayoutChanged],
  );

  const updateFilterParameters = React.useCallback(() => {
    if (gridApi.current && onGridLayoutChanged) {
      onGridLayoutChanged({
        ...gridLayout,
        filter: gridApi.current.getFilterModel(),
      });
    }
  }, [gridLayout, onGridLayoutChanged]);

  const applyFilterParameters = React.useCallback(() => {
    if (gridApi.current && gridLayout.filter) {
      gridApi.current?.setFilterModel(gridLayout.filter);
    }
  }, [gridLayout.filter]);

  React.useEffect(() => applyFilterParameters(), [applyFilterParameters]);

  const updateOverlay = React.useCallback(() => {
    if (gridApi.current) {
      setOverlay(old =>
        gridApi.current?.getDisplayedRowCount()
          ? ''
          : old === 'showLoadingOverlay'
            ? 'showLoadingOverlay'
            : 'showNoRowsOverlay',
      );
    }
  }, []);

  const applyTableParameters = React.useCallback(() => {
    if (gridApi.current) {
      if (gridLayout.columns?.length) {
        updateColumnState(gridLayout);
      }
      if (!gridLayout.columnsResized) {
        if (autosizeColumns) {
          gridApi.current.autoSizeAllColumns();
        } else if (sizeColumnsToFit) {
          gridApi.current?.sizeColumnsToFit();
        }
      }
    }
    if (gridApi.current && Object.keys(gridLayout.filter ?? {}).length) {
      gridApi.current?.setFilterModel(gridLayout.filter);
      gridApi?.current?.onFilterChanged();
    }
  }, [autosizeColumns, gridLayout, sizeColumnsToFit, updateColumnState]);

  const preventParametersUpdate = () => {
    permitUpdates.current = false;
    setTimeout(() => {
      permitUpdates.current = true;
    }, DelayOfTableLifecycle.update);
  };

  const [throttledUpdateFilterParameters] = Hooks.useThrottle(updateFilterParameters, 200);

  const gridEventListener = React.useCallback(
    (eventType: string) => {
      switch (eventType) {
        case 'columnResized':
          if (permitUpdates.current) {
            updateColumnsResized();
            updateColumnParameters();
          }
          break;
        case 'columnVisible':
        case 'columnPinned':
        case 'columnMoved':
        case 'columnValueChanged':
        case 'columnPivotModeChanged':
        case 'columnPivotChanged':
        case 'columnGroupOpened':
        case 'columnRowGroupChanged':
        case 'dragStopped':
        case 'sortChanged':
          if (permitUpdates.current) {
            updateColumnParameters();
          }
          break;
        case 'filterChanged':
          if (permitUpdates.current) {
            throttledUpdateFilterParameters();
          }
          break;
        case 'modelUpdated': {
          const model = gridApi?.current?.getFilterModel();
          if (!modelApplied.current && Object.keys(model ?? {}).length) {
            setTimeout(() => {
              gridApi?.current?.onFilterChanged();
            }, 500);
            modelApplied.current = true;
          }
          break;
        }
        case 'gridColumnsChanged':
          updateColumnParameters();
          preventParametersUpdate();
          break;
        case 'gridSizeChanged':
          if (!gridLayout.columnsResized && sizeColumnsToFit) {
            gridApi.current?.sizeColumnsToFit();
          }
          setTimeout(() => {
            gridApi.current?.redrawRows();
          }, 0);
          preventParametersUpdate();
          break;
        case 'displayedRowsChanged':
          updateOverlay();
          break;
        case 'firstDataRendered':
          applyTableParameters();
          preventParametersUpdate();
          break;
      }
    },
    [
      updateColumnParameters,
      gridLayout.columnsResized,
      sizeColumnsToFit,
      updateOverlay,
      applyTableParameters,
      updateColumnsResized,
      throttledUpdateFilterParameters,
    ],
  );

  React.useEffect(() => {
    if (gridReady) {
      gridApi.current?.addGlobalListener(gridEventListener);
      return () => gridApi.current?.removeGlobalListener(gridEventListener);
    }
    return;
  }, [gridEventListener, gridReady]);

  const handleGridReady = React.useCallback(
    (params: GridReadyEvent) => {
      permitUpdates.current = false;
      if (params) {
        gridApi.current = params.api;
        gridLayout?.columns && updateColumnState(gridLayout);
        applyTableParameters();
        preventParametersUpdate();

        // gridEventListenerRef.current && gridApi.current?.addGlobalListener(gridEventListenerRef.current);
        props.transaction?.setGridApi(params.api);
        onGridReadyProp?.(params);
        onGridReadyOptionsProp?.(params);
        setGridReady(true);
      }
    },
    [gridLayout, updateColumnState, applyTableParameters, props.transaction, onGridReadyProp, onGridReadyOptionsProp],
  );

  const overlayDisplay = React.useMemo(() => {
    if (bzOverLayElement) {
      switch (overlay) {
        case 'showErrorOverlay':
          return ReactDOM.createPortal(
            <Overlay>{props.overlays?.error ?? <NoRowsOverlayComponent message={overlayError} />}</Overlay>,
            bzOverLayElement,
          );
        case 'showLoadingOverlay':
          return ReactDOM.createPortal(
            <Overlay>{props.overlays?.loading ?? <LoadingOverlayComponent />}</Overlay>,
            bzOverLayElement,
          );
        case 'showNoRowsOverlay':
          return ReactDOM.createPortal(
            <Overlay>{props.overlays?.noData ?? <NoRowsOverlayComponent />}</Overlay>,
            bzOverLayElement,
          );
        default:
          return null;
      }
    } else {
      return null;
    }
  }, [bzOverLayElement, overlay, props.overlays?.loading, props.overlays?.noData, props.overlays?.error, overlayError]);

  const deselectAll = React.useCallback(() => {
    if (gridApi.current) {
      gridApi.current.deselectAll();
      gridApi.current.clearRangeSelection();
    }
  }, []);

  const handleClickOff = React.useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if ((event.target as HTMLDivElement)?.className?.includes('ag-body-viewport')) {
        deselectAll();
      }
    },
    [deselectAll],
  );

  const formattedColumnDefs = React.useMemo(
    () =>
      columnDefs?.map(c => ({
        ...c,
        comparator: (c as ColDef & BZColumnState).absSorted
          ? (valueA: any, valueB: any) => (valueA === valueB ? 0 : Math.abs(valueA) > Math.abs(valueB) ? 1 : -1)
          : (c as ColDef).comparator,
      })),
    [columnDefs],
  );

  return (
    <Container className={props.className}>
      {overlayDisplay}
      <AgGridTheme className={'ag-theme-quartz'} onClick={handleClickOff} ref={refDiv}>
        <AgGridReact
          {...agGridProps}
          columnDefs={formattedColumnDefs}
          context={{ session }}
          getContextMenuItems={getContextMenuItems}
          getMainMenuItems={getMainMenuItems}
          gridOptions={gridOptionsProp}
          key={refreshGrid}
          navigateToNextCell={props.autoSelectTickerOnSelect ? navigateToNextCell : undefined}
          onGridReady={handleGridReady}
          suppressDragLeaveHidesColumns
          tooltipInteraction={true}
          tooltipShowDelay={0}
        />
      </AgGridTheme>
    </Container>
  );
});

const Overlay = styled.div`
  background-color: ${props => props.theme.colors.background};
  position: absolute;
  height: 100%;
  width: 100%;
  z-index: 3;
  overflow-y: auto;
`;

const LoadingOverlayComponent: React.FC = () => (
  <SpinnerContainer>
    <Spinner />
  </SpinnerContainer>
);

const AgGridTheme = styled.div`
  height: 100%;
  width: 100%;
`;

const Container = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  overflow: hidden;
  position: relative;

  .ag-theme-quartz {
    /* --ag-icon-font-family: 'Font Awesome 5 Free'; */
    --ag-accent-color: ${props => props.theme.colors.accent};
    --ag-active: ${props => props.theme.colors.brand};
    --ag-alt-background: ${props => props.theme.colors.background};
    --ag-alt-icon-color: ${props => props.theme.colors.background};
    --ag-background-color: ${props => props.theme.colors.background};
    --ag-border-color: ${props => props.theme.colors.border};
    --ag-card-radius: 0px;
    --ag-cell-data-changed-color: #fce4ec;
    --ag-chip-background-color: lighten(${props => props.theme.colors.background}, 5);
    --ag-chrome-background: ${props => props.theme.colors.backgroundActive};
    --ag-control-panel-background-color: ${props => props.theme.colors.backgroundActive};
    --ag-default-background: ${props => props.theme.colors.background};
    --ag-disabled-foreground-color: rgba(${props => props.theme.colors.foreground}, 0.5);
    --ag-editor-background-color: ${props => props.theme.colors.backgroundActive};
    --ag-font-family: 'Nimbus Sans', helvetica, arial, sans-serif;
    --ag-font-size: 12px;
    --ag-foreground-color: ${props => props.theme.colors.foreground};
    --ag-header-background-color: ${props => props.theme.colors.backgroundActive};
    --ag-header-height: 30px;
    --ag-header-column-separator-color: ${props => props.theme.colors.border};
    --ag-header-column-separator: true;
    --ag-header-column-separator-height: 80%;
    --ag-header-column-separator-width: 1px;
    --ag-hover-color: lighten(${props => props.theme.colors.background}, 10);
    --ag-icon-color: ${props => props.theme.colors.foreground};
    --ag-odd-row-background-color: ${props => props.theme.colors.backgroundInactive}7F;
    --ag-panel-background-color: ${props => props.theme.colors.backgroundActive};
    --ag-primary-color: ${props => props.theme.colors.brand};
    --ag-range-selection-background-color: ${props => props.theme.colors.brand}CC;
    --ag-range-selection-highlight-color: ${props => props.theme.colors.brand};
    --ag-row-height: 20px;
    --ag-row-border-width: 0px;
    --ag-secondary-foreground-color: rgba(${props => props.theme.colors.foreground}, 1);
    --ag-selected-color: darken(${props => props.theme.colors.brand}, 25);
    --ag-subheader-background-color: ${props => props.theme.colors.backgroundActive};
    --ag-subheader-toolbar-background-color: ${props => props.theme.colors.backgroundActive};
    --ag-tool-panel-background-color: ${props => props.theme.colors.backgroundActive};
    --ag-value-change-delta-up-color: ${props => props.theme.colors.statistic.positive};
    --ag-value-change-delta-down-color: ${props => props.theme.colors.statistic.negative};
    --ag-value-change-value-highlight-background-color: ${props => props.theme.colors.statistic.positive}7F;
    --ag-wrapper-border-radius: 0px;
  }
`;

const NoRowsOverlayComponent: React.FC<{ message?: string }> = ({ message }) => <NoResults message={message} />;

const SpinnerContainer = styled.div`
  ${TC.RowStyles};
  flex: 1;
  justify-content: center;
  align-items: center;
  height: 100%;
`;
