import React from 'react';
import { ColDef, ColumnState, ColGroupDef, ITooltipParams } from '@ag-grid-community/core';
import {
  BooleanColDef,
  CalculatedChangePercentColDef,
  ChangeColDef,
  ChangePercentColDef,
  ChangeShortenedNumberColDef,
  CommaSeparatedNumberColDef,
  DateColDef,
  FlagColDef,
  LinkColDef,
  NotesColDef,
  NumberColDef,
  PercentNumberBackgroundColorColDef,
  PercentNumberColDef,
  PeriodColDef,
  PriceAlertColDef,
  PriceColDef,
  ProgressColDef,
  SetColDef,
  ShortenedNumberColDef,
  SparkLineColDef,
  SymbolColDef,
  TextColDef,
} from './columnDefs';
import { useScannerColumnableDefs } from '@benzinga/scanner-manager-hooks';
import { TimeConfig, useTime } from '@benzinga/time-manager-hooks';
import { isTextOverflowingAgGridCell } from './helpers/getCellTextOverflowed';
import { TimeFormat } from '@benzinga/date-utils';
import { sparklineTimeFrames } from '../Sparkline/Sparkline';
import { FullDataField } from '@benzinga/quotes-v3-fields-manager';
import { arrayDeepEqual, deepArrayMerge } from '@benzinga/utils';

export const useLayoutFill = (colLayouts: ColumnState[]): ColumnState[] => {
  const addDefs = useQuoteColDef([]);

  const defs = React.useMemo(() => addDefs.flatMap<ColDef>(a => (a as ColGroupDef).children ?? [a]), [addDefs]);

  const addDefaults = React.useCallback((cs: ColumnState) => {
    return {
      aggFunc: null,
      flex: null,
      hide: true,
      pinned: null,
      pivot: false,
      pivotIndex: null,
      rowGroup: false,
      rowGroupIndex: null,
      sort: null,
      sortIndex: null,
      width: 100,
      ...cs,
    };
  }, []);
  const [col] = React.useState(() =>
    (defs ?? [])
      .filter(df => !colLayouts.some(a => a.colId === (df.colId ?? df.field ?? '')))
      .reduce<ColumnState[]>((acc, df) => {
        acc.push(
          addDefaults({
            colId: df.colId ?? df.field ?? '',
            hide: df.hide ?? true,
          }),
        );
        return acc;
      }, colLayouts.map(addDefaults)),
  );

  return col;
};

export const useQuoteColDef = (colLayouts: ColumnState[], callback?: (value: boolean) => void, groupPrefix = '') => {
  const latestTimeConfig: TimeConfig = useTime();
  const defs = useScannerColumnableDefs();

  const getColDefs = React.useCallback(
    (colLayouts: ColumnState[], _prevCols?: (ColGroupDef | ColDef)[]) => {
      const newCols = [
        ...Array.from(
          (defs ?? [])
            .reduce((acc, df) => {
              const colDef = buildColDef(
                df,
                Object.entries(colLayouts.find(a => a.colId === df.name) ?? {}).reduce(
                  (acc, [key, value]) => ({ ...acc, [key]: value ?? undefined }),
                  {},
                ),
                latestTimeConfig,
              );
              if (acc.has(df.category ?? 'NONE')) {
                acc.get(df.category ?? 'NONE')?.push(colDef);
              } else {
                acc.set(df.category ?? 'NONE', [colDef]);
              }
              return acc;
            }, new Map<string, ColDef[]>())
            .entries(),
        ).flatMap(([category, val]) =>
          category !== 'NONE'
            ? [
                {
                  children: val.sort((a, b) =>
                    (a.headerName?.toLowerCase() ?? '').localeCompare(b.headerName?.toLowerCase() ?? ''),
                  ),
                  headerName: groupPrefix + category,
                },
              ]
            : val,
        ),
        {
          children: [
            PriceColDef({
              field: 'cost',
              headerName: 'Cost',
              hide: true,
            }),
            ChangePercentColDef({
              field: 'gainPercentage',
              headerName: 'Gain (%)',
              hide: true,
            }),
            PriceColDef({
              field: 'gainDollar',
              headerName: 'Gain ($)',
              hide: true,
            }),
            PriceColDef({
              field: 'marketValue',
              headerName: 'Mrkt Value',
              hide: true,
            }),
            ShortenedNumberColDef({
              field: 'quantity',
              headerName: 'Quantity',
              hide: true,
            }),
          ],
          headerName: 'Holding',
        },
        {
          children: [
            PriceAlertColDef({
              field: 'PriceAlerts',
              headerName: 'Price Alert',
              hide: true,
            }),
            NotesColDef({
              field: 'notes',
              headerName: 'Notes',
              hide: true,
            }),
          ],
          headerName: 'Reminders',
        },
        {
          children: sparklineTimeFrames.map((s, i) =>
            SparkLineColDef(
              {
                field: `sparkline${i}`,
                headerName: s.label,
                hide: true,
              },
              s,
            ),
          ),
          headerName: 'Chart',
        },
      ];

      return newCols;
    },
    [defs, groupPrefix, latestTimeConfig],
  );

  const [columnDefs, setColDefs] = React.useState<(ColGroupDef | ColDef)[]>(() => getColDefs(colLayouts));

  const prevDef = React.useRef<FullDataField[] | undefined>(undefined);
  const prevTimeConfigRef = React.useRef<TimeConfig | null>(latestTimeConfig);
  const prevColLayoutsRef = React.useRef(colLayouts);

  React.useEffect(() => {
    if (
      prevDef.current !== defs ||
      prevTimeConfigRef.current !== latestTimeConfig ||
      !arrayDeepEqual(prevColLayoutsRef.current, colLayouts)
    ) {
      prevDef.current = defs;
      prevTimeConfigRef.current = latestTimeConfig;
      prevColLayoutsRef.current = colLayouts;
      setColDefs(prev => getColDefs(colLayouts, prev));
      if (callback) {
        callback(true);
      }
    }
  }, [defs, getColDefs, colLayouts, latestTimeConfig, callback]);

  return columnDefs;
};

export interface UserSettings {
  timeFormat: TimeFormat;
  timeOffset: number;
  timezone: string;
}
export type TimezoneName = string;

/**
 * Build columns defs based on the data field def.
 * A lot of fairly manual/custom config for columns in here that could use cleaning up in the future.
 */
const buildColDef = (df: FullDataField, colLayout: ColDef, settings: UserSettings): ColDef => {
  const format = df.format.split('-')[0];
  // Our default coldef
  const colDef: ColDef = {
    ...colLayout,
    colId: df.name ?? undefined,
    field: df.name ?? undefined,
    headerName: df.label ?? undefined,
    hide: true,
    initialWidth: 100,
    sortable: df.filterable ?? undefined,
  };

  if (!colDef.cellRenderer && !colDef.tooltipValueGetter) {
    colDef.tooltipValueGetter = (params: ITooltipParams): string => {
      const text = params.valueFormatted || params.value;
      const cellWidth = params.column?.getActualWidth() || 0;
      return isTextOverflowingAgGridCell(text, cellWidth) ? text : '';
    };
  }

  switch (format) {
    case 'calculatedChangePercent':
      return CalculatedChangePercentColDef(colDef, '', '');
    case 'change':
      return ChangeColDef(colDef);
    case 'changePercent':
      return ChangePercentColDef(colDef);
    case 'changeShortenedNumber':
      return ChangeShortenedNumberColDef(colDef);
    case 'commaSeparatedNumber':
      return CommaSeparatedNumberColDef(colDef);
    case 'date':
    case 'time':
    case 'dateTime':
      return DateColDef(colDef, { timeFormat: settings.timeFormat, timeOffset: settings.timeOffset });

    case 'number':
    case 'positiveNumber':
      return NumberColDef(colDef);
    case 'shortenedNumber':
      return ShortenedNumberColDef(colDef);
    case 'percentNumberBackgroundColor':
      return PercentNumberBackgroundColorColDef(colDef);
    case 'percentNumber':
    case 'positivePercentNumber':
      return PercentNumberColDef(colDef, 1);
    case 'positivePrice':
    case 'price':
      return PriceColDef(colDef);
    case 'boolean':
      return BooleanColDef(colDef);
    case 'flag':
      return FlagColDef(colDef);
    case 'link':
      return LinkColDef(colDef);

    case 'note':
      return NotesColDef(colDef);
    case 'period':
      return PeriodColDef(colDef);
    case 'priceAlert':
      return PriceAlertColDef(colDef);
    case 'progress':
      return ProgressColDef(colDef, { high: 100, low: 0 });
    case 'set':
      return SetColDef(colDef);
    case 'sparkLine':
      return SparkLineColDef(colDef);
    case 'symbol':
      return SymbolColDef(colDef);
    case 'text':
      return TextColDef(colDef);
  }

  // If the data field has options, then use that to map values
  // if ((df.options?.length ?? 0) > 0) {
  //   const optionsByValue = new Map<string, string>(
  //     df.options?.map(option => [option.value as string, option.label as string]),
  //   );
  //   colDef.cellRenderer = (params: { value: any }) => optionsByValue.get(String(params.value)) ?? params.value ?? '-';
  // }

  return colDef;
};
