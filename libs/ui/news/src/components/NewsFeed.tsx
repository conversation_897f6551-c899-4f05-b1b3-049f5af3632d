'use client';
import React, { useCallback, useEffect, useState } from 'react';
import styled from '@benzinga/themetron';
import { Spinner, FeedButton, Icon } from '@benzinga/core-ui';
import { faChevronDown } from '@fortawesome/pro-solid-svg-icons/faChevronDown';
import { SessionContext } from '@benzinga/session-context';
import { getNodeUrl } from '@benzinga/advanced-news-manager';
import { BasicNewsManager, News, NewsFeedQueryParams } from '@benzinga/basic-news-manager';
import { isSponsoredArticle } from '@benzinga/article-manager';
import { PostCard } from './PostCard';
import { formatImageUrl } from '@benzinga/utils';

interface NewsFeedProps {
  initialNewsItems?: News[];
  params?: NewsFeedQueryParams;
  title?: string;
  isInfinite?: boolean;
  hideEmptyThumb?: boolean;
  hideImages?: boolean;
}

export const NewsFeed: React.FC<NewsFeedProps> = ({
  hideEmptyThumb = false,
  hideImages = false,
  initialNewsItems,
  isInfinite = false,
  params = {},
  title,
}) => {
  const [items, setItems] = useState<News[]>(initialNewsItems ? initialNewsItems : []);
  const [page, setPage] = useState<number>(initialNewsItems ? 1 : 0);
  const [didLoad, setDidLoad] = useState<boolean>(!!initialNewsItems?.length);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const session = React.useContext(SessionContext);

  const loadNews = useCallback(() => {
    if (isLoading) {
      return;
    }
    setIsLoading(true);

    session
      .getManager(BasicNewsManager)
      .fetchNodes({
        ...params,
        page: page,
      })
      .then(res => {
        if (res?.ok) {
          let latestNews = [...items];
          latestNews = latestNews.concat(res.ok.filter((node: News) => node.author !== 'Benzinga Insights'));
          setItems(latestNews);
          setDidLoad(true);
          setIsLoading(false);
          setPage(page + 1);
        }
      });
  }, [isLoading, session, params, page, items]);

  useEffect(() => {
    if (!didLoad) {
      loadNews();
    }
  }, [didLoad, loadNews]);

  useEffect(() => {
    setItems(initialNewsItems ?? []);
    if (initialNewsItems) {
      setPage(1);
    }
  }, [initialNewsItems]);

  return (
    <NewsFeedWrapper>
      <NewsFeedHeader className="mb-4">
        <NewsFeedTitle className="news-feed-title">{title}</NewsFeedTitle>
      </NewsFeedHeader>
      {items &&
        items.map((node: News) => {
          const isSponsored = isSponsoredArticle(node);
          const href = getNodeUrl(node);
          return (
            <div className="newsfeed-card text-black" key={node.id}>
              <PostCard
                created={node.created}
                dateType="med"
                description={node.teaser}
                hideEmptyThumb={hideEmptyThumb}
                id={node.id}
                image={
                  hideImages
                    ? undefined
                    : {
                        alt: node.assets?.[0]?.alt || node.title || '',
                        url: formatImageUrl(node),
                      }
                }
                layout="feed"
                size="nano"
                sponsored={isSponsored}
                title={node.title}
                url={href}
              />
            </div>
          );
        })}
      <div className="feed-more-wrapper">
        {isLoading ? <Spinner /> : null}
        {!isLoading && didLoad ? (
          isInfinite ? (
            <FeedButton loadMore={loadNews}>Load More</FeedButton>
          ) : (
            <button className="load-more-button" onClick={loadNews} tabIndex={0}>
              Load More <Icon icon={faChevronDown} />
            </button>
          )
        ) : null}
      </div>
    </NewsFeedWrapper>
  );
};

const NewsFeedHeader = styled.div``;

const NewsFeedTitle = styled.h2`
  font-size: 24px;
  margin-bottom: 10px;
  font-weight: 700;
`;

const NewsFeedWrapper = styled.div`
  .newsfeed-card {
    .post-card {
      //margin-bottom: 12px;
    }

    .post-title {
      font-size: 18px;
      line-height: 26px;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
    }

    .post-teaser {
      color: #7c7c7c;
      font-weight: 400;
      font-size: 12px;
    }

    .post-card-image {
      position: relative;
      width: 100px !important;
      height: 100px !important;
      min-width: 100px !important;
      min-height: 100px !important;
      max-width: 100px !important;
      max-height: 100px !important;
    }
  }
  .feed-more-wrapper {
    align-items: center;
    display: flex;
    flex-direction: column;
    padding: 16px;

    .load-more-button {
      color: ${({ theme }) => theme.colorPalette.blue400};
      font-weight: ${({ theme }) => theme.fontWeight.bold};
      font-size: ${({ theme }) => theme.fontSize.sm};
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
`;

export default NewsFeed;
