'use client';
import React from 'react';
import styled from '@benzinga/themetron';
import { Ticker } from '@benzinga/ticker-ui';
import { StoryObject } from '@benzinga/advanced-news-manager';
import formatDate from 'date-fns/format';
import { InView } from 'react-intersection-observer';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import NewsElementCommentCount from '../NewsElementCommentCount';

export interface Brief extends StoryObject {
  isHighlighted?: boolean;
}

export interface BenzingaBriefNewsElementProps {
  className?: string;
  data: Brief;
  sidebarUnder?: React.ReactElement;
  type?: 'briefs' | 'exclusive-briefs';
  previewTextSelection?: 'body' | 'teaser';
  showCommentsIcon?: boolean;
}

export const BenzingaBriefNewsElement: React.FC<BenzingaBriefNewsElementProps> = ({
  className = '',
  data,
  previewTextSelection = 'teaser',
  showCommentsIcon = false,
  sidebarUnder,
}) => {
  const [isHighlighted, setIsHighlighted] = React.useState<boolean>(false);
  const [didImpress, setDidImpress] = React.useState<boolean>(false);

  React.useEffect(() => {
    if (data.isHighlighted) {
      setIsHighlighted(data.isHighlighted);
    }
  }, [data]);

  const visibilityChanged = React.useCallback(
    (isVisible: boolean) => {
      if (isVisible && !didImpress) {
        setDidImpress(true);
      } else if (!isVisible && didImpress) {
        setIsHighlighted(false);
        data.isHighlighted = false;
      }
    },
    [didImpress, data],
  );

  return (
    <BenzingaBriefNewsElementWrapper className={`benzinga-brief-block-wrapper ${isHighlighted ? 'highlight' : ''}`}>
      <InView onChange={visibilityChanged} skip={isHighlighted}>
        <BenzingaBriefNewsElementContainer className={`benzinga-brief-block ${previewTextSelection} ${className}`}>
          <div className="brief-sidebar-info">
            <div>
              {Array.isArray(data.stocks) && (
                <div className="ticker-list">
                  {data.stocks && data.stocks.map(stock => <Ticker key={stock.name} symbol={stock.name} />)}
                </div>
              )}
              {data.created && (
                <div className="brief-datetime">
                  <span className="brief-time">{formatDate(new Date(data.created), 'hh:mmaaa')} ET</span>
                  <span className="brief-date">{formatDate(new Date(data.created), 'MM/dd/yyyy')}</span>
                </div>
              )}
              {showCommentsIcon && (
                <div className="mt-2">
                  <NewsElementCommentCount id={data.nodeId} url={data.url} />
                </div>
              )}
            </div>
            <div className="brief-sidebar-info-under">{sidebarUnder}</div>
          </div>
          <div className="brief-main-info">
            <div className="benzinga-brief-block-header">
              <a dangerouslySetInnerHTML={{ __html: sanitizeHTML(data.title ?? '') }} href={data.url} />
            </div>
            {previewTextSelection === 'teaser' && data?.teaser && data?.teaser.trim() ? (
              <div className="brief-teaser" dangerouslySetInnerHTML={{ __html: sanitizeHTML(data.teaser) }} />
            ) : (
              data?.body && <div className="brief-body" dangerouslySetInnerHTML={{ __html: sanitizeHTML(data.body) }} />
            )}
            <a className="read-more" href={data.url}>
              Read More
            </a>
          </div>
        </BenzingaBriefNewsElementContainer>
      </InView>
    </BenzingaBriefNewsElementWrapper>
  );
};

const BenzingaBriefNewsElementWrapper = styled.div`
  padding: 0 20px;
  &.highlight {
    background-color: ${({ theme }) => theme.colorPalette.yellow50};
  }
`;

const BenzingaBriefNewsElementContainer = styled.div`
  &.benzinga-brief-block {
    display: flex;
    flex-direction: column;
    padding: 20px 0;

    .brief-body {
      > * {
        display: none;
      }
      ul:first-of-type {
        display: block;
        overflow-y: unset;
        li:nth-of-type(1n + 4) {
          display: none;
        }
      }
    }

    .brief-main-info {
      iframe {
        width: 100%;
        max-width: 560px;
      }
    }

    .benzinga-brief-block-header {
      margin-bottom: 15px;
      display: flex;
      flex-direction: column;

      a {
        color: ${({ theme }) => theme.colorPalette.blue800};
        font-size: ${({ theme }) => theme.fontSize['2xl']};
        font-weight: ${({ theme }) => theme.fontWeight.bold};

        &:hover {
          color: ${({ theme }) => theme.colorPalette.blue400};
        }
      }
    }

    .ticker-list {
      margin-bottom: 10px;
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }

    .brief-sidebar-info {
      margin-right: 20px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
    }

    ul {
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      height: 100%;

      > li {
        list-style-type: disc;
        list-style-position: inside;
        font-size: ${({ theme }) => theme.fontSize.base};
        margin-bottom: 10px;

        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }

    .brief-title {
      color: ${({ theme }) => theme.colorPalette.gray700};
      font-weight: ${({ theme }) => theme.fontWeight.semibold};
      cursor: pointer;
    }

    .brief-datetime {
      display: flex;
      font-weight: ${({ theme }) => theme.fontWeight.semibold};
    }

    .brief-time {
      color: ${({ theme }) => theme.colorPalette.gray900};
      font-size: ${({ theme }) => theme.fontSize.sm};
      margin-right: 10px;
    }

    .brief-date {
      color: ${({ theme }) => theme.colorPalette.gray900};
      font-size: ${({ theme }) => theme.fontSize.sm};
    }

    .read-more {
      display: block;
      margin-top: 0.4rem;
      color: ${({ theme }) => theme.colorPalette.blue500};
      font-weight: ${({ theme }) => theme.fontWeight.bold};
    }

    @media screen and (min-width: 1024px) {
      flex-direction: row;

      .brief-sidebar-info {
        max-width: 190px;
        min-width: 180px;
        flex-direction: column;
        justify-content: unset;
      }

      .brief-datetime {
        flex-direction: column;
      }

      .brief-time {
        margin-right: 0;
      }

      .benzinga-brief-block-header {
        line-height: 1.1;
      }
    }
  }
`;

export default BenzingaBriefNewsElement;
