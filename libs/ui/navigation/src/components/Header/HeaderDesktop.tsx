'use client';
import React from 'react';

import styled from '@benzinga/themetron';
import { ErrorBoundary, Icon } from '@benzinga/core-ui';
import { cryptoTickers } from '@benzinga/crypto-manager';
import { BreakingNews } from '@benzinga/basic-news-manager';
import Hooks, { NoFirstRender } from '@benzinga/hooks';
import { useIsUserLoggedIn } from '@benzinga/user-context';

import { faEllipsisH } from '@fortawesome/pro-regular-svg-icons/faEllipsisH';

import { TopBar } from '../TopBar';
import { QuoteBar } from './QuoteBar';
import { convertToRelativeUrl, featuredMenu } from './_mockMenu';
import { BreakingNewsBanner } from '../BreakingNewsBanner';
import { appEnvironment, appName } from '@benzinga/utils';
import { isMobile } from '@benzinga/device-utils';
import { faBars } from '@fortawesome/pro-light-svg-icons/faBars';
import { faXmark } from '@fortawesome/pro-light-svg-icons/faXmark';
import { NavigationHeaderProps } from '../../entities';
import { MenuInterface } from './interfaces';
import { NavigationLogo } from './NavigationLogo';
import { Menu, MenuItemLink, MenuItems, MoreMenuItemsDropdown } from './Menu';
import classNames from 'classnames';
import { AdBannerFallback } from './RotatingBannerFallback';

export { menus as MainMenu, DefaultQuotesList } from './_mockMenu';

const RaptiveHeaderBanner = React.lazy(() =>
  import('./RaptiveHeaderBanner').then(module => {
    return { default: module.RaptiveHeaderBanner };
  }),
);

const RotatingBanner = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RotatingBanner };
  }),
);

export interface NavigationMenus {
  primary: MenuInterface[];
  secondary: MenuInterface[];
}

export interface HeaderDesktopProps extends NavigationHeaderProps {
  breakingNews?: BreakingNews[];
}

export const HeaderDesktop = ({
  breakingNews,
  hideMenuBar,
  hideQuoteBar,
  hideTopBar,
  logoVariant,
  marketTickers,
  menus,
  moneyBannerLink,
  moneyBannerText,
  onSearch,
  quotes,
  recentTickers,
  shouldRenderRaptiveBanner,
  showRaptiveBanner,
  showRotatingBanner,
  sticky,
}: HeaderDesktopProps) => {
  const [isNavBarFixed, setIsNavBarFixed] = React.useState(false);
  const [isMoreItemsDropdownVisible, setIsMoreItemsDropdownVisible] = React.useState(false);
  const [breakingNewsVisible, setBreakingNewsVisible] = React.useState(false);
  const [hideRotatingBanner, setHideRotatingBanner] = Hooks.useLocalStorage('hideRotatingBanner', !showRotatingBanner);
  const hideRotatingBannerHydrated = Hooks.useHydrate(hideRotatingBanner, showRotatingBanner === false ? true : false);
  const desktopMenuWrapperRef = React.useRef<HTMLDivElement | null>(null);
  const raptiveHeaderBannerRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (window?.scrollY > 0) {
      if (!breakingNewsVisible && breakingNews) {
        setBreakingNewsVisible(true);
      }
    }
  }, [breakingNews, breakingNewsVisible]);

  Hooks.useEffectDidMount(() => {
    window?.addEventListener(
      'scroll',
      () => {
        if (!breakingNewsVisible && breakingNews) {
          setBreakingNewsVisible(true);
        }
      },
      { once: true },
    );
  });

  Hooks.useEventListener('scroll', () => {
    if (!sticky || isMobile()) return;
    //const navbarHeight = height ? height + 20 : 132;
    const scrollThreshold = 20;
    if (window.scrollY >= scrollThreshold && !isNavBarFixed) {
      setIsNavBarFixed(true);
    } else if (window.scrollY < scrollThreshold && isNavBarFixed) {
      setIsNavBarFixed(false);
    }
  });

  const tickers = logoVariant === 'crypto' ? cryptoTickers : marketTickers;

  const handleToggleMenuItemsDropdown = () => {
    setIsMoreItemsDropdownVisible(!isMoreItemsDropdownVisible);
  };

  const isMoneyApp = appEnvironment().isApp(appName.money);
  const isIndiaApp = appEnvironment().isApp(appName.india);

  const handleDismissAdBanner = () => {
    setHideRotatingBanner(true);
  };

  return (
    <>
      {isNavBarFixed ? <div className="navbar-placeholder"></div> : null}
      <DesktopMenuWrapper
        $isFixed={isNavBarFixed}
        className="desktop-menu-wrapper"
        id="navigation-header"
        ref={desktopMenuWrapperRef}
      >
        {showRotatingBanner && (
          <ErrorBoundary name="rotating-banner">
            <NoFirstRender fallback={AdBannerFallback}>
              {!hideRotatingBannerHydrated && (
                <React.Suspense fallback={AdBannerFallback}>
                  <RotatingBanner close={handleDismissAdBanner} fallback={AdBannerFallback} />
                </React.Suspense>
              )}
            </NoFirstRender>
          </ErrorBoundary>
        )}
        {shouldRenderRaptiveBanner && (
          <React.Suspense fallback={<div />}>
            <RaptiveHeaderBanner isHidden={!showRaptiveBanner} ref={raptiveHeaderBannerRef} />
          </React.Suspense>
        )}
        {!hideTopBar && (
          <ErrorBoundary name="navigation-top-bar">
            <TopBar items={featuredMenu} />
          </ErrorBoundary>
        )}
        <WhiteBar className="main-menu-bar" hideQuoteBar={hideQuoteBar} isIndiaApp={isIndiaApp}>
          <BarLayoutWidth className="main-menu-wrapper" isIndiaApp={isIndiaApp}>
            <BarContentLayout className="bar-content-layout">
              <ErrorBoundary name="benzinga-logo-wrapper">
                <NavigationLogo deviceType="desktop" logoVariant={logoVariant} />
              </ErrorBoundary>
              {!hideMenuBar && (
                <>
                  <PrimaryMenuWrapper className="primary-menu-wrapper">
                    <MenuItemContainer className="menu-item-container" isIndiaApp={isIndiaApp}>
                      {menus &&
                        menus.primary?.map((menu: MenuInterface, i: number) => {
                          return (
                            <li key={i}>
                              <Menu menu={menu} />
                            </li>
                          );
                        })}
                    </MenuItemContainer>
                  </PrimaryMenuWrapper>
                  {!isIndiaApp && !isMoneyApp ? (
                    <>
                      <button
                        aria-label="Click to open more navbar items"
                        className="more-nav-tabs-icon-container"
                        onClick={handleToggleMenuItemsDropdown}
                      >
                        <Icon className="text-2xl text-black" icon={faEllipsisH} />
                        {<MoreMenuItemsDropdown isVisible={isMoreItemsDropdownVisible} menus={menus} />}
                      </button>
                      <SecondaryMenuWrapper className="secondary-menu-wrapper">
                        {menus &&
                          menus.secondary
                            ?.filter((menuItem: MenuInterface) => menuItem?.deviceType !== 'mobile')
                            ?.map((menu: MenuInterface, i: number) => {
                              return <Menu key={i} menu={menu} secondary={true} />;
                            })}
                      </SecondaryMenuWrapper>
                    </>
                  ) : (
                    menus &&
                    menus.secondary &&
                    Array.isArray(menus.secondary) &&
                    menus.secondary.length > 0 && (
                      <SecondaryMenuWrapper className="secondary-menu-wrapper">
                        <TertiaryMenu menu={menus.secondary} />
                      </SecondaryMenuWrapper>
                    )
                  )}
                </>
              )}
            </BarContentLayout>
          </BarLayoutWidth>
        </WhiteBar>
        {!hideQuoteBar ? (
          <QuoteBar marketTickers={tickers} onInput={onSearch} quotes={quotes} recentTickers={recentTickers} />
        ) : null}
        <ErrorBoundary name="breaking-news-banner">
          {breakingNews && breakingNews?.length > 0 && (
            <div
              className={classNames('breaking-news-wrapper', { 'breaking-news-wrapper--visible': breakingNewsVisible })}
            >
              <BreakingNewsBanner data={breakingNews} />
            </div>
          )}
          {isMoneyApp && moneyBannerLink && moneyBannerText ? (
            <MoneyBreaking className="money-breaking">
              <a href={moneyBannerLink}>{moneyBannerText}</a>
            </MoneyBreaking>
          ) : null}
        </ErrorBoundary>
      </DesktopMenuWrapper>
    </>
  );
};

const MoneyBreaking = styled.div`
  background: #6f9bdb;
  text-align: center;
  font-size: 18px;
  padding: 4px;
  text-decoration: underline;
  a {
    color: white;
  }
`;

export const TertiaryMenu: React.FC<{ menu: MenuInterface[] }> = ({ menu }) => {
  const [menuVisible, setMenuVisible] = React.useState(false);
  const isLoggedIn = Hooks.useHydrate(useIsUserLoggedIn(), false);
  return (
    <TertiaryMenuWrap className="money-menu-wrap">
      <div className="menu-icon-wrapper">
        <div className="money-menu-toggle" onClick={() => setMenuVisible(!menuVisible)}>
          <span>Menu</span>
          <Icon icon={menuVisible ? faXmark : faBars} />
        </div>
      </div>
      <div className={`tertiary-menu-items ${menuVisible ? 'show' : ''}`}>
        {menu &&
          menu.map((menuItem: MenuInterface, i: number) => {
            return (
              <div className="money-toggle-wrap" key={i}>
                <MenuItemLink
                  className={'parent-menu-item'}
                  data-action="Navigation Main Menu Click"
                  href={convertToRelativeUrl(
                    isLoggedIn && menuItem.logged_in_href ? isLoggedIn && menuItem.logged_in_href : menuItem.href,
                  )}
                  title={menuItem.label}
                >
                  {menuItem.label}
                </MenuItemLink>
                {menuItem.subnav ? (
                  <div className="sub-nav-items">
                    <MenuItems items={menuItem.subnav.links} />
                  </div>
                ) : null}
              </div>
            );
          })}
      </div>
    </TertiaryMenuWrap>
  );
};

const TertiaryMenuWrap = styled.div`
  &.money-menu-wrap {
    position: relative;
    .menu-icon-wrapper {
      display: flex;
      align-items: center;
      height: 100%;
      .money-menu-toggle {
        display: flex;
        align-items: center;
        cursor: pointer;
        border: 1px solid #395173;
        border-radius: ${({ theme }) => theme.borderRadius.md};
        height: auto;
        padding: 8px 14px 6px;
        color: ${({ theme }) => theme.colorPalette.white};
        &:hover {
          background: initial;
          color: ${({ theme }) => theme.colorPalette.white};
        }
        span {
          font-weight: ${({ theme }) => theme.fontWeight.semibold};
          display: inline-block;
          padding-right: 18px;
          line-height: 1;
        }
      }
    }
    .tertiary-menu-items {
      position: absolute;
      z-index: 99;
      right: 0;
      min-width: 370px;
      background-color: #f2f8ff;
      display: none;
      scrollbar-gutter: stable;
      &.show {
        display: block;
      }
      .money-toggle-wrap {
        a {
          color: #192940;
          font-weight: bold;
        }
        .parent-menu-item {
          display: inline-block;
          width: 160px;
          line-height: 2.8em;
          border-right: 1px solid #e1ebfa;
        }
        .sub-nav-items {
          position: absolute;
          top: 0;
          margin-left: calc(100% - 212px);
          border-left: 1px solid #e1ebfa;
          display: none;
          overflow-y: auto;
          max-height: 100%;
          scrollbar-gutter: stable;
          ::-webkit-scrollbar {
            width: 6px;
          }
          ::-webkit-scrollbar-track {
            background: #f1f1f1;
          }
          ::-webkit-scrollbar-thumb {
            background: #888;
          }
          ::-webkit-scrollbar-thumb:hover {
            background: #555;
          }
          a {
            font-size: 14px;
            font-weight: 500;
          }
        }
        &:hover {
          .sub-nav-items {
            display: block;
          }
        }
      }
    }
  }
`;

const MenuItemContainer = styled.ul<{ isIndiaApp?: boolean }>`
  display: flex;
  overflow-x: scroll;
  scrollbar-width: none;
  ::-webkit-scrollbar {
    display: none;
  }
  justify-content: ${({ isIndiaApp }) => (isIndiaApp ? 'flex-end' : 'flex-start')};

  li {
    flex-shrink: 0;

    &:nth-of-type(3),
    &:nth-of-type(4),
    &:nth-of-type(5),
    &:nth-of-type(6),
    &:nth-of-type(7),
    &:nth-of-type(8),
    &:nth-of-type(9),
    &:nth-of-type(10),
    &:nth-of-type(11),
    &:nth-of-type(12) {
      display: none;
    }

    @media screen and (min-width: 850px) {
      &:nth-of-type(3) {
        display: initial;
      }
    }

    @media screen and (min-width: 900px) {
      &:nth-of-type(4) {
        display: initial;
      }
    }

    @media screen and (min-width: 950px) {
      &:nth-of-type(5) {
        display: initial;
      }
    }

    @media screen and (min-width: 1000px) {
      &:nth-of-type(6) {
        display: initial;
      }
    }

    @media screen and (min-width: 1100px) {
      &:nth-of-type(7) {
        display: initial;
      }
    }

    @media screen and (min-width: 1130px) {
      &:nth-of-type(8) {
        display: initial;
      }
    }

    @media screen and (min-width: 1190px) {
      &:nth-of-type(9) {
        display: initial;
      }
    }

    @media screen and (min-width: 1250px) {
      &:nth-of-type(10) {
        display: initial;
      }
    }

    @media screen and (min-width: 1315px) {
      &:nth-of-type(11) {
        display: initial;
      }
    }

    @media screen and (min-width: 1360px) {
      &:nth-of-type(12) {
        display: initial;
      }
    }
  }
`;

const DesktopMenuWrapper = styled.div<{ $isFixed?: boolean }>`
  display: block;
  position: ${({ $isFixed }) => ($isFixed ? 'fixed' : 'unset')};
  top: ${({ $isFixed }) => ($isFixed ? '0' : 'unset')};
  width: ${({ $isFixed }) => ($isFixed ? '100%' : 'unset')};
  //z-index: ${({ $isFixed }) => ($isFixed ? '100' : 'unset')};
  z-index: 999;
  box-shadow: ${({ $isFixed, theme }) => ($isFixed ? theme.shadow.lg : 'unset')};

  @media (max-width: 800px) {
    display: none;
  }

  .breaking-news-banner {
    position: absolute;
    top: -50px;
    transition: all ease-in 0.5s;
    z-index: -1;
  }

  .breaking-news-wrapper {
    position: relative;

    &--visible {
      .breaking-news-banner {
        position: ${({ $isFixed }) => ($isFixed ? 'absolute' : 'unset')};
        top: 0;
      }
    }
  }

  .more-nav-tabs-icon-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
    margin-right: 5px;
    cursor: pointer;

    &:hover {
      > div {
        display: flex;
      }
    }

    @media screen and (min-width: 1315px) {
      display: none !important;
    }
  }
`;

const WhiteBar = styled.div<{ hideQuoteBar?: boolean; isIndiaApp?: boolean }>`
  background-color: ${({ theme }) => theme.colorPalette.white};
  border-bottom: ${props => (props.hideQuoteBar && !props.isIndiaApp ? '1px' : '0px')} solid
    ${({ theme }) => theme.colorPalette.gray300};
  width: 100%;
  padding: 0;
`;

const BarLayoutWidth = styled.div<{ isIndiaApp?: boolean }>`
  height: 48px;
  max-width: ${({ isIndiaApp }) => (isIndiaApp ? '1250px' : '1400px')};
  margin: 0 auto;
`;

export const PremiumBtn = styled.a`
  background: ${({ theme }) => theme.colorPalette.orange500};
  color: ${({ theme }) => theme.colorPalette.white};
  text-transform: uppercase;
  font-family: Manrope, Manrope-fallback, sans-serif;
  font-weight: ${({ theme }) => theme.fontWeight.bold};
  font-size: ${({ theme }) => theme.fontSize.sm};
  line-height: ${({ theme }) => theme.lineHeight[5]};
  padding: 14px 22px;
  text-align: center;
  display: block;

  &:hover {
    background: ${({ theme }) => theme.colorPalette.blue700};
  }
`;

export const PrimaryNavButton = styled.a<{ color: string }>`
  background: ${({ color }) => color};
  color: white;
  font-size: ${({ theme }) => theme.fontSize.sm};
  font-weight: bold;
  opacity: 0.9;
  padding: ${({ theme }) => theme.space[3]} ${({ theme }) => theme.space[4]};
  text-transform: uppercase;
  vertical-align: middle;
  line-height: ${({ theme }) => theme.space[6]};

  &:hover {
    opacity: 1;
  }
`;

export const BarContentLayout = styled.div`
  display: flex;
  position: relative;
  height: 100%;
  justify-content: center;
`;

export const PrimaryMenuWrapper = styled.div`
  flex-grow: 1;
  margin-left: 20px;
`;

export const SecondaryMenuWrapper = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: flex-end;
  height: 48px;
  min-width: 244px;
`;
