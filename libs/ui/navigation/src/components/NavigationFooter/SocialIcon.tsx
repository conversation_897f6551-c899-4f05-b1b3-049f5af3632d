import React from 'react';
import { Icon } from '@benzinga/core-ui';
import { SocialSite } from './footerEntity';
import styled from '@benzinga/themetron';

const StyledAnchor = styled.a<{ color?: string }>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 0.25rem;
  font-size: ${({ theme }) => theme.fontSize['3xl']};
  color: ${({ theme }) => theme.colorPalette.white};
  -webkit-transition:
    color 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94),
    color 200ms ease;
  transition:
    color 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94),
    color 200ms ease;
  &:hover {
    color: ${props => props.color};
  }
  svg {
    fill: #ffffff;
  }
`;

interface SocialIconProps extends SocialSite {
  isAmp?: boolean;
}

const SocialIcon: React.FC<SocialIconProps> = ({ color, icon, isAmp, link, name }) => {
  return (
    <StyledAnchor
      aria-label={`Click to check out <PERSON><PERSON>'s ${name}`}
      className={name}
      color={isAmp ? undefined : color}
      href={link}
      rel="noopener noreferrer"
      target="_blank"
    >
      <Icon icon={icon} />
    </StyledAnchor>
  );
};
export default SocialIcon;
