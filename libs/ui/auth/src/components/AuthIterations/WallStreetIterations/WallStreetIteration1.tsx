import React, { FC, memo } from 'react';
import Link from 'next/link';
import { FaCheck } from 'react-icons/fa6';
import { FiX } from 'react-icons/fi';

import { BzImage } from '@benzinga/image';
import { AuthIterationType } from '../types';
import { wallStreetCopy } from '../utils';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import { usePaywallTracking } from '../../../hooks/usePaywallTracking';
import {
  buildCheckoutUrl,
  getPaywallConfig,
  getPaywallPoints,
  handlePaywallLoginRedirect,
} from '../../../utils/paywallUtils';

export const WallStreetIteration1: FC<AuthIterationType> = memo(
  ({ allowClose, contentType, placement, setShowPaywall }) => {
    const { ad, hasAltCheckout, paywallCopy, utmSource } = getPaywallConfig(
      contentType,
      wallStreetCopy,
      'wall-street-1',
      'pricetag',
    );

    const campaign = contentType ? 'wallstreetadvantage' : 'wallstreetinsights';

    const checkoutUrl = buildCheckoutUrl({
      ad,
      adType: 'paywall',
      campaign,
      hasAltCheckout,
    });

    const points = getPaywallPoints(paywallCopy, 'STANDARD');

    const isLoggedIn = useIsUserLoggedIn();

    usePaywallTracking({
      allowClose,
      checkoutUrl,
      placement,
      utmSource,
    });

    const handleLoginRedirect = () => handlePaywallLoginRedirect(utmSource);

    return (
      <>
        <div className="fixed inset-0 z-[1000002] touch-none pointer-events-none"></div>
        <div className="paywall-content w-full max-h-[100vh] h-auto animate-fade-up animate-delay-300 animate-once no-scrollbar overflow-scroll flex flex-col md:flex-row items-center md:items-start justify-start md:justify-center md:gap-12 fixed z-[1000003] bottom-0 left-0 bg-white border-t">
          {allowClose && setShowPaywall && (
            <button
              className="absolute top-4 right-4 text-black md:text-white z-10"
              onClick={() => {
                setShowPaywall(false);
              }}
            >
              <FiX />
            </button>
          )}
          <Link className="w-full flex flex-col md:flex-row overflow-hidden" href={checkoutUrl} target="_blank">
            <div className="w-full lg:w-fit ml-0 md:ml-auto">
              <div className="ml-2 border-[#F2F2F2BF] border border-t-0 min-h-[232px] lg:min-w-[500px] pr-8">
                <div className="flex flex-col md:flex-row items-start md:items-center p-2 border-b border-[#F2F2F2BF]">
                  <div className="w-32 mr-1">
                    <BzImage height="19px" src="/next-assets/images/bz-edge-logo-dark.svg" width="170px" />
                  </div>
                  <div className="flex flex-row items-center md:items-center justify-between w-full flex-wrap">
                    <div className="text-[#192940] font-bold text-xs">
                      Subscriber-Only{' '}
                      {placement === 'article'
                        ? 'Article'
                        : paywallCopy?.subscriberType
                          ? paywallCopy.subscriberType
                          : 'Content'}
                    </div>
                    {!isLoggedIn && (
                      <div
                        className="text-bzblue-700 text-xs z-1 border-none md:border-l border-slategray"
                        onClick={handleLoginRedirect}
                      >
                        Already a member? Login
                      </div>
                    )}
                  </div>
                </div>
                <div className="px-4 py-6 h-full">
                  <span className="text-[#3F83F8] tracking-widest font-extrabold">
                    {paywallCopy?.subhead ?? 'WALL STREET LEVEL INTEL'}
                  </span>
                  <div
                    className="text-4xl py-2 font-extrabold text-[#192940]"
                    dangerouslySetInnerHTML={{
                      __html: paywallCopy?.headline ?? 'Without The <br /> Wall Street Price Tag',
                    }}
                  ></div>
                  <span className="text-[#3F83F8] text-sm font-extrabold">{paywallCopy?.text ?? 'LEARN MORE'}</span>
                </div>
              </div>
            </div>
            <div className="bg-[#3b82f6] p-8 relative w-full lg:w-1/2 flex flex-row justify-center md:justify-start">
              <SectionDivider />
              <div className="bg-white p-4 rounded-sm relative w-fit">
                <div className="w-full absolute -top-2 left-0">
                  <div className="bg-[#F57F36] px-1 text-sm font-extrabold text-white mx-auto w-fit">
                    members receive
                  </div>
                </div>
                <div className="flex flex-col items-center justify-center gap-4 pt-2 px-8 h-full">
                  <ul className="list-none pb-4 text-sm font-extrabold">
                    {points.map((item: string, index: number) => {
                      return (
                        <li className="flex items-center gap-2 text-[#192940]" key={index}>
                          <FaCheck className="text-bzblue-700" />
                          {item}
                        </li>
                      );
                    })}
                  </ul>
                </div>
                <div className="w-full absolute -bottom-4 left-0">
                  <div className="bg-[#F57F36] px-4 py-2 text-white text-sm font-extrabold mx-auto w-fit uppercase animate-shake animate-delay-[3000ms]">
                    unlock access now
                  </div>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </>
    );
  },
);

WallStreetIteration1.displayName = 'WallStreetIteration1';

const SectionDivider: React.FC = () => {
  return (
    <div className="diagonal absolute right-0 -top-[20px] md:-left-[60px] md:top-0">
      <style>{`
        .diagonal {
          width: 61px;
          height: 500px;
          background-color: #3b82f6;

          clip-path: polygon(100% 0, 0 100%, 100% 100%);
        }
        @media (max-width: 768px) {
          .diagonal {
            height: 21px;
            width: 100vw;
          }
        }
      `}</style>
    </div>
  );
};
