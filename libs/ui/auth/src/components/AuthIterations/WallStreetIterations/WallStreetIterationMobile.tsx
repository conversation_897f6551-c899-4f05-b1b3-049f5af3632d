import { FC, memo } from 'react';
import { FaCheck } from 'react-icons/fa6';
// import { RxLockClosed } from 'react-icons/rx';
import { useDrag } from '@use-gesture/react';
import { useSpring, animated } from '@react-spring/web';

import { BzImage } from '@benzinga/image';
import { AuthIterationType } from '../types';
import { wallStreetCopy } from '../utils';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import { usePaywallTracking } from '../../../hooks/usePaywallTracking';
import {
  buildCheckoutUrl,
  getPaywallConfig,
  getPaywallPoints,
  handlePaywallLoginRedirect,
} from '../../../utils/paywallUtils';

export const WallStreetIterationMobile: FC<AuthIterationType> = memo(
  ({ allowClose, contentType, placement, setShowPaywall }) => {
    const { ad, hasAltCheckout, paywallCopy, utmSource } = getPaywallConfig(
      contentType,
      wallStreetCopy,
      'wall-street-mobile',
      'insights',
    );

    const checkoutUrl = buildCheckoutUrl({
      ad,
      adType: 'paywall',
      campaign: 'wallstreetadvantage',
      hasAltCheckout,
    });

    const points = getPaywallPoints(paywallCopy, 'MOBILE_AD_LIGHT');

    const isLoggedIn = useIsUserLoggedIn();
    const [{ y }, api] = useSpring(() => ({ y: 0 }));
    const modalHeight = document.getElementsByClassName('paywall-content')?.[0]?.clientHeight ?? 466;

    usePaywallTracking({
      allowClose,
      checkoutUrl,
      placement,
      utmSource,
    });

    const handleLoginRedirect = () => handlePaywallLoginRedirect(utmSource);

    const bind = useDrag(
      ({ direction: [, dy], down, movement: [, my], velocity: [, vy] }) => {
        if (!allowClose || !setShowPaywall) return;

        const threshold = modalHeight * 0.6;
        if (!down && my > threshold) {
          setShowPaywall(false);
        } else {
          api.start({ config: { velocity: vy * dy }, immediate: down, y: down ? my : 0 });
        }
      },
      { bounds: { top: 0 }, filterTaps: true, from: () => [0, y.get()] },
    );

    return (
      <>
        <div className="fixed inset-0 z-[9998] touch-none pointer-events-none"></div>
        <animated.a
          {...bind()}
          className="paywall-content w-full h-auto animate-fade-up animate-delay-300 animate-once no-scrollbar overflow-scroll fixed z-[1000003] bottom-0 left-0 bg-white rounded-t-[20px]"
          href={checkoutUrl}
          rel="noreferrer"
          style={{
            bottom: y.to(y => {
              return y > 0 ? -y : 0;
            }),
            touchAction: 'none',
            y,
          }}
          target="_blank"
        >
          <div className="flex flex-col items-center justify-center text-center px-4">
            {allowClose && setShowPaywall && <div className="border border-[#CEDDF2] w-32 mt-2"></div>}
            <div className="w-32 mr-1 mt-2">
              <BzImage height="19px" src="/next-assets/images/bz-edge-logo-dark.svg" width="170px" />
            </div>
            <div
              className="text-3xl py-2 font-extrabold text-[#192940]"
              dangerouslySetInnerHTML={{
                __html: paywallCopy?.headline ?? 'Unlock Wall Street <br/> Level Insights',
              }}
            ></div>
            <div className="text-xl text-[#5B7292] mb-1">
              for less than <span className="text-[#479CFF]">$11 / Month</span>
            </div>
          </div>
          <div className="bg-[#3b82f6] px-2 relative w-full flex flex-col justify-center md:justify-start">
            <div className="uppercase text-sm font-extrabold text-white mx-auto w-fit tracking-widest py-1">
              members receive
            </div>
            <div className="bg-white p-2 rounded-sm relative">
              <ul className="list-none pb-2 text-sm font-bold">
                {points.map((item: string, index: number) => {
                  return (
                    <li className="flex items-center gap-2 text-[#192940] font-bold" key={index}>
                      <FaCheck className="text-bzblue-700 bg-[#E1EBFA] rounded-md p-1" size={18} />
                      {item}
                    </li>
                  );
                })}
              </ul>
              <button className="bg-[#F27238] px-4 py-2 text-white font-extrabold w-full rounded-[4px] uppercase text-center block">
                unlock access now
              </button>
              <div className="text-xs text-[#5B7292] tracking-widest uppercase font-bold mx-auto text-center mt-2">
                2025 special - save 43% - cancel anytime
              </div>
            </div>
            {!isLoggedIn && (
              <div className="text-sm text-[#E1EBFA] flex flex-row justify-between items-center py-1 px-4">
                <span>Already a Member?</span>
                <button className="uppercase font-extrabold" onClick={handleLoginRedirect}>
                  login
                </button>
              </div>
            )}
          </div>
        </animated.a>
      </>
    );
  },
);

WallStreetIterationMobile.displayName = 'WallStreetIterationMobile';
