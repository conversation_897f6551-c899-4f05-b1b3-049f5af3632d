import { FC, memo } from 'react';
import { FaCheck } from 'react-icons/fa6';
import { useDrag } from '@use-gesture/react';
import { useSpring, animated } from '@react-spring/web';

import { BzImage } from '@benzinga/image';
import { AuthIterationType } from '../types';
import { wallStreetCopy } from '../utils';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import { usePaywallTracking } from '../../../hooks/usePaywallTracking';
import {
  buildCheckoutUrl,
  getPaywallConfig,
  getPaywallPoints,
  handlePaywallLoginRedirect,
} from '../../../utils/paywallUtils';

export const WallStreetIterationMobile2: FC<AuthIterationType> = memo(
  ({ allowClose, contentType, placement, setShowPaywall }) => {
    const { ad, hasAltCheckout, paywallCopy, utmSource } = getPaywallConfig(
      contentType,
      wallStreetCopy,
      'wall-street-mobile-2',
      'marketmoves',
    );

    const checkoutUrl = buildCheckoutUrl({
      ad,
      adType: 'paywall',
      campaign: 'volatility',
      hasAltCheckout,
    });

    const points = getPaywallPoints(paywallCopy, 'MOBILE_DISCOUNT');

    const isLoggedIn = useIsUserLoggedIn();
    const [{ y }, api] = useSpring(() => ({ y: 0 }));
    const modalHeight = document.getElementsByClassName('paywall-content')?.[0]?.clientHeight ?? 466;

    usePaywallTracking({
      allowClose,
      checkoutUrl,
      placement,
      utmSource,
    });

    const handleLoginRedirect = () => handlePaywallLoginRedirect(utmSource);

    const bind = useDrag(
      ({ direction: [, dy], down, movement: [, my], velocity: [, vy] }) => {
        if (!allowClose || !setShowPaywall) return;

        const threshold = modalHeight * 0.6;
        if (!down && my > threshold) {
          setShowPaywall(false);
        } else {
          api.start({ config: { velocity: vy * dy }, immediate: down, y: down ? my : 0 });
        }
      },
      { bounds: { top: 0 }, filterTaps: true, from: () => [0, y.get()] },
    );

    return (
      <>
        <div className="fixed inset-0 z-[9998] touch-none pointer-events-none"></div>
        <animated.a
          {...bind()}
          className="paywall-content w-full h-auto animate-fade-up animate-delay-300 animate-once no-scrollbar overflow-scroll fixed z-[1000003] bottom-0 left-0 bg-white rounded-t-[20px]"
          href={checkoutUrl}
          rel="noreferrer"
          style={{
            bottom: y.to(y => {
              return y > 0 ? -y : 0;
            }),
            touchAction: 'none',
            y,
          }}
          target="_blank"
        >
          <div className="flex flex-col items-center justify-center text-center px-4">
            {allowClose && setShowPaywall && <div className="border border-[#CEDDF2] w-32 mt-2"></div>}
            <div className="w-28 mr-1 mt-2">
              <BzImage height="17px" src="/next-assets/images/bz-edge-logo-dark.svg" width="170px" />
            </div>
            <div className="text-base uppercase font-bold text-[#225AA9] tracking-widest my-1">
              {paywallCopy?.subhead ?? 'WHEN THE MARKET MOVES'}
            </div>
            <div
              className="text-2xl font-extrabold text-[#192940] mb-4"
              dangerouslySetInnerHTML={{
                __html: paywallCopy?.headline ?? 'EDGE MEMBERS ALREADY <br/> KNOW WHAT TO DO',
              }}
            ></div>
          </div>
          <div className="bg-[#0B2040] relative w-full flex flex-col justify-center md:justify-start">
            <div className="uppercase text-sm font-extrabold text-white mx-auto w-fit tracking-widest py-1 border-b border-[#B4B4B4] w-full text-center">
              join to receive
            </div>
            <div className="p-4 rounded-sm relative">
              <ul className="list-none pb-2 text-base">
                {points.map((item: string, index: number) => {
                  return (
                    <li className="flex items-center gap-2 text-[#E3E3E3] mb-1" key={index}>
                      <FaCheck className="text-[#225AA9] bg-white rounded-md p-1" size={18} />
                      {item}
                    </li>
                  );
                })}
              </ul>
              <button className="bg-[#225AA9] px-4 py-2 text-white font-extrabold w-full rounded-[4px] uppercase text-center block">
                unlock access now
              </button>
            </div>
          </div>
          {!isLoggedIn && (
            <div className="bg-white text-sm text-[#5B7292] flex flex-row justify-between items-center py-1 px-4">
              <span>Already a Member?</span>
              <button className="uppercase font-extrabold text-[#225AA9]" onClick={handleLoginRedirect}>
                login
              </button>
            </div>
          )}
        </animated.a>
      </>
    );
  },
);

WallStreetIterationMobile2.displayName = 'WallStreetIterationMobile2';
