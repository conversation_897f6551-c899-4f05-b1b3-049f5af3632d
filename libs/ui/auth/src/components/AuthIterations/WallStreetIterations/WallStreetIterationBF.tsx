import React, { FC } from 'react';
import Link from 'next/link';
import { FaCheck } from 'react-icons/fa6';
import { FiX } from 'react-icons/fi';

import { BzImage } from '@benzinga/image';
import { AuthIterationType } from '../types';
import { wallStreetCopy } from '../utils';
import { usePaywallTracking } from '../../../hooks/usePaywallTracking';
import { getPaywallConfig, getPaywallPoints } from '../../../utils/paywallUtils';

import styles from './wallstreet.module.scss';

export const WallStreetIterationBF: FC<AuthIterationType> = ({
  allowClose,
  contentType,
  placement,
  setShowPaywall,
}) => {
  const { paywallCopy, utmSource } = getPaywallConfig(
    contentType,
    wallStreetCopy,
    'wall-street-black-friday',
    'blackfriday',
  );

  const checkoutUrl = `https://www.benzinga.com/premium/ideas/benzinga-edge-checkout-black-friday-24/?utm_source=${utmSource}`;

  const points = getPaywallPoints(paywallCopy, 'STANDARD');

  usePaywallTracking({
    allowClose,
    checkoutUrl,
    placement,
    utmSource,
  });

  return (
    <>
      <div className="fixed inset-0 z-[1000002] touch-none pointer-events-none"></div>
      <div className="paywall-content w-full max-h-[100vh] h-auto animate-fade-up animate-delay-300 animate-once no-scrollbar overflow-scroll flex flex-col md:flex-row items-center md:items-start justify-start md:justify-center md:gap-12 fixed z-[1000003] bottom-0 left-0 bg-white shadow-[0_-48px_200px_0_rgba(25,41,64,0.94)]">
        {allowClose && setShowPaywall && (
          <button
            className="absolute top-4 right-4 text-black md:text-white z-10"
            onClick={() => {
              setShowPaywall(false);
            }}
          >
            <FiX />
          </button>
        )}
        <Link className="w-full flex flex-col md:flex-row overflow-hidden" href={checkoutUrl} target="_blank">
          <div className="w-full lg:w-fit ml-0 md:ml-auto">
            <div className="ml-2 border-[#F2F2F2BF] border border-t-0 border-r-0 min-h-[232px] lg:min-w-[500px] pr-8">
              <div className="flex flew-col items-center p-2 border-b border-[#F2F2F2BF]">
                <div className="w-32 mr-1">
                  <BzImage height="19px" src="/next-assets/images/bz-edge-logo-dark.svg" width="170px" />
                </div>
                <div className="text-[#192940] font-bold text-xs md:font-base">
                  Subscriber-Only{' '}
                  {placement === 'article'
                    ? 'Article'
                    : paywallCopy?.subscriberType
                      ? paywallCopy.subscriberType
                      : 'Content'}
                </div>
              </div>
              <div className="px-4 py-6 h-full">
                <span className="text-[#ED5735] tracking-widest font-extrabold">
                  {paywallCopy?.subhead ?? 'WALL STREET LEVEL INTEL'}
                </span>
                <div
                  className="text-4xl py-2 font-extrabold text-[#192940]"
                  dangerouslySetInnerHTML={{
                    __html: paywallCopy?.headline ?? 'Without The <br /> Wall Street Price Tag',
                  }}
                ></div>
                <span className="text-[#ED5735] text-sm font-extrabold">
                  LOCK IN OUR LOWEST PRICE OF THE YEAR AND SAVE 75%
                </span>
              </div>
            </div>
          </div>
          <div
            className={'p-8 relative w-full lg:w-1/2 flex flex-row justify-center md:justify-start ' + styles.leftSide}
          >
            <SectionDivider />
            <div className={styles.leftBalloons}>
              <BzImage
                height="250px"
                objectPosition="right bottom"
                src="/next-assets/images/banners/blackfriday-pw-balloons.png"
                width="300px"
              ></BzImage>
            </div>
            <div className={styles.rightBalloons}>
              <BzImage
                height="250px"
                objectPosition="right bottom"
                src="/next-assets/images/banners/blackfriday-pw-balloons.png"
                width="300px"
              ></BzImage>
            </div>
            <div className={'bg-white p-4 rounded-sm relative w-fit ' + styles.pointsWrapper}>
              <div className="w-full absolute -top-2 left-0">
                <div className="bg-[#ED5735] px-1 text-sm font-extrabold text-white mx-auto w-fit">members receive</div>
              </div>
              <ul className="list-none pb-4 text-sm font-extrabold">
                {points.map((item: string, index: number) => {
                  return (
                    <li className="flex items-center gap-2 text-[#192940]" key={index}>
                      <FaCheck className="text-bzblue-700" />
                      {item}
                    </li>
                  );
                })}
              </ul>
              <div className="w-full absolute -bottom-4 left-0">
                <div className="bg-[#ED5735] px-4 py-2 text-white text-sm font-extrabold mx-auto w-fit uppercase animate-shake animate-delay-[3000ms]">
                  unlock access now
                </div>
              </div>
            </div>
          </div>
        </Link>
      </div>
    </>
  );
};

const SectionDivider: React.FC = () => {
  return (
    <div className="diagonal absolute right-0 -top-[20px] md:left-0 md:top-0">
      <style jsx>{`
        .diagonal {
          width: 61px;
          height: 400px;
          background-color: white;

          clip-path: polygon(0 0, 0% 100%, 100% 0);
        }
        @media (max-width: 768px) {
          .diagonal {
            display: none;
          }
        }
      `}</style>
    </div>
  );
};
