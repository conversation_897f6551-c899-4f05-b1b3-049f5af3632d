import { Impression } from '@benzinga/analytics';
import { BzEdgeLight } from '@benzinga/themed-icons';
import { styled } from '@benzinga/themetron';
import { FaCheckCircle } from 'react-icons/fa';
import { IoRemoveOutline } from 'react-icons/io5';

export interface UpsellProps {
  onClose?: () => void;
  utmSource?: string;
}

export const Upsell = ({ onClose, utmSource }: UpsellProps) => {
  const openUpsell = () => {
    window.open(
      `https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_adType=newaccountreg&utm_ad=${utmSource ?? 'newacct-reg-path-step1'}`,
      '_blank',
    );
  };

  const handleContinue = () => {
    onClose && onClose();
  };

  return (
    <Impression campaign_id="benzinga-edge-checkout" unit_type="newaccountreg">
      <div>
        <div className="mb-4">
          <div className="mb-4 w-full max-w-[240px]">
            <BzEdgeLight />
          </div>
          <h1 className="text-white capitalize text-xl">Get Our Top Trade Ideas Delivered Daily</h1>
        </div>
        <p>
          Benzinga Edge is your secret weapon for smarter investing. With real-time data, expert analysis, and premium
          tools, you'll make more informed decisions to spot opportunities faster. Plus, enjoy an ad-light experience
          that lets you focus on what matters—your portfolio.
        </p>
        <TableWrapper>
          <table className="table-auto w-full">
            <thead>
              <tr>
                <th></th>
                <th>Free Account</th>
                <th className="border-2 border-[#3F83F8] border-b-0 rounded-t-lg">Edge Account</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Articles</td>
                <td className="text-center">Limited Access</td>
                <td className="text-center border-x-2 border-[#3F83F8]">Unlimited</td>
              </tr>
              <tr>
                <td>Portfolio Page</td>
                <td>
                  <FaCheckCircle />
                </td>
                <td className="border-x-2 border-[#3F83F8]">
                  <FaCheckCircle />
                </td>
              </tr>
              <tr>
                <td>Ad-Light Reading</td>
                <td>
                  <IoRemoveOutline />
                </td>
                <td className="border-x-2 border-[#3F83F8]">
                  <FaCheckCircle />
                </td>
              </tr>
              <tr>
                <td>Premium Content</td>
                <td>
                  <IoRemoveOutline />
                </td>
                <td className="border-x-2 border-[#3F83F8]">
                  <FaCheckCircle />
                </td>
              </tr>
              <tr>
                <td>Premium Tools</td>
                <td>
                  <IoRemoveOutline />
                </td>
                <td className="border-2 border-[#3F83F8] rounded-b-md border-t-0 mr-1">
                  <FaCheckCircle />
                </td>
              </tr>
            </tbody>
          </table>
        </TableWrapper>
        <div className="border border-[#3F83F8] bg-[#0383F840] p-2 rounded-md text-center my-8">
          Get a limited time 65% off Founding Member discount when you join today.
        </div>
        <div className="flex justify-between gap-20 md:gap-4 ">
          <button className="bg-bzorange-500 text-white px-4 md:px-8 py-2 rounded-sm" onClick={openUpsell}>
            Get Access Now
          </button>
          <button className="capitalize hover:underline font-semibold" onClick={handleContinue}>
            Or Continue Reading with Ads
          </button>
        </div>
      </div>
    </Impression>
  );
};

const TableWrapper = styled.div`
  border-radius: 0.5rem;
  background-color: #f2f8ff;
  overflow: hidden;
  color: #395173;
  margin: 1.5rem 0;
  font-size: 14px;

  tbody {
    border: 1px solid #ceddf2;
    border-radius: 0.5rem;

    tr {
      border-bottom: 1px solid #ceddf2;
    }
    tr:nth-child(odd) {
      background-color: white;
    }
  }

  td {
    padding: 0.25rem 0.5rem;
  }

  th {
    padding: 0.5rem;
    background-color: #f2f8ff;
    font-weight: 700;
  }

  svg {
    margin: 0 auto;
    fill: #3f83f8;
  }
`;
