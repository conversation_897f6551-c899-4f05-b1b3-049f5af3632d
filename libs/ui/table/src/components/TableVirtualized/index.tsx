'use client';
import React from 'react';

import { ColumnDef as ColumnDefTS, Column, getCoreRowModel, useReactTable, CellContext } from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import classNames from 'classnames';

import styled from '@benzinga/themetron';
import Hooks from '@benzinga/hooks';
import { ErrorBoundary } from '@benzinga/core-ui';

import { StyledTableRow } from '../Table/TableRow';
import { SortBy, getCellClassname, getCellRendererRowValue, getScrollbarWidth, sortRowData } from '../../libs/utils';
import { ColumnDef } from '../../interface';
import useScrollShadow from '../../libs/useScrollShadow';
import useResizeShadow from '../../libs/useResizeShadow';
import { StyledLine, StyledShadow, StyledTBody, StyledTable } from '../Table';
import { TableHead } from './TableHead';
import Loader from '../Loader';

export type CombinedColumnDefTS<T> = ColumnDefTS<T> & ColumnDef<T>;

export interface TableVirtualizedProps<T = any> {
  className?: string;
  columnsDef: ColumnDef<T>[];
  customNoDataElement?: React.ReactElement;
  hasStockTableStyling?: boolean;
  height?: number;
  hiddenColumns?: string[];
  isGated?: boolean;
  isLoading?: boolean;
  rowData: T[];
  seeMoreLink?: string;
  seeMoreText?: string;
  title?: string;
  adPlacement?: React.ReactElement;
  noDataText?: string;
}

export const TableVirtualized = <T,>({
  adPlacement,
  className,
  columnsDef,
  customNoDataElement,
  hasStockTableStyling,
  height = 500,
  hiddenColumns,
  isGated = false,
  isLoading = false,
  noDataText,
  rowData,
  seeMoreLink,
  seeMoreText,
  title,
}: TableVirtualizedProps<T>) => {
  const [sortBy, setSortBy] = React.useState<SortBy>({});

  const ref = React.useRef<HTMLDivElement | null>(null!);
  const tableWrapperRef = React.useRef<HTMLDivElement>(null!);
  const shadowRef = React.useRef<HTMLDivElement>(null!);

  const { width } = Hooks.useRefElementSize(ref);
  const isGreaterThan700 = width > 700;

  const scrollbarWidth = getScrollbarWidth();

  useScrollShadow(tableWrapperRef, shadowRef);
  useResizeShadow(tableWrapperRef, shadowRef);

  const filteredColumnsDef = (columnsDef ?? []).filter(column => {
    const shouldBeHidden = Array.isArray(hiddenColumns) && hiddenColumns.includes(column.field);
    return !shouldBeHidden;
  });

  const sortedRowData = React.useMemo(() => sortRowData(rowData ?? [], sortBy), [rowData, sortBy]);

  const tanStackColumns = React.useMemo<CombinedColumnDefTS<T>[]>(() => {
    if (!Array.isArray(filteredColumnsDef)) return [];

    return filteredColumnsDef.map(column => {
      let accessorFn: ((row: any, index: number) => string | number | JSX.Element) | undefined;

      if (column.cellRenderer || column.valueGetter || column.valueFormatter) {
        accessorFn = (row, index) => getCellRendererRowValue(column, index, row);
      } else {
        accessorFn = undefined;
      }

      const result: CombinedColumnDefTS<T> = {
        ...column,
        accessorFn,
        accessorKey: column.field,
        header: column.headerName || column.field,
        size: column.width,
      };

      return result;
    });
  }, [filteredColumnsDef]);

  const table = useReactTable({
    columns: tanStackColumns,
    data: sortedRowData,
    //debugTable: true,
    getCoreRowModel: getCoreRowModel(),
  });

  const { rows } = table.getRowModel();
  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    estimateSize: () => 32,
    getScrollElement: () => tableWrapperRef.current,
    overscan: height ? Math.round(height / 32.5) : 15,
  });
  const { getTotalSize, getVirtualItems } = rowVirtualizer;
  const virtualRows = getVirtualItems();

  const paddingTop = virtualRows.length > 0 ? virtualRows?.[0]?.start || 0 : 0;
  const paddingBottom = virtualRows.length > 0 ? getTotalSize() - (virtualRows?.[virtualRows.length - 1]?.end || 0) : 0;

  const getCellProps = (context: CellContext<any, unknown>): { className?: string | undefined } | void => {
    const columnDef = context.column.columnDef as unknown as ColumnDef<CombinedColumnDefTS<T>>;

    const data = context.row.original;

    const value: string | number =
      (typeof columnDef.valueGetter === 'function' && columnDef.valueGetter({ data })) ||
      data[columnDef.field] ||
      columnDef.shouldRender ||
      null;

    const className = columnDef
      ? getCellClassname(columnDef, {
          data,
          index: context.row.index,
          value,
        })
      : undefined;

    return {
      className,
    };
  };

  const handleSortChange = React.useCallback(
    (newSortByField: string, column: Column<T>) => {
      let direction: 'desc' | 'asc' = 'asc';
      const columnDef = column.columnDef as CombinedColumnDefTS<T>;
      React.startTransition(() => {
        if (sortBy.field !== newSortByField) {
          direction = 'asc';
          setSortBy({ columnDef, direction, field: newSortByField });
        } else if (sortBy?.direction === 'asc') {
          direction = 'desc';
          setSortBy({ columnDef, direction, field: newSortByField });
        } else if (sortBy?.direction === 'desc') {
          setSortBy({});
        } else {
          setSortBy({ columnDef, direction, field: newSortByField });
        }
      });
    },
    [sortBy],
  );

  const isLastColumnSortable = !filteredColumnsDef[filteredColumnsDef.length - 1]?.sortEnabled || false;

  return (
    <Container
      $alignLastRowsRight={isLastColumnSortable}
      $hasStockTableStyling={hasStockTableStyling}
      $height={height}
      className={classNames('benzinga-core-virtualized-table-container', { [`${className}`]: !!className })}
      ref={ref}
    >
      {title && (
        <div className="header-container">
          <h2 className="header-title">{title}</h2>
          <StyledLine />
          {seeMoreLink && (
            <a className="see-more-link" href={seeMoreLink} rel="noreferrer" target="_blank">
              See More
              {seeMoreText && <span className="sr-only">{seeMoreText}</span>}
            </a>
          )}
        </div>
      )}
      <div className="relative">
        <div className="benzinga-core-virtualized-table-wrapper overflow-x-auto relative" ref={tableWrapperRef}>
          {(isLoading || sortedRowData.length === 0) && (
            <div className="flex items-center justify-center absolute w-full h-full text-sm">
              {isLoading && <Loader loading={true} />}
              {!isLoading &&
                sortedRowData.length === 0 &&
                (customNoDataElement || (
                  <span className="font-semibold">{noDataText || 'No data available to display'}</span>
                ))}
            </div>
          )}
          <StyledTable
            $alignLastRowsRight={isLastColumnSortable}
            $hasStockTableStyling={hasStockTableStyling}
            className={classNames('benzinga-core-virtualized-table min-w-[600px] relative', {
              'min-w-[unset]': isGreaterThan700,
            })}
          >
            <TableHead
              $alignLastRowsRight={isLastColumnSortable}
              $hasStockTableStyling={hasStockTableStyling}
              onSortChange={handleSortChange}
              sortBy={sortBy}
              table={table}
            />
            <StyledTBody className="benzinga-core-virtualized-table-tbody whitespace-nowrap">
              {paddingTop > 0 && (
                <tr>
                  <td style={{ height: `${paddingTop}px` }} />
                </tr>
              )}
              {virtualRows.map(virtualRow => {
                const row = rows[virtualRow.index];

                return (
                  <React.Fragment key={row.id}>
                    <StyledTableRow
                      $hasStockTableStyling={hasStockTableStyling}
                      className={isGated && virtualRow.index > 2 ? 'blur-md' : ''}
                    >
                      {row.getVisibleCells().map(cell => {
                        return (
                          <td className="truncate" key={cell.id} {...getCellProps(cell.getContext())}>
                            {cell.renderValue() as React.ReactNode}
                          </td>
                        );
                      })}
                    </StyledTableRow>
                    {adPlacement && sortedRowData.length > 3 && virtualRow.index === 2 && (
                      <ErrorBoundary name="stock-movers-table-ad-placement">
                        <StyledTableRow $hasStockTableStyling={hasStockTableStyling}>
                          <td colSpan={columnsDef.length}>
                            <div>{adPlacement}</div>
                          </td>
                        </StyledTableRow>
                      </ErrorBoundary>
                    )}
                  </React.Fragment>
                );
              })}
              {paddingBottom > 0 && (
                <tr>
                  <td style={{ height: `${paddingBottom}px` }} />
                </tr>
              )}
            </StyledTBody>
          </StyledTable>
        </div>
        <StyledShadow $right={scrollbarWidth} ref={shadowRef} />
      </div>
    </Container>
  );
};

export default TableVirtualized;

const Container = styled.div<{ $hasStockTableStyling?: boolean; $height?: number; $alignLastRowsRight?: boolean }>`
  width: 100%;

  .header-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;

    .header-title {
      z-index: 2;
      font-weight: 600;
      font-size: ${({ theme }) => theme.fontSize.base};
      line-height: 1;
      padding-right: 0.5rem;
      word-wrap: break-word;
      white-space: normal;
    }

    .see-more-link {
      font-weight: ${({ theme }) => theme.fontWeight.bold};
      font-size: ${({ theme }) => theme.fontSize.sm};
      color: ${({ theme }) => theme.colorPalette.blue500};
      margin-left: 0.625rem;
    }
  }

  .benzinga-core-virtualized-table-wrapper {
    height: ${({ $height }) => ($height ? `${$height}px` : 'auto')};
    border-left: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '1px solid #aaa' : '1px solid #f0f0f0')};
    border-right: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '1px solid #aaa' : '1px solid #f0f0f0')};
    border-bottom: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '1px solid #aaa' : '1px solid #f0f0f0')};
  }

  table {
    table-layout: fixed;
    border-collapse: collapse;
    border-spacing: 0;

    th {
      padding: 0.4rem;
    }

    td {
      padding: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '0.2rem 0.5rem' : '0.35rem 0.5rem')};

      &:last-of-type {
        text-align: ${({ $alignLastRowsRight }) => ($alignLastRowsRight ? 'right' : 'unset')};
      }
    }
  }

  thead {
    margin: 0;
    position: sticky;
    top: 0;
    z-index: 2;
  }
`;
