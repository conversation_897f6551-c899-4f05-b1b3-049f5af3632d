import React from 'react';
import { QA, QAProps } from '../QA/index';
import styled from '@benzinga/themetron';

export interface FAQProps {
  faqData: QAProps[];
  title?: string;
}

export const FAQ: React.FC<FAQProps> = ({ faqData }) => {
  return (
    <FAQContainer className="faq-container">
      <FAQBlock className="faq-block">
        {faqData.map((faq: QAProps) => (
          <QA answer={faq.answer} key={faq.question} question={faq.question} />
        ))}
      </FAQBlock>
    </FAQContainer>
  );
};

const FAQContainer = styled.div`
  margin-bottom: 1rem;
  h2 {
    margin-bottom: 6px;
  }
`;

const FAQBlock = styled.div`
  border: solid 1px #c3ddfd;
  border-radius: 4px;
  background: white;
  .qa-container:last-of-type {
    border-bottom: none;
  }
  h2 {
    font-weight: ${({ theme }) => theme.fontWeight.bold};
    font-size: ${({ theme }) => theme.fontSize.xl};
    line-height: 1.75rem;
    margin-left: 0.5rem;
    margin-top: 0.5rem;
    text-transform: uppercase;
  }
`;
