'use client';
import React from 'react';
import classnames from 'classnames';

import styles from './styles.module.scss';

interface Props {
  children: React.ReactNode;
  whiteBackground?: boolean;
  paddingVertical?: boolean;
  paddingHorizontal?: boolean;
  sectionPadding?: boolean;
  className?: string;
}
const FusionSectionContent = ({
  children,
  className,
  paddingHorizontal,
  paddingVertical,
  sectionPadding,
  whiteBackground,
}: Props) => {
  const content = (
    <div
      className={classnames(
        className,
        {
          bg: whiteBackground,
          paddingHorizontal,
          paddingVertical,
          sectionPadding,
        },
        styles.fusionSectionContent,
      )}
    >
      {children}
    </div>
  );

  return sectionPadding ? (
    <div className={styles.fusionSectionPadding} style={{ width: '100%' }}>
      {content}
    </div>
  ) : (
    content
  );
};

FusionSectionContent.defaultProps = {
  paddingHorizontal: true,
  paddingVertical: true,
  sectionPadding: false,
};

export default FusionSectionContent;
