import React, { useState, useEffect } from 'react';
import styled from '@benzinga/themetron';
import { usePermission } from '@benzinga/user-context';
import { Button } from '@benzinga/core-ui';
import { RankingDetail } from '@benzinga/quotes-manager';
import { usePaywallText } from './utils';
import { RankingItem } from './RankingItem';
import { GetPriceTrend } from './GetPriceTrend';

interface EdgeRankingQuoteLayoutProps extends RankingDetail {
  className?: string;
  showRanking?: boolean;
  symbol: string;
  adType: string;
}

export const EdgeRankingQuoteLayout: React.FC<EdgeRankingQuoteLayoutProps> = ({
  adType,
  className = '',
  growth,
  longTermTrend = 'Y',
  mediumTermTrend = 'Y',
  momentum,
  quality,
  shortTermTrend = 'Y',
  value,
}) => {
  const [show, setShow] = useState(false);
  const hasPermission = usePermission('com/read', 'unlimited-calendars');

  useEffect(() => {
    if (hasPermission) {
      setShow(true);
    }
  }, [hasPermission]);

  if (!show) {
    return <GetAccessToEdgeRanking adType={adType} />;
  }

  return (
    <EdgeRankingQuoteLayoutWrapper className={className}>
      <div className="ranking-wrap flex flex-col md:flex-row w-full">
        <div className="rankings-data flex flex-col justify-end bg-wrap w-full">
          <h5 className="leading-none mb-2">Benzinga Rankings</h5>
          <div className="ranking-row flex flex-row w-full">
            {RankingItem({ label: 'Momentum', value: momentum ?? 0 })}
            {RankingItem({ label: 'Value', value: value ?? 0 })}
          </div>
          <div className="ranking-row flex flex-row">
            {RankingItem({ label: 'Growth', value: growth ?? 0 })}
            {RankingItem({ label: 'Quality', value: quality ?? 0 })}
          </div>
        </div>
        <div className="position-type w-full flex flex-col bg-wrap">
          <h5 className="leading-none mb-2">Price Trend</h5>
          <div className="flex gap-2 flex flex-col sm:flex-row">
            {GetPriceTrend(shortTermTrend ?? '', 'Short')}
            {GetPriceTrend(mediumTermTrend ?? '', 'Medium')}
            {GetPriceTrend(longTermTrend ?? '', 'Long')}
          </div>
        </div>
      </div>
    </EdgeRankingQuoteLayoutWrapper>
  );
};

export default EdgeRankingQuoteLayout;

const GetAccessToEdgeRanking = ({ adType }: { adType: string }) => {
  const [paywallLabel, paywallDesc, paywallButtonLabel, buyNowLink] = usePaywallText(adType);

  return (
    <GetEdgeRankingWrap className="get-access-wrap">
      <div className="flex flex-col sm:flex-row justify-between items-center w-full gap-2">
        <div className="access-header">
          <h3>{paywallLabel}</h3>
          <p>{paywallDesc}</p>
        </div>
        <Button as="a" className="w-full sm:w-auto" href={buyNowLink} variant="flat-blue">
          {paywallButtonLabel}
        </Button>
      </div>
    </GetEdgeRankingWrap>
  );
};

const GetEdgeRankingWrap = styled.div`
  background-color: #f2f8ff;
  border: 1px solid #ceddf2;
  border-radius: 4px;
  padding: 1rem;
  flex-grow: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  a {
    min-width: 140px;
    padding: 12px 10px;
    @media (max-width: 768px) {
      min-width: 140px;
      padding: 12px 10px;
    }
  }
  .access-header {
    h3 {
      font-size: 1.2rem;
      color: #192940;
    }
    p {
      color: #5b7292;
      font-size: 14px;
    }
  }
  button {
    font-size: 16px;

    @media (max-width: 768px) {
      font-size: 14px;
    }
  }
`;

const EdgeRankingQuoteLayoutWrapper = styled.div`
  .ranking-wrap {
    flex-grow: 1;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    width: 100%;
    .bg-wrap {
      background-color: #f2f8ff;
      border: 1px solid #ceddf2;
      border-radius: 4px;
      padding: 1rem;
    }
    h5 {
      font-size: 16px;
      font-weight: 600;
      color: rgb(37, 50, 67);
    }
    .rankings-data {
      .ranking-row {
        .ranking-item {
          margin-right: 0.8rem;
          display: flex;
          width: 100%;
          .ranking-label {
            color: #5b7292;
            min-width: 110px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
          }
          .ranking-value {
            font-size: 16px;
            min-width: 40px;
            font-weight: 600;
          }
        }
        .ranking-item {
          &:nth-child(2) {
            .ranking-label {
              min-width: 75px;
            }
          }
        }
      }
    }
    .position-type {
      .position-wrap {
        display: flex;
        border-radius: 4px;
        padding: 4px 6px;
        align-items: center;
        width: 100%;
        justify-content: space-between;
        background: white;

        .position-label {
          color: #5b7292;
          font-weight: 600;
        }
        .position-direction {
          height: 30px;
          width: 30px;
          justify-content: center;
          display: flex;
          align-items: center;
          padding: 0.5rem;
          border-radius: 4px;

          &.positive {
            background: #30bf6040;
          }
          &.negative {
            background: #ff405040;
          }
        }
      }
    }
  }
`;
