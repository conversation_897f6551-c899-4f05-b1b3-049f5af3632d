import React from 'react';
import { Schema } from '@benzinga/seo';
import styled from '@benzinga/themetron';
import { SectionTitle } from '@benzinga/core-ui';
import { ReviewEntries, SubmitReviewBox } from '@benzinga/reviews-ui';
import { Product, WordpressPost } from '@benzinga/content-manager';
import MoneyPostTemplate from './MoneyPostTemplate';
import { MoneyTemplateProps } from '../../entities';
import { ProductDetails } from '@benzinga/product-ui';

const generateProsConsItems = (product: Product, notes: string, dataKey: string) => {
  const items: any = [];
  if (product?.details?.[`${dataKey}`]?.length) {
    const data = product?.details?.[`${dataKey}`];
    data?.forEach((item, index) => {
      const reviewListItem = {
        '@type': 'ListItem',
        name: item,
        position: index + 1,
      };
      items.push(reviewListItem);
    });

    return {
      [`${notes}`]: {
        '@type': 'ItemList',
        itemListElement: items,
      },
    };
  }
  return null;
};

const prepareSchema = (product: Product, post: WordpressPost) => {
  const productPros = generateProsConsItems(product, 'positiveNotes', 'pros');
  const productCons = generateProsConsItems(product, 'negativeNotes', 'cons');

  const schemaObj = {
    '@context': 'https://schema.org/',
    '@type': 'Review',
    author: {
      '@type': 'Person',
      name: post?.meta?.author ?? 'Benzinga',
    },
    description: product.review,
    itemReviewed: {
      '@type': 'Organization',
      image: product.image,
      name: product.name,
    },
    name: product.name,
    publisher: {
      '@type': 'Organization',
      name: 'Benzinga',
    },
    reviewBody: '',
    reviewRating: {
      '@type': 'Rating',
      ratingValue: product.rating,
    },
    ...productPros,
    ...productCons,
  };
  return schemaObj;
};

export const ReviewTemplate: React.FC<MoneyTemplateProps> = ({ post }) => {
  return (
    <ReviewTemplateWrapper>
      {post?.review?.product && <Schema data={prepareSchema(post.review?.product, post)} name="organization" />}
      <MoneyPostTemplate
        layoutAboveArticle={
          post?.review?.product && (
            <ProductDetails
              cons_title={'Potential Drawbacks'}
              force_show_review={true}
              hide_leave_review={true}
              is_branded_review={post.review?.is_branded_review}
              product={post.review?.product}
              pros_title={'Reasons to Use'}
            />
          )
        }
        post={post}
      />
    </ReviewTemplateWrapper>
  );
};

export default ReviewTemplate;

const ReviewTemplateWrapper = styled.div`
  .narrow {
    .header-container,
    .layout-container {
      max-width: 1100px;
    }
  }
  .product-features-section {
    .product-features {
      .section-title {
        font-size: 1.15rem;
      }
    }
  }
  .product-more-details {
    border: solid 1px #ceddf2;
    border-radius: 4px;
  }
  .reviews-section {
    border: 1px solid #ceddf2;
    border-radius: 4px;
    margin: 1rem auto;
    .review-heading {
      padding: 0 1rem 1rem;
    }
  }
  .review-form-wrapper {
    max-width: 420px;
    margin: auto;
    border: 1px solid #ceddf2;
    border-radius: 4px;

    .submit-review-box {
      margin: auto;
    }

    .section-title {
      margin-top: 0;
      margin-bottom: 0;
      padding: 1rem;
    }
    #one-minute-opinion {
      margin: 0;
      padding: 10px 16px;
      background: #ceddf2;
      h3 {
        line-height: 1.2;
        text-transform: capitalize;
      }
    }
  }
`;
