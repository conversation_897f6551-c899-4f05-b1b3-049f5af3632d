import React from 'react';
import styled from '@benzinga/themetron';
import { WordpressPost } from '@benzinga/content-manager';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { DateTime } from 'luxon';
import { MoneyPostBreadcrumbs } from './MoneyPostBreadcrumbs';
import { NoFirstRender } from '@benzinga/hooks';

import { LayoutAdmin } from '@benzinga/blocks';

import { MoneyProductCard } from '@benzinga/product-ui';

export interface MoneyHeader {
  post: WordpressPost;
}

const MoneyPostAuthorBlock: React.FC<MoneyHeader> = ({ post }) => {
  const verifiedUserUrl = `https://www.benzinga.com/money/author/${post.meta?.verifiedUserSlug}`;
  const editedUserUrl = `https://www.benzinga.com/money/author/${post.meta?.editedUserSlug}`;
  const publishedDate = post.meta?.publishedDate
    ? DateTime.fromISO(post.meta?.publishedDate, { setZone: true }).toFormat('LLLL d, yyyy')
    : '';

  return (
    <div className="contributor-block-container">
      <div className="contributor-block">
        {post?.review?.is_branded_review && post?.review?.product?.company_name !== '' ? (
          <div className="branded-by">
            <div className="label pb-1">Presented By</div>
            <div className="brand-name">{post?.review?.product?.company_name}</div>
          </div>
        ) : (
          <>
            <div className="author">
              {post.meta?.authorSlug ? (
                <a href={`https://www.benzinga.com/money/author/${post.meta?.authorSlug}`}>{post.meta?.author}</a>
              ) : (
                <span>{post.meta?.author}</span>
              )}
            </div>
            <div className="author-description">Contributor, Benzinga</div>
            {publishedDate && <div className="date">{publishedDate}</div>}
            {post.meta?.editedUserSlug && post.meta?.editedBy && (
              <div className="edited-by mt-1">
                <span className="italic text-xs font-os">
                  edited by{' '}
                  <a className="text-sm" href={editedUserUrl}>
                    {post.meta?.editedBy}
                  </a>
                </span>
              </div>
            )}
            {post.meta?.verifiedUserSlug && post.meta?.verifiedBy && (
              <div className="verified-by">
                <span className="italic text-xs font-os">
                  verified by{' '}
                  <a className="text-sm" href={verifiedUserUrl}>
                    {post.meta?.verifiedBy}
                  </a>
                </span>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export const MoneyHeader: React.FC<MoneyHeader> = ({ post }) => {
  return !post.layout?.settings?.simple_article_title ? (
    <MoneyHeaderWrapper className={`money-page-header ${post?.template === 'review' ? 'review' : ''}`}>
      <div className="header-container mx-auto container max-w-7xl px-4 flex-col md:flex-row items-cetner">
        <MoneyPostBreadcrumbs post={post} />
      </div>
      <div className="header-container mx-auto container max-w-7xl px-4 flex-col items-start md:flex-row md:items-cetner">
        <div className="page-header-info">
          <NoFirstRender>
            <React.Suspense>
              <LayoutAdmin post={post} showClearCache />
            </React.Suspense>
          </NoFirstRender>
          <h1>{post.title}</h1>
          {post.intro && <div className="page-intro" dangerouslySetInnerHTML={{ __html: sanitizeHTML(post.intro) }} />}
          <p className="affiliate-disclosure" />
          <a className="learn-more-link" href="https://www.benzinga.com/money/advertiser-disclosure/">
            Read our Advertiser Disclosure.
          </a>
        </div>
        {post?.post_type !== 'discover' && <MoneyPostAuthorBlock post={post} />}
      </div>
      <div className="header-container mx-auto container max-w-7xl px-4 flex-col items-start md:flex-row md:items-cetner">
        {post.review?.product && (
          <MoneyProductCard
            is_braded_review={post.review?.is_branded_review ?? false}
            options={{ show_best_for: '0', show_cons: '0', show_pros: '0', show_review: '0' }}
            product={post.review?.product}
            review_label={'Their Score:'}
            variant="header"
          />
        )}
      </div>
    </MoneyHeaderWrapper>
  ) : (
    <MoneyPageTitle className="header-container mx-auto container max-w-7xl flex-col md:flex-row items-cetner mt-6 advertorial-header">
      <h1>{post.title}</h1>
    </MoneyPageTitle>
  );
};

const MoneyPageTitle = styled.div`
  &.advertorial-header {
    clear: both;
    display: block;
    @media (max-width: 1300px) {
      padding-left: 0.25rem;
      padding-right: 0.25rem;
    }

    @media (max-width: 1100px) {
      padding: 0 1rem 0.5rem 0;
    }
    h1 {
      font-size: 2.5rem;
      font-weight: normal;
      @media (max-width: 1100px) {
        font-size: 2rem;
      }
      @media (max-width: 767px) {
        font-size: 1.8rem;
      }
      @media (max-width: 576px) {
        font-size: 1.6rem;
      }
    }
  }
`;

const MoneyHeaderWrapper = styled.div`
  background-color: ${({ theme }) => theme.colorPalette.gray50};
  background-color: #151F2E;
  color: #F2F8FF;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  border-bottom: none;
  padding: 20px 0;

  .page-intro {
    font-size: ${({ theme }) => theme.fontSize.base};
    white-space: pre-line;
  }

  @media (min-width: 1300px) {
    .header-container {
      padding: 0;
    }
  }

  .money-product-card {
    margin-top: 1rem;
    .single-item-view {
      display: none;
    }
  }

  > div {
    display: flex;
    justify-content: space-between;
  }

  .affiliate-disclosure {
    margin-top: 0.5rem;
    margin-bottom: 0;
    color: #5B7292;
    font-size: 14px;
    &:before {
      content: 'Over 1,000,000 people have trusted Benzinga to help them choose their investing tools. Our experts are hard at work with constant updates as products & markets change! Benzinga Money is a reader-supported publication. We may earn a commission from the advertisers associated with this article.'
    }
  }

  .page-header-info {
    width: 100%;
    max-width: 640px;
    margin-right: 0.5rem;

    h1 {
      font-size: 1.8rem;
      margin-bottom: 0;
      color: #F2F8FF;
    }

    p {
      color: ${({ theme }) => theme.colorPalette.gray500};
      color: #99AECC;
      font-size: ${({ theme }) => theme.fontSize.base};
      margin-bottom: 0;
    }

    .learn-more-link {
      font-size: ${({ theme }) => theme.fontSize.base};
      color: ${({ theme }) => theme.colorPalette.blue500};
      color: #3F83F8;
    }
  }
  &.review {
     .affiliate-disclosure {
      &:before {
        content: "This content may have been created in collaboration with the featured partner and is therefore considered branded content. The partner may have provided materials and input for this piece. It should not be considered an independent editorial review. It's possible that we received compensation — which may have included a fixed fee, commissions based on sales, or both — for publishing this content."
      }
    }
  }
}

.contributor-block-container {
  max-width: 20rem;
  width: 100%;
  margin-top: 1rem;
}

.contributor-block {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 320px;
  border: 1px solid ${({ theme }) => theme.colorPalette.gray300};
  border: 1px solid #395173;
  background-color: ${({ theme }) => theme.colorPalette.gray100};
  background-color: transparent;
  padding: 1rem;
  border-radius: ${({ theme }) => theme.borderRadius.default};

  .author {
    color: ${({ theme }) => theme.colorPalette.gray800};
    text-transform: uppercase;
    font-size: ${({ theme }) => theme.fontSize.xl};
    font-weight: ${({ theme }) => theme.fontWeight.semibold};
    a {
      color: #F2F8FF;
    }
  }
  .branded-by {
    color: ${({ theme }) => theme.colorPalette.gray800};
    font-size: ${({ theme }) => theme.fontSize.xl};
    .label {
      color: #5B7292;
      font-size: ${({ theme }) => theme.fontSize.base};
    }
    .brand-name  {
      color: #E1EBFA;
      font-size: ${({ theme }) => theme.fontSize.lg};
    }
  }

  .author-description {
    color: #5B7292;
    font-size: ${({ theme }) => theme.fontSize.base};
  }

  .verified-by, .edited-by {
    color: #6b7280;
    font-size: ${({ theme }) => theme.fontSize.base};
  }

  .date {
    color: #E1EBFA;
    font-size: ${({ theme }) => theme.fontSize.base};

    &.updated {
      font-style: italic;
      margin-right: 2px;

      > span {
        color: ${({ theme }) => theme.colorPalette.gray400};
      }
    }
  }
`;

export default MoneyHeader;
