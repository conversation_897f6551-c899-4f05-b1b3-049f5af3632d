import React from 'react';
import { StarRating } from '@benzinga/core-ui';

type HeadingLevel = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

const isValidHeadingLevel = (level: string): level is HeadingLevel => {
  return ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(level);
};

export interface StarRatingBlockProps {
  id?: string;
  attrs: {
    data: {
      heading: string;
      level: string;
      rating: number;
    };
  };
}

export const StarRatingBlock: React.FC<StarRatingBlockProps> = ({ attrs, id }) => {
  const level = isValidHeadingLevel(attrs.data.level) ? attrs.data.level : 'h2';

  return (
    <StarRating
      heading={attrs.data.heading}
      headingClassname="core-block"
      id={id}
      level={level}
      rating={attrs.data.rating}
    />
  );
};
