import React from 'react';
import { ReviewCard } from '../ReviewCard';
import { SectionTitle } from '@benzinga/core-ui';
import styled from '@benzinga/themetron';
import { MoneyPosts } from '../MoneyPosts';
import { WordpressPost } from '@benzinga/content-manager';
import classNames from 'classnames';

interface ReviewCardProps {
  attrs?: {
    data: {
      borderThroughTitle?: boolean;
      title?: string;
    };
  };
  className?: string;
  reviews: any[];
  sponsoredContent?: WordpressPost[];
}

export const ReviewCardsBlock: React.FC<ReviewCardProps> = ({ attrs, className, reviews, sponsoredContent }) => {
  if (!Array.isArray(reviews) || reviews.length < 1) {
    return <div />;
  }

  return (
    <Container className={classNames('review-cards-block', { [`${className}`]: !!className })}>
      <div className={'review-cards-block-inner'}>
        {attrs?.data?.title && (
          <SectionTitle borderThrough={attrs?.data?.borderThroughTitle}>{attrs.data.title}</SectionTitle>
        )}
        <div className="review-cards-list">
          {reviews.map((review, index) => {
            const data = {
              href: `/money/${review?.slug}/`,
              image: review?.product?.image,
              name: review?.product?.name,
              rating: review?.product?.overall_rating,
              subtitle: review.product?.short_best_for,
              title: review?.product?.name,
            };
            return <ReviewCard data={data} key={`${review?.product_id}-${index}`} variant="secondary" />;
          })}
        </div>
      </div>
      {Array.isArray(sponsoredContent) && (
        <div className="sponsored-content-section">
          <MoneyPosts hidePublishDate={true} layout="card" posts={sponsoredContent} title="Sponsored Content" />
        </div>
      )}
    </Container>
  );
};

export const Container = styled.div`
  &.review-cards-block {
    margin: 2rem 0;
    display: flex;
    flex-direction: column;
    padding: 0 0.25rem;

    @media screen and (min-width: 1024px) {
      flex-direction: row;
    }

    .review-cards-block-inner {
      flex: 1;
    }

    .review-cards-list {
      display: grid;
      gap: 24px;
      grid-template-columns: repeat(3, minmax(0, 1fr));
      @media (max-width: 640px) {
        gap: 24px;
        grid-column: 2;
        grid-template-columns: repeat(1, minmax(0, 1fr));
      }
    }

    .section-title h3,
    .posts-header h2 {
      font-size: ${({ theme }) => theme.fontSize.lg};
      color: ${({ theme }) => theme.colorPalette.gray700};
      margin-bottom: 1rem;
    }

    .sponsored-content-section {
      margin-top: 1rem;

      @media screen and (min-width: 1024px) {
        margin-left: 1rem;
        max-width: 300px;
        width: 100%;
        margin-top: 0;

        .post-card-wrapper {
          width: 300px;
        }
      }

      .money-post-wrapper {
        margin: 0;
      }

      .posts-header {
        border-bottom: none;
        margin: 0;
        padding: 0;
      }

      .post-title {
        color: ${({ theme }) => theme.colorPalette.gray700};
      }

      .posts-container {
        display: grid;
        gap: 1rem;
        grid-template-columns: repeat(2, minmax(0, 1fr));

        @media screen and (min-width: 1024px) {
          display: flex;
          flex-direction: column;
          gap: 0;
        }
      }

      .post-card-wrapper {
        max-width: 100%;
      }
    }
  }
`;
