'use client';
import React from 'react';
import Hooks from '@benzinga/hooks';
import { Icon, Spinner } from '@benzinga/core-ui';
import { faComment } from '@fortawesome/pro-solid-svg-icons';
import { faClose } from '@fortawesome/pro-regular-svg-icons';

export interface CommentsDrawerProps {
  onClose: () => void;
  isOpen: boolean;
}

export const CommentsDrawer: React.FC<CommentsDrawerProps> = ({ isOpen, onClose }) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  Hooks.useClickOutside(containerRef, onClose);

  const drawerClassName = `fixed top-0 right-0 z-40 h-screen p-4 overflow-y-auto transition-transform bg-white w-full max-w-[700px] z-[999] ${
    isOpen ? 'transform-none' : 'translate-x-full'
  }`;

  const backdropClassName = `bg-black/40 w-screen h-screen fixed z-[998] top-0 left-0 ${isOpen ? 'block' : 'hidden'}`;

  return (
    <div className="flex">
      <div aria-labelledby="comments-drawer" className={drawerClassName} ref={containerRef} tabIndex={-1}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Icon className="mr-2" icon={faComment} />
            <h5 className="inline-flex items-center text-base font-bold text-gray-700" id="comments-drawer">
              Comments
            </h5>
          </div>
          <button
            className="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 flex items-center justify-center"
            onClick={onClose}
            type="button"
          >
            <Icon className="w-3.5 h-3.5" icon={faClose} />
            <span className="sr-only">Close menu</span>
          </button>
        </div>
        <div id="coral_thread">
          <Spinner />
        </div>
      </div>
      <div className={backdropClassName}></div>
    </div>
  );
};

export default CommentsDrawer;
