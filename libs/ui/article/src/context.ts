'use client';
import React from 'react';

import { Campaigns, CampaignStrategy } from '@benzinga/article-manager';

interface ContextProps {
  campaignStrategy?: CampaignStrategy | null;
  campaign?: Campaigns | null;
  hide?: boolean;
  primaryImage?: null | {
    alt: string;
    height: number;
    url: string;
    width: number;
  };
  trackMeta?: boolean;
}

// Set Context
export const ArticlePageContext = React.createContext<ContextProps>({
  campaign: undefined,
  campaignStrategy: 'all',
  hide: false,
  primaryImage: null,
  trackMeta: true,
});
