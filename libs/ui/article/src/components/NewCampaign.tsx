import React, { ReactElement, useMemo, useCallback } from 'react';
import { DateTime } from 'luxon';
import { createCampaignifyBlock } from '@benzinga/ads-utils';
import { Block, MoneyBlockType, NodeLayout } from '@benzinga/content-manager';
import {
  ArticleAdsManager,
  ArticleBlock,
  ArticleBlockPlaceholder,
  ArticleData,
  Campaigns,
  CampaignStrategy,
  countElementsInBodyBlocks,
  isTaxonomyExists,
} from '@benzinga/article-manager';
import { StoryCategory } from '@benzinga/advanced-news-manager';
import { DeviceType } from '@benzinga/device-utils';

type Blocks = ArticleBlock[] | ArticleBlockPlaceholder[] | MoneyBlockType[];

export interface CampaignProps {
  nodeId?: number | string;
  contentType?: string;
  articleTitle?: string;
  articleBody: string[];
  articleBlocks?: Blocks;
  articleUrl?: string;
  inContentBlocks?: Blocks;
  campaigns?: Campaigns;
  disableCampaignifyUnit?: boolean;
  hasAdLight?: boolean;
  isAmp?: boolean;
  isSponsored?: boolean;
  channels?: StoryCategory[];
  layout?: NodeLayout | null;
  createdDate?: string;
  deviceType?: DeviceType | null;
  tags?: StoryCategory[];
  ticker?: string;
  strategy?: CampaignStrategy;
  onRendered?: () => void;
  onReady?: () => void;
  primis?: boolean;
  wordCount?: number | null;
  articleData?: ArticleData;
  googleAdPlaceholder?: Block;
  raptiveEnabled?: boolean;
  useNewTemplate?: boolean;
  children(articleBody: string | Blocks): ReactElement;
}

const SHORT_ARTICLE_WORD_COUNT = 200;

const CONTENT_TYPE_FOR_HIDE = 'story';
const CHANNELS_FOR_HIDE = ['News', 'Markets'];
const COUNT_OF_ELEMENT_FOR_HIDE = 4;
const TYPE_OF_ELEMENT_FOR_HIDE = 'p';
const DAYS_OF_PUBLISHED_FOR_HIDE = 30;

const excludeBlocks = (blocks: Blocks): Blocks => {
  return blocks.filter(block => !['acf/google-ad-placement'].includes(block.blockName || ''));
};

const articleAdsManager = new ArticleAdsManager({}).AD_BLOCK_NAMES;

const excludeAdBlocks = (blocks: Blocks): Blocks => {
  return blocks.filter(block => !articleAdsManager.includes(block.blockName || ''));
};

export const NewCampaign: React.FC<CampaignProps> = ({
  articleBlocks = [],
  channels,
  children,
  contentType,
  createdDate,
  disableCampaignifyUnit,
  hasAdLight,
  isSponsored,
  nodeId,
  onRendered,
  strategy,
  ticker,
  wordCount,
}) => {
  React.useEffect(() => {
    typeof onRendered === 'function' && onRendered();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const checkIfShortArticle = useCallback((count: number): boolean => {
    return count ? count < SHORT_ARTICLE_WORD_COUNT : false;
  }, []);

  const shouldHideCampaign = useCallback((): boolean => {
    if (!createdDate || !articleBlocks || !contentType || !channels) {
      return false;
    }

    const duration = DateTime.now().diff(DateTime.fromISO(createdDate), ['days']);

    return (
      contentType === CONTENT_TYPE_FOR_HIDE &&
      isTaxonomyExists(CHANNELS_FOR_HIDE, channels) &&
      countElementsInBodyBlocks(articleBlocks as ArticleBlock[], TYPE_OF_ELEMENT_FOR_HIDE) >
        COUNT_OF_ELEMENT_FOR_HIDE &&
      duration.days > DAYS_OF_PUBLISHED_FOR_HIDE
    );
  }, [articleBlocks, channels, contentType, createdDate]);

  const shouldRenderCampaign = useCallback(
    (content: string[] | Blocks): boolean => {
      return (
        Array.isArray(content) && content.length > 0 && !isSponsored && strategy !== 'none' && !shouldHideCampaign()
      );
    },
    [isSponsored, shouldHideCampaign, strategy],
  );

  const shouldRenderAds = useCallback(
    (content: string[] | Blocks): boolean => {
      return Array.isArray(content) && content.length > 0 && !isSponsored;
    },
    [isSponsored],
  );

  const findLastReadMorePosition = useCallback((blocks: ArticleBlock[]): number => {
    for (let i = blocks.length - 1; i >= 0; i--) {
      const content = blocks[i].innerHTML?.toLowerCase() || '';
      if (
        content.includes('<strong>read next:') ||
        content.includes('<strong>read more:') ||
        content.includes('<strong>read now:')
      ) {
        return i;
      }
    }
    return -1;
  }, []);

  const formatArticleBlocks = useCallback(
    (blocks: Blocks): string | Blocks => {
      if (!blocks.length) return blocks;

      const newArticleBlocks: (MoneyBlockType | ArticleBlock)[] = [...blocks];
      const isShortArticle = wordCount ? checkIfShortArticle(wordCount) : false;

      const baseCampaignifyParams = {
        nodeId: nodeId ? Number(nodeId) : undefined,
        strategy,
        ticker,
      };

      let middleAd: MoneyBlockType | null = createCampaignifyBlock({
        ...baseCampaignifyParams,
        position: 'middle',
      });

      let bottomAd: MoneyBlockType | null = createCampaignifyBlock({
        ...baseCampaignifyParams,
        position: 'bottom',
      });

      if (disableCampaignifyUnit || !shouldRenderCampaign(blocks)) {
        middleAd = null;
        bottomAd = null;
      }

      if (shouldRenderAds(blocks)) {
        if (isShortArticle) {
          middleAd && newArticleBlocks.push(middleAd);
        } else if (hasAdLight) {
          middleAd && newArticleBlocks.push(middleAd);
        } else {
          if (bottomAd) {
            const readMorePosition = findLastReadMorePosition(newArticleBlocks as ArticleBlock[]);
            if (readMorePosition !== -1) {
              newArticleBlocks.splice(readMorePosition, 0, bottomAd);
            } else {
              newArticleBlocks.push(bottomAd);
            }
          }
        }
      }
      return newArticleBlocks;
    },
    [
      checkIfShortArticle,
      disableCampaignifyUnit,
      findLastReadMorePosition,
      hasAdLight,
      nodeId,
      shouldRenderAds,
      shouldRenderCampaign,
      strategy,
      ticker,
      wordCount,
    ],
  );

  const articleBodyWithCampaign = useMemo(() => {
    if (hasAdLight) {
      return formatArticleBlocks(excludeAdBlocks(articleBlocks) || []);
    }
    return formatArticleBlocks(excludeBlocks(articleBlocks) || []);
  }, [articleBlocks, formatArticleBlocks, hasAdLight]);

  return <div>{children(articleBodyWithCampaign)}</div>;
};

export default NewCampaign;
