'use client';
import React, { useEffect, useMemo, useState } from 'react';
import camelcaseKeys from 'camelcase-keys';
import classNames from 'classnames';

import { TrackingManager } from '@benzinga/tracking-manager';
import styled from '@benzinga/themetron';

import {
  authorTypeGenerator,
  calculateReadTime,
  generatePartialViewPreviewFromArticleBlocks,
  getCanonicalUrl,
  printProHeadlineArticleContent,
  setDFPTargeting,
} from '@benzinga/article-manager';

import { Button, ButtonVariant, ErrorBoundary, Icon, Layout } from '@benzinga/core-ui';

import { checkDeviceType } from '@benzinga/device-utils';
import Hooks, { NoFirstRender } from '@benzinga/hooks';
import { VisiblePageTracker } from '@benzinga/analytics';

import { NodeLayout } from '@benzinga/content-manager';
import { ArticleBlock, ArticleData, ArticleManager, getPrimaryTickers } from '@benzinga/article-manager';

import { faChevronRight } from '@fortawesome/pro-solid-svg-icons';
//import { ArticleLayoutSidebar } from './ArticleLayoutSidebar';
import { SessionContext } from '@benzinga/session-context';

import { ShareButtons } from '@benzinga/ui';
import { ArticlePageContext } from '../context';
import { AdvertiserDisclosure } from './AdvertiserDisclosure';
import AppleNewsButton from './AppleNewsButton';
import { ArticleBlocks } from './ArticleBlocks';
import { TaboolaSettings, ArticleLayoutWrapper, ArticleContentWrapper } from './ArticleLayoutMain';
import { GetBelowArticlePartnerAdBlock } from './GetBelowArticlePartnerAdBlock';
import { AuthorContent } from './AuthorContent';
import GoogleNewsButton from './GoogleNewsButton';
import { ImagePlacement } from './ImagePlacement';
import KeyPoints from './KeyPoints';
import ReadInAppButton from './ReadInAppButton';

interface PartialArticleDataProps {
  articleID?: number;
  articleData: ArticleData;
  articleIndex?: number;
  deviceType?: string | null;
  articleScrollViewMoreLink?: string;
  googleNewsUrlKey?: string;
  lastStory?: boolean;
  layout?: NodeLayout;
  loadMoreButtonVariant?: ButtonVariant;
  showAdvertiserDisclosure?: boolean;
  executeImpressionEvent?: boolean;
  executeReplaceHistory?: boolean;
  executePageviewEvent?: boolean;
  showCommentButton?: boolean;
  showFontAwesomeIcons?: boolean;
  showPartnerAd?: boolean;
  showWhatsAppIcon?: boolean;
  taboolaSettings?: TaboolaSettings;
  trackMeta?: boolean;
}

type MinimumRequiredArticleProps = Required<
  Pick<PartialArticleDataProps, 'articleData'> | Pick<PartialArticleDataProps, 'articleID'>
>;

export const PartialArticleLayout = ({
  articleData: initialArticleData,
  articleID,
  articleIndex,
  articleScrollViewMoreLink,
  deviceType,
  executeImpressionEvent,
  executePageviewEvent,
  executeReplaceHistory,
  googleNewsUrlKey,
  lastStory,
  //layout,
  loadMoreButtonVariant,
  showCommentButton,
  showFontAwesomeIcons,
  showPartnerAd,
  showWhatsAppIcon,
  taboolaSettings,
  trackMeta = true,
}: MinimumRequiredArticleProps & PartialArticleDataProps) => {
  const [articleData, setArticleData] = useState(initialArticleData);
  const articleMeta = camelcaseKeys(articleData?.meta, { deep: true });
  const showAdvertiserDisclosure = articleMeta?.flags?.showAdvertiserDisclosure || false;
  const session = React.useContext(SessionContext);
  const articleUrl = getCanonicalUrl(articleData ?? {});

  useEffect(() => {
    if (!articleData && articleID) {
      const articleManager = session.getManager(ArticleManager);
      articleManager.getArticle(articleID).then(article => {
        article.ok && setArticleData(article.ok);
      });
    }
  }, [articleData, articleID, session]);

  if (!articleData) {
    return null;
  }

  return (
    <ArticlePageContext.Provider value={{ campaign: null, campaignStrategy: null }}>
      <ArticleLayoutWrapper className="article-layout-wrapper">
        <Layout
          layoutMain={
            <PartialArticleLayoutMain
              {...{
                articleData,
                articleIndex,
                articleScrollViewMoreLink,
                deviceType,
                executeImpressionEvent,
                executePageviewEvent,
                executeReplaceHistory,
                googleNewsUrlKey,
                lastStory,
                loadMoreButtonVariant,
                showAdvertiserDisclosure,
                showCommentButton,
                showFontAwesomeIcons,
                showPartnerAd,
                showWhatsAppIcon,
                taboolaSettings,
                trackMeta,
              }}
            />
          }
          layoutMainClassName="w-full overflow-hidden"
          // layoutSidebar={
          //   <ArticleLayoutSidebar
          //     {...{
          //       article: articleData,
          //       articleIndex,
          //       isHeadline: articleData?.isHeadline,
          //       layout,
          //       nodeId: articleData?.nodeId,
          //       partialView: true,
          //     }}
          //   />
          // }
          title={articleData.title}
          url={articleUrl}
        />
      </ArticleLayoutWrapper>
    </ArticlePageContext.Provider>
  );
};

const PartialArticleLayoutMain: React.FC<PartialArticleDataProps> = ({
  articleData,
  articleIndex,
  articleScrollViewMoreLink = 'top-stories',
  deviceType,
  executeImpressionEvent = false,
  executePageviewEvent = false,
  executeReplaceHistory = false,
  googleNewsUrlKey,
  lastStory,
  layout,
  loadMoreButtonVariant = 'flat-light-blue',
  showAdvertiserDisclosure = false,
  showCommentButton,
  showFontAwesomeIcons,
  showPartnerAd,
  showWhatsAppIcon,
  taboolaSettings,
  trackMeta = true,
}) => {
  const session = React.useContext(SessionContext);
  const isMobile = Hooks.useHydrate(checkDeviceType().isMobile(), false);
  const isApple = Hooks.useHydrate(checkDeviceType().isApple(), false);
  const createdAt = articleData?.createdAt ? new Date(articleData.createdAt).toISOString() : null;
  const contentType = articleData?.type;
  const keyItems = articleData?.keyItems && articleData?.keyItems?.map(item => item.value).filter(item => item?.length);
  const title = articleData?.title;

  const articleUrl = getCanonicalUrl(articleData ?? {});

  const tickers = getPrimaryTickers(articleData?.tickers);

  const previewText = useMemo(() => {
    const result = generatePartialViewPreviewFromArticleBlocks(articleData?.blocks as ArticleBlock[]);
    return result || articleData?.teaserText || articleData?.body;
  }, [articleData?.blocks, articleData?.body, articleData?.teaserText]);

  const handleReadMore = () => {
    if (showAdvertiserDisclosure) {
      session.getManager(TrackingManager).trackCampaignEvent('view', {
        partner_id: (articleData?.meta?.Reach?.Disclosure?.tid || 0).toString(),
        unit_type: 'infinite-scroll',
      });
      session.getManager(TrackingManager).trackPageEvent('load_more', {});
    }
  };

  const onArticleVisible = articleData => {
    setDFPTargeting(articleData);
  };

  const authorName =
    articleData?.author?.firstname && articleData?.author?.lastname
      ? `${articleData?.author.firstname} ${articleData?.author.lastname}`
      : articleData?.author?.name || articleData?.name;

  if (!articleData) {
    return null;
  }

  const url = articleData?.canonicalPath ? `/${articleData.canonicalPath}` : `/article/${articleData.nodeId}`;

  return (
    <>
      <div className="flex flex-col w-full items-start lg:flex-row">
        <ErrorBoundary name="partial-article-layout-main-author-content">
          <AuthorContent
            authorLink={articleData?.author?.profileUrl}
            authorName={authorName ?? ''}
            authorType={authorTypeGenerator(
              articleData?.author?.byLine ?? '',
              showAdvertiserDisclosure,
              contentType ?? '',
            )}
            className="flex flex-grow"
            commentCount={articleData.commentCount}
            created={createdAt ?? ''}
            isDraft={false}
            isPartialView={true}
            readTime={calculateReadTime(articleData.body)}
            showCommentButton={showCommentButton}
            twitter={articleData.author?.twitter}
            url={url}
          />
        </ErrorBoundary>
        <div className="flex items-center lg:ml-auto flex-wrap gap-2">
          <ShareButtons
            className="flex"
            showFontAwesomeIcons={showFontAwesomeIcons}
            showWhatsAppIcon={showWhatsAppIcon}
            tickers={tickers}
            title={title}
            url={articleUrl}
          />
          <NoFirstRender fallback={<div className="h-[40px] w-[128px]" />}>
            {googleNewsUrlKey === 'benzingaIndia' ? (
              <GoogleNewsButton urlKey={googleNewsUrlKey} />
            ) : !showAdvertiserDisclosure && isApple ? (
              <AppleNewsButton />
            ) : deviceType === 'mobile' ? (
              <ReadInAppButton />
            ) : (
              <GoogleNewsButton />
            )}
          </NoFirstRender>
        </div>
      </div>

      {!!showAdvertiserDisclosure && <AdvertiserDisclosure articleData={articleData} slug={layout?.disclosure_slug} />}

      {keyItems && keyItems.length > 0 && (
        <div className="key-points-wrapper">
          <KeyPoints data={keyItems} />
        </div>
      )}

      <ImagePlacement
        caption={articleData?.assets?.source}
        primaryImage={articleData.primaryImage || { url: '/next-assets/images/benzinga-post-card-placeholder.svg' }}
        title={articleData?.title}
      />

      <ArticleContentWrapper
        className={classNames('article-content-body', { 'is-headline': articleData?.isHeadline })}
        id={`node-${articleData.nodeId}`}
      >
        {Array.isArray(layout?.content_header?.blocks) && <ArticleBlocks blocks={layout.content_header.blocks} />}

        <ErrorBoundary name="partial-article-layout-main-visible-page-tracker">
          <VisiblePageTracker
            article={articleData}
            executeImpressionEvent={executeImpressionEvent}
            executePageviewEvent={executePageviewEvent}
            executeReplaceHistory={executeReplaceHistory}
            meta={articleData.metaProps}
            onImpress={() => onArticleVisible(articleData)}
            trackMeta={trackMeta}
          >
            <div style={{ height: 2 }} />
          </VisiblePageTracker>
        </ErrorBoundary>

        <PreviewTextWrapper>
          {articleData?.isHeadline ? (
            <div className="" dangerouslySetInnerHTML={{ __html: printProHeadlineArticleContent() }} />
          ) : (
            <div className="preview-text" dangerouslySetInnerHTML={{ __html: previewText ?? '' }} />
          )}
        </PreviewTextWrapper>

        <React.Suspense fallback={<div />}>
          <div className="read-more-box">
            <Button as="a" href={articleUrl} onClick={handleReadMore} variant={loadMoreButtonVariant}>
              Read More
              <Icon className="chevron-right-icon" icon={faChevronRight} />
            </Button>
            <ShareButtons
              className="flex"
              showFontAwesomeIcons={showFontAwesomeIcons}
              showWhatsAppIcon={showWhatsAppIcon}
              tickers={tickers}
              title={title}
              url={articleUrl}
            />
          </div>
        </React.Suspense>

        <hr />

        <ErrorBoundary name="partial-article-layout-main-visible-page-tracker">
          <VisiblePageTracker
            article={articleData}
            executeImpressionEvent={false}
            executePageviewEvent={false}
            meta={articleData.metaProps}
            trackMeta={trackMeta}
          >
            <div />
          </VisiblePageTracker>
        </ErrorBoundary>

        {showPartnerAd && taboolaSettings?.placementMethod !== 'below-article' && (
          <GetBelowArticlePartnerAdBlock
            articleIndex={articleIndex}
            id={articleData.nodeId ?? ''}
            taboolaSettings={taboolaSettings}
            url={articleUrl}
          />
        )}

        {lastStory && (
          <ViewMoreWrapper>
            <Button
              as="a"
              className="top-stories-button"
              href={`/${articleScrollViewMoreLink}`}
              size="lg"
              variant="primary"
            >
              View More Top Stories
            </Button>
          </ViewMoreWrapper>
        )}
      </ArticleContentWrapper>
    </>
  );
};

const ViewMoreWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin: 20px 0;
`;

const PreviewTextWrapper = styled.div`
  margin-bottom: 1.5rem;
  .preview-text {
    font:
      Helvetica Neue,
      Helvetica,
      OpenSans,
      Arial,
      sans-serif;
    font-size: 1.1rem;
    line-height: 1.6;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 5; /* number of lines to show */
    -webkit-box-orient: vertical;
  }
`;
