import React from 'react';
import { ArticleBody } from './ArticleBody';
import { printProHeadlineArticleContent } from '@benzinga/article-manager';
import styled from '@benzinga/themetron';
import { BenzingaProLogo } from '@benzinga/logos-ui';
import { Button } from '@benzinga/core-ui';
import { Impression } from '@benzinga/analytics';
// import { YoutubeBlock } from '@benzinga/money';

const features = [
  'The most trusted brand in stock market news',
  'Fatest intelligence & alerts',
  'Active trader chat rooms',
  'Option trade alerts',
  'Only comprehensive mobile notifications solution',
  'Welcoming & white glove support',
];

export const ArticleHeadlineContent = () => {
  return (
    <Container>
      <ArticleBody body={printProHeadlineArticleContent()} />
      {/* <YoutubeBlock videoId="HfE5b6NxUis" /> */}
      <Impression campaign_id="bzheadline" unit_type="newsdeskheadline-page-ad">
        <a
          className="benzinga-pro-ad-box"
          href="https://pro.benzinga.com/register/?utm_source=bzheadline"
          rel="noreferrer"
          target="_blank"
        >
          <BenzingaProLogo />
          <ul className="features-list">
            {features.map(feature => (
              <li key={feature}>{feature}</li>
            ))}
          </ul>
          <Button variant="flat-blue">Active My Free Trial</Button>
        </a>
      </Impression>
    </Container>
  );
};

const Container = styled.div`
  display: block;

  .youtube-block iframe {
    border: 1px solid ${({ theme }) => theme.colorPalette.black};
  }

  .benzinga-pro-ad-box {
    border: 4px solid ${({ theme }) => theme.colors.brandLight};
    background-color: ${({ theme }) => theme.colorPalette.blue50};
    padding: 1rem;
    margin-bottom: 1rem;
    display: block;

    .features-list {
      list-style-type: none;
      padding-left: 1em;
      margin: 0.3rem 0;
      color: ${({ theme }) => theme.colorPalette.gray600};

      li:before {
        content: '–';
        position: absolute;
        margin-left: -1em;
      }
    }

    button {
      margin-top: 0.5rem;
    }
  }
`;
