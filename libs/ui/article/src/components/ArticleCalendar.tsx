import React, { Suspense } from 'react';
import { CalendarsWidget } from '@benzinga/calendars';
import { ArticleData, getInitialTab, getInitialTickers } from '@benzinga/article-manager';
import subMonths from 'date-fns/subMonths';
import addMonths from 'date-fns/addMonths';

export interface ArticleCalendarWidgetProps {
  article: ArticleData;
}

export const ArticleCalendarWidget: React.FC<ArticleCalendarWidgetProps> = ({ article }) => {
  const initialTab = getInitialTab(article);
  const initialTickers = getInitialTickers(article);

  if (!initialTab || !getInitialTickers(article)?.length) {
    return null;
  }

  const params = { dateFrom: subMonths(new Date(), 3), dateTo: addMonths(new Date(), 1) };

  return (
    <Suspense fallback={<div />}>
      <CalendarsWidget initialTab={initialTab} initialTickers={initialTickers} params={params} visibleTitle={false} />
    </Suspense>
  );
};

export default ArticleCalendarWidget;
