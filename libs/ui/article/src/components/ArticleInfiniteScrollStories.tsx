'use client';

import React from 'react';
import InfiniteScroll from 'react-infinite-scroller';
import { InView } from 'react-intersection-observer';
import { SessionContext } from '@benzinga/session-context';
import { manageTaboolaPosition, ArticleData } from '@benzinga/article-manager';
import { ButtonVariant, ErrorBoundary, Spinner } from '@benzinga/core-ui';

import { NewPartialArticleLayout } from './NewPartialArticleLayout';
import { PartialArticleLayout } from './PartialArticleLayout';
import { TaboolaSettings } from './ArticleLayoutMain';
import { useInfiniteArticles } from '../hooks';
import { Session } from '@benzinga/session';
import { TrackingManager } from '@benzinga/tracking-manager';
import { isGlobalImpressionStored, storeGlobalImpression } from '@benzinga/content-manager';
interface ArticleInfiniteScrollProps {
  articleData: ArticleData;
  deviceType?: string | null;
  googleNewsUrlKey?: string;
  loadMoreButtonVariant?: ButtonVariant;
  showCommentButton?: boolean;
  showFontAwesomeIcons?: boolean;
  showPartnerAd?: boolean;
  showWhatsAppIcon?: boolean;
  taboolaSettings?: TaboolaSettings;
  articleScrollViewMoreLink?: string;
  useNewTemplate?: boolean;
}

interface ArticleLayoutProps {
  articleData: ArticleData;
  articleIndex: number;
  lastStory: boolean;
  showPartnerAd: boolean;
  commonProps: CommonLayoutProps;
}

interface CommonLayoutProps {
  articleScrollViewMoreLink?: string;
  campaignStrategy: 'none';
  deviceType?: string | null;
  executeImpressionEvent: boolean;
  executePageviewEvent: boolean;
  executeReplaceHistory: boolean;
  googleNewsUrlKey: string;
  loadMoreButtonVariant?: ButtonVariant;
  partialView: boolean;
  primis: boolean;
  session: Session;
  showCommentButton?: boolean;
  showFontAwesomeIcons?: boolean;
  showWhatsAppIcon?: boolean;
  taboolaSettings?: TaboolaSettings;
  trackMeta: boolean;
  useNewTemplate?: boolean;
}

const INFINITE_ARTICLE_INITIAL_COUNT = 4;

const ArticleLayoutSpinner = () => (
  <div className="w-full max-w-[1080px] my-10 mx-auto">
    <Spinner />
  </div>
);

const ArticleLayout: React.FC<ArticleLayoutProps> = ({
  articleData,
  articleIndex,
  commonProps,
  lastStory,
  showPartnerAd,
}) => {
  const LayoutComponent = commonProps.useNewTemplate ? NewPartialArticleLayout : PartialArticleLayout;

  return (
    <ErrorBoundary name="partial-article-layout">
      <React.Suspense fallback={<div />}>
        <LayoutComponent
          articleData={articleData}
          articleIndex={articleIndex}
          lastStory={lastStory}
          showPartnerAd={showPartnerAd}
          {...commonProps}
        />
      </React.Suspense>
    </ErrorBoundary>
  );
};

export const ArticleInfiniteScrollStories: React.FC<ArticleInfiniteScrollProps> = ({
  articleData,
  articleScrollViewMoreLink,
  deviceType,
  googleNewsUrlKey = 'benzinga',
  loadMoreButtonVariant,
  showCommentButton,
  showFontAwesomeIcons,
  showPartnerAd = true,
  showWhatsAppIcon,
  taboolaSettings,
  useNewTemplate,
}) => {
  const session = React.useContext(SessionContext);
  const showPartnerRotateArticleIndex = manageTaboolaPosition();

  const { infiniteScrollArticles, infiniteScrollArticlesIDs, loadInfiniteArticle } = useInfiniteArticles(
    articleData,
    session,
    {
      initialCount: INFINITE_ARTICLE_INITIAL_COUNT,
    },
  );

  const commonProps: CommonLayoutProps = {
    articleScrollViewMoreLink,
    campaignStrategy: 'none',
    deviceType,
    executeImpressionEvent: true,
    executePageviewEvent: false,
    executeReplaceHistory: true,
    googleNewsUrlKey,
    loadMoreButtonVariant,
    partialView: true,
    primis: false,
    session: session.getSession(),
    showCommentButton,
    showFontAwesomeIcons,
    showWhatsAppIcon,
    taboolaSettings,
    trackMeta: true,
    useNewTemplate,
  };

  if (!Array.isArray(infiniteScrollArticlesIDs)) {
    return null;
  }

  if (!infiniteScrollArticlesIDs.length) {
    return <ArticleLayoutSpinner />;
  }

  const handleLoadMore = (index: number) => {
    session.getManager(TrackingManager).trackTableEvent('load_more', 'article-infinite-scroll', 'news', {});
    loadInfiniteArticle(index);
  };

  const registerImpression = (isVisible: boolean, article: ArticleData) => {
    if (isVisible && !isGlobalImpressionStored(`${article?.nodeId}`)) {
      storeGlobalImpression(`${article?.nodeId}`);
      const sponsored = article?.meta?.Flags?.ShowAdvertiserDisclosure;
      session.getManager(TrackingManager).trackCampaignEvent('view', {
        partner_id: (article?.meta?.Reach?.Disclosure?.tid || 0).toString(),
        sponsored: String(sponsored),
        unit_type: sponsored ? `sponsored-item-${article?.nodeId}` : `article-infinite-scroll-${article?.nodeId}`,
      });
    }
  };

  return (
    <div className="min-h-[300px]">
      <InfiniteScroll
        hasMore={infiniteScrollArticlesIDs.length !== infiniteScrollArticles.length}
        loader={<ArticleLayoutSpinner key="article-layout-spinner" />}
        loadMore={(index: number) => handleLoadMore(index)}
        pageStart={INFINITE_ARTICLE_INITIAL_COUNT - 1}
        threshold={700}
      >
        {infiniteScrollArticles?.map((articleData, index) => {
          const offsetIndex = index + 1;
          const updatedShowPartnerAd =
            taboolaSettings?.placementMethod === 'interval'
              ? true
              : showPartnerAd
                ? offsetIndex === showPartnerRotateArticleIndex
                : false;

          return (
            <InView
              key={`${articleData.nodeId}-${index}`}
              onChange={isVisible => registerImpression(isVisible, articleData)}
              rootMargin="100px 0px"
            >
              <ArticleLayout
                articleData={articleData}
                articleIndex={offsetIndex}
                commonProps={commonProps}
                lastStory={infiniteScrollArticlesIDs.length - 1 === index}
                showPartnerAd={updatedShowPartnerAd}
              />
            </InView>
          );
        })}
      </InfiniteScroll>
    </div>
  );
};
