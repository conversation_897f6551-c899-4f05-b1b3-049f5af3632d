'use client';
import i18n from 'i18next';

import { initReactI18next } from './commonExports';
import { TRANSLATION_RESOURCES } from './resources';
import { DEFAULT_NAMESPACE, DEFAULT_LANGUAGE } from './language';

export let language = DEFAULT_LANGUAGE;

if (typeof window !== 'undefined' && window['language']) {
  language = window['language'] ?? DEFAULT_LANGUAGE;
}

i18n.use(initReactI18next).init({
  fallbackLng: DEFAULT_LANGUAGE,
  fallbackNS: DEFAULT_NAMESPACE,
  interpolation: { escapeValue: false },
  lng: language,
  resources: TRANSLATION_RESOURCES,
});
i18n.changeLanguage(language);

export default i18n;
