'use client';
import React from 'react';
import styled, { ThemeContext } from '@benzinga/themetron';
import {
  Notification,
  NotificationManager,
  NotificationReceivedEvent,
  NotificationsManagerEvent,
  ReadStatus,
} from '@benzinga/notification-manager';
import { Checkbox, Collapse, Dropdown, MenuProps, Radio } from 'antd';
import { SessionContext } from '@benzinga/session-context';
import { throttle } from 'lodash';
import Hooks from '@benzinga/hooks';
import { WidgetContextProvider } from '@benzinga/widget-tools';
import { connect } from 'react-redux';
import { TempSpinner } from './TempSpinner';
import { PanelHeader } from './PanelHeader';
import { PanelBody } from './PanelBody';
import { setNotificationCenterLinkingId } from './actions';
import { HistoryError } from './HistoryError';
import { IoMdFunnel } from 'react-icons/io';
import { IoChevronDown } from 'react-icons/io5';
import { DesktopNotificationManager } from '@benzinga/desktop-notification-manager';
import { MdMonetizationOn, MdChat } from 'react-icons/md';
import { getNotificationData } from './utils';
import { PiWarningOctagonFill } from 'react-icons/pi';
import {
  useSymbolSearchModule,
  useKeywordSearchModule,
  TagSearch,
  useLinkingSearchModule,
  useClearTool,
  useClipboardTool,
  useDropdownTool,
} from '@benzinga/pro-ui';
import { SearchItem } from '@benzinga/search-modules';

import { StockSymbol } from '@benzinga/session';
import { HiMiniBookOpen } from 'react-icons/hi2';
import { WidgetLinkingManager } from '@benzinga/widget-linking';
import { useNavigate } from 'react-router-dom';
import { filterByDateRange, useNotificationFilters } from './useNotificationFilters';
import { FaNewspaper } from 'react-icons/fa';
import { hexToRGBA, toTitleCase } from '@benzinga/utils';
import { ActiveSmallSearch } from '@benzinga/themed-icons';
import { DateFilter, NotificationFilters, NotificationType } from '../entity';
import { MenuInfo } from './entity';

interface OwnProps {
  sources: SearchItem[];
  onSourceChanged: (source: SearchItem[]) => void;
  filters: NotificationFilters;
  onFiltersChanged: (filters: NotificationFilters) => void;
}
interface DispatchableActions {
  setNotificationCenterLinkingId: ReturnType<typeof setNotificationCenterLinkingId>;
}
export interface SearchQueryProps {
  symbols: StockSymbol[];
  keywords: string[];
}

type Props = OwnProps & DispatchableActions;

const NotificationsContainer: React.FC<Props> = props => {
  const session = React.useContext(SessionContext);
  const dropdownRef = React.useRef<HTMLDivElement>(null);
  const isLoadingRef = React.useRef<boolean>(false);
  const openedIdsRef = React.useRef<number[]>([]);
  const [notificationsError, setNotificationsError] = React.useState<boolean>(false);
  const syncInterval = React.useRef<NodeJS.Timeout | null>(null);
  const [timeUpdate, setTimeUpdate] = React.useState<number>(0);
  const [keepDropdownOpen, setKeepDropdownOpen] = React.useState<boolean>(false);
  const update = Hooks.useForceUpdate();

  const notificationManager = session.getManager(NotificationManager);
  const [notifications, setNotifications] = React.useState<Notification[]>(notificationManager.getNotifications());
  const notificationsActiveKey = React.useRef<number | undefined | number[]>(undefined);
  const onFiltersChanged = props.onFiltersChanged;

  const setNotificationsActiveKey = React.useCallback((key: number[] | number) => {
    notificationsActiveKey.current = key;
  }, []);

  const navigate = useNavigate();
  React.useEffect(() => {
    const notificationId = new URLSearchParams(window.location.search).get('open-notification');
    if (notificationId) {
      setNotificationsActiveKey(Number(notificationId));

      navigate(window.location.pathname);
    }
  }, [navigate, setNotificationsActiveKey]);

  Hooks.useSubscriber(notificationManager, async (event: NotificationsManagerEvent) => {
    switch (event.type) {
      case 'notifications:update': {
        const n = notificationManager.getNotifications();
        setNotifications(n);
        break;
      }
      case 'notifications:notification_received': {
        const { notification } = event as NotificationReceivedEvent;
        const { notificationType } = getNotificationData(notification);
        const options: NotificationOptions = {
          body: notification.data.title,
        };
        // if no push registered, use DesktopNotificationManager
        const pushRegistered = notificationManager.isPushRegistered();
        if (!pushRegistered) {
          session.getManager(DesktopNotificationManager).dispatchNotification(notificationType, options, () => {
            setNotificationsActiveKey(notification.id);
          });
        }
        break;
      }
      case 'notifications:push_notification_clicked': {
        const { id } = event as any;
        if (!notificationsActiveKey.current) {
          setNotificationsActiveKey(id);
        } else if (notificationsActiveKey.current !== undefined) {
          const activeKeys = Array.isArray(notificationsActiveKey.current)
            ? notificationsActiveKey.current
            : [notificationsActiveKey.current];
          activeKeys.push(id);
          setNotificationsActiveKey(activeKeys);
        }
        break;
      }
    }
  });

  const onTagsUpdated = React.useCallback(
    (tags: SearchItem[]) => {
      const onSourceChanged = props.onSourceChanged;
      onSourceChanged(tags);
    },
    [props.onSourceChanged],
  );

  const symbolModule = useSymbolSearchModule();
  const keywordModule = useKeywordSearchModule();
  const linkingModule = useLinkingSearchModule();

  const modules = React.useMemo(
    () => [symbolModule, keywordModule, linkingModule],
    [symbolModule, keywordModule, linkingModule],
  );
  const clearTool = useClearTool(
    React.useCallback(() => {
      onTagsUpdated([]);
    }, [onTagsUpdated]),
  );
  const dropDownTool = useDropdownTool();
  const clipboardTool = useClipboardTool();
  const tools = React.useMemo(() => [dropDownTool, clearTool, clipboardTool], [dropDownTool, clearTool, clipboardTool]);

  const linkingManager = session.getManager(WidgetLinkingManager);
  const widgetLinks = linkingManager.getWidgetLinks();
  const sources = React.useMemo(() => {
    const filteredLinks = widgetLinks.filter(link => props.sources.some(source => source.id === link.id));
    return [...props.sources.map(source => source.id), ...filteredLinks.map(link => link.events[0].symbol)] as string[];
  }, [props.sources, widgetLinks]);

  const filteredNotifications = useNotificationFilters(notifications, props.filters, sources);
  const handleNotificationTypeSelection = React.useCallback(
    (value: NotificationType) => _event => {
      onFiltersChanged({
        ...props.filters,
        notificationCategory: props.filters.notificationCategory.includes(value)
          ? props.filters.notificationCategory.filter(item => value !== item)
          : [...props.filters.notificationCategory, value],
      });
    },
    [onFiltersChanged, props.filters],
  );
  const viewReadMessages = React.useCallback(
    (_event: React.SyntheticEvent) => {
      openedIdsRef.current = [];
      onFiltersChanged({ ...props.filters, statusBasedNotification: 'read' });
    },
    [onFiltersChanged, props.filters],
  );
  const setDateRange = React.useCallback(
    (key: MenuInfo) => {
      onFiltersChanged({ ...props.filters, currentDateRange: key.key as unknown as DateFilter });
    },
    [onFiltersChanged, props.filters],
  );

  const handleMarkAllAsReadClick = React.useCallback(
    (_event: React.SyntheticEvent) => {
      notificationManager.markReadUnread([], [], true);
    },
    [notificationManager],
  );

  const updateOpenedIds = React.useCallback((id: number) => {
    openedIdsRef.current = [...new Set([...openedIdsRef.current, id])];
  }, []);

  const markUnread = React.useCallback(
    (id: number, read: boolean) => {
      const readIds: number[] = [];
      const unreadIds: number[] = [];
      if (read) {
        unreadIds.push(id);
      } else {
        readIds.push(id);
        updateOpenedIds(id);
      }
      notificationManager.markReadUnread(readIds, unreadIds);
    },
    [notificationManager, updateOpenedIds],
  );

  const handleAllClick = React.useCallback(() => {
    onFiltersChanged({ ...props.filters, currentDateRange: DateFilter.ALL });
  }, [onFiltersChanged, props.filters]);

  const handleDisplayStatusChange = React.useCallback(
    (checked: boolean, statusType: ReadStatus) => (_: React.MouseEvent<HTMLDivElement, MouseEvent>) =>
      onFiltersChanged({ ...props.filters, statusBasedNotification: checked ? statusType : 'all' }),
    [onFiltersChanged, props.filters],
  );

  const filterOptions: MenuProps['items'] = React.useMemo(
    () => [
      {
        key: 'Unread Only',
        label: (
          <StyledRadioWrapper onClick={handleDisplayStatusChange(true, 'unread')}>
            <StyledSpan>Unread Only</StyledSpan>
            <Radio checked={props.filters.statusBasedNotification === 'unread'} />
          </StyledRadioWrapper>
        ),
      },
      {
        key: 'Read Only',
        label: (
          <StyledRadioWrapper onClick={handleDisplayStatusChange(true, 'read')}>
            <StyledSpan>Read Only</StyledSpan>
            <Radio checked={props.filters.statusBasedNotification === 'read'} />
          </StyledRadioWrapper>
        ),
      },
      {
        type: 'divider',
      },
      {
        icon: <HiMiniBookOpen size={16} />,
        key: 'Events',
        label: (
          <StyledBaseFilterOption onClick={handleNotificationTypeSelection(NotificationType.EVENT_ALERT)}>
            <StyledSpan>Events</StyledSpan>
            <StyledCheckbox checked={props.filters.notificationCategory.includes(NotificationType.EVENT_ALERT)} />
          </StyledBaseFilterOption>
        ),
      },
      {
        icon: <FaNewspaper size={16} />,
        key: 'Articles',
        label: (
          <StyledBaseFilterOption onClick={handleNotificationTypeSelection(NotificationType.WATCHLIST_NEWS_ALERT)}>
            <StyledSpan>Articles</StyledSpan>
            <StyledCheckbox
              checked={props.filters.notificationCategory.includes(NotificationType.WATCHLIST_NEWS_ALERT)}
            />
          </StyledBaseFilterOption>
        ),
      },

      {
        icon: <MdChat size={16} />,
        key: 'Chat',
        label: (
          <StyledBaseFilterOption onClick={handleNotificationTypeSelection(NotificationType.CHAT_ALERT)}>
            <StyledSpan>Chat</StyledSpan>
            <StyledCheckbox checked={props.filters.notificationCategory.includes(NotificationType.CHAT_ALERT)} />
          </StyledBaseFilterOption>
        ),
      },
      {
        icon: <MdMonetizationOn size={16} />,
        key: 'Price',
        label: (
          <StyledBaseFilterOption onClick={handleNotificationTypeSelection(NotificationType.PRICE_ALERT)}>
            <StyledSpan>Price</StyledSpan>
            <StyledCheckbox checked={props.filters.notificationCategory.includes(NotificationType.PRICE_ALERT)} />
          </StyledBaseFilterOption>
        ),
      },
    ],
    [
      handleDisplayStatusChange,
      props.filters.statusBasedNotification,
      props.filters.notificationCategory,
      handleNotificationTypeSelection,
    ],
  );

  const dateRangeOptions: MenuProps['items'] = React.useMemo(
    () => [
      {
        key: DateFilter.OLDER_THAN_A_DAY,
        label: (
          <StyledBaseFilterOption>
            <StyledSpan>Older than 1 day</StyledSpan>
            <StyledTag>{filterByDateRange(filteredNotifications, DateFilter.OLDER_THAN_A_DAY).length}</StyledTag>
          </StyledBaseFilterOption>
        ),
      },

      {
        key: DateFilter.OLDER_THAN_THREE_DAYS,
        label: (
          <StyledBaseFilterOption>
            <StyledSpan>Older than 3 days</StyledSpan>
            <StyledTag>{filterByDateRange(filteredNotifications, DateFilter.OLDER_THAN_THREE_DAYS).length}</StyledTag>
          </StyledBaseFilterOption>
        ),
      },
      {
        key: DateFilter.OLDER_THAN_A_WEEK,
        label: (
          <StyledBaseFilterOption>
            <StyledSpan>Older than 1 week</StyledSpan>
            <StyledTag>{filterByDateRange(filteredNotifications, DateFilter.OLDER_THAN_A_WEEK).length}</StyledTag>
          </StyledBaseFilterOption>
        ),
      },
      {
        key: DateFilter.OLDER_THAN_A_MONTH,
        label: (
          <StyledBaseFilterOption>
            <StyledSpan>Older than 1 month</StyledSpan>
            <StyledTag>{filterByDateRange(filteredNotifications, DateFilter.OLDER_THAN_A_MONTH).length}</StyledTag>
          </StyledBaseFilterOption>
        ),
      },
      {
        type: 'divider',
      },
      {
        key: DateFilter.ALL,
        label: (
          <StyledBaseFilterOption onClick={handleAllClick}>
            <StyledSpan>All</StyledSpan>
            <StyledTag>{notifications.length}</StyledTag>
          </StyledBaseFilterOption>
        ),
      },
    ],
    [filteredNotifications, handleAllClick, notifications.length],
  );
  const updateIsLoading = React.useCallback(
    (active: boolean) => {
      isLoadingRef.current = active;
      update();
    },
    [update],
  );

  const loadMoreThrottled = React.useMemo(
    () =>
      throttle((status: ReadStatus) => {
        notificationManager.loadMore(status);
      }, 500),
    [notificationManager],
  );

  const loadMoreOnScroll = React.useCallback(
    (event: Event) => {
      const element = event.target as HTMLDivElement;
      if (
        element.scrollHeight - element.scrollTop - element.clientHeight < 50 &&
        !isLoadingRef.current &&
        !notificationsError &&
        props.filters.notificationCategory.length !== 0 &&
        notificationManager.hasMore(props.filters.statusBasedNotification)
      ) {
        loadMoreThrottled(props.filters.statusBasedNotification);
        updateIsLoading(true);
      }
    },
    [
      notificationsError,
      props.filters.notificationCategory.length,
      props.filters.statusBasedNotification,
      notificationManager,
      loadMoreThrottled,
      updateIsLoading,
    ],
  );

  React.useEffect(() => {
    updateIsLoading(false);
  }, [updateIsLoading]);

  React.useEffect(() => {
    if (syncInterval.current) {
      clearInterval(syncInterval.current);
    }
    syncInterval.current = setInterval(() => {
      setTimeUpdate(Date.now());
    }, 60 * 1000);
    return () => {
      if (syncInterval.current) {
        clearInterval(syncInterval.current);
      }
    };
  }, []);

  React.useEffect(() => {
    const dropdown = dropdownRef.current;
    dropdown?.addEventListener('scroll', loadMoreOnScroll);
    return () => {
      dropdown?.removeEventListener('scroll', loadMoreOnScroll);
      setNotificationsActiveKey(0);
    };
  }, [loadMoreOnScroll, setNotificationsActiveKey]);

  const panels = React.useMemo(
    () =>
      filteredNotifications.reduce((acc, notification) => {
        if (
          (props.filters.statusBasedNotification === 'all' ||
            !notification.read ||
            notification.read ||
            openedIdsRef.current.includes(notification.id)) &&
          props.filters.notificationCategory.length !== 0
        ) {
          acc.push(
            <Panel
              collapsible={notification.data.title.length === 0 ? 'disabled' : undefined}
              header={<PanelHeader markUnread={markUnread} notification={notification} timeUpdate={timeUpdate} />}
              key={notification.id}
              showArrow={false}
            >
              <PanelBody notification={notification} updateOpenedIds={updateOpenedIds} />
            </Panel>,
          );
        }
        return acc;
      }, [] as JSX.Element[]),
    [
      filteredNotifications,
      props.filters.statusBasedNotification,
      props.filters.notificationCategory.length,
      markUnread,
      timeUpdate,
      updateOpenedIds,
    ],
  );

  Hooks.useSubscriber(notificationManager, async (event: NotificationsManagerEvent) => {
    switch (event.type) {
      case 'notifications:get_history': {
        updateIsLoading(false);
        setNotificationsError(false);
        break;
      }
      case 'notifications:get_history_error': {
        updateIsLoading(false);
        setNotificationsError(true);
        break;
      }
      case 'notifications:push_notification_clicked': {
        const { id } = event as any;
        markUnread(id, false);
        break;
      }
    }
  });

  React.useEffect(() => {
    if (
      notificationManager.hasMore(props.filters.statusBasedNotification) &&
      panels.length < 50 &&
      props.filters.notificationCategory.length !== 0 &&
      !notificationsError
    ) {
      loadMoreThrottled(props.filters.statusBasedNotification);
      updateIsLoading(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.filters.statusBasedNotification]);

  const handleDropdownStateChange = React.useCallback(() => setKeepDropdownOpen(!keepDropdownOpen), [keepDropdownOpen]);
  const handleMenuItemClick = React.useCallback(
    event => (event.key.length > 0 ? setKeepDropdownOpen(true) : setKeepDropdownOpen(false)),
    [],
  );

  const theme = React.useContext(ThemeContext);
  return (
    <WidgetContextProvider>
      <Container>
        <StyledSearchBar>
          <StyledTagSearch
            iconOverride={<ActiveSmallSearch />}
            modules={modules}
            onTagsUpdated={onTagsUpdated}
            placeholder={'Search by $Symbol or keyword...'}
            tags={props.sources}
            tools={tools}
          />
          <Dropdown
            menu={{
              items: filterOptions,
              onClick: handleMenuItemClick,
            }}
            onOpenChange={handleDropdownStateChange}
            open={keepDropdownOpen}
            overlayStyle={{
              border: `1px solid ${theme.colors.borderLight}`,
              width: '150px',
            }}
          >
            <StyledFunnel />
          </Dropdown>
        </StyledSearchBar>
        <OptionsWrapper>
          <OptionsDiv>
            <StyledSpan>{toTitleCase(props.filters.statusBasedNotification)}</StyledSpan>
            <StyledTag>{filteredNotifications.length}</StyledTag>
          </OptionsDiv>
          <OptionsDiv>
            {props.filters.statusBasedNotification === 'unread' && (
              <StyledMarkAsReadText onClick={handleMarkAllAsReadClick}>Mark all Read</StyledMarkAsReadText>
            )}
            <Dropdown
              menu={{ items: dateRangeOptions, onClick: setDateRange }}
              overlayStyle={{
                width: '170px',
              }}
              trigger={['click']}
            >
              <StyledChevronDownIcon />
            </Dropdown>
          </OptionsDiv>
        </OptionsWrapper>
        <DropdownBody ref={dropdownRef}>
          <Collapse bordered={true} defaultActiveKey={notificationsActiveKey.current} ghost>
            {panels}
          </Collapse>
          {isLoadingRef.current && <TempSpinner destroy={false} height="36px" />}
          <HistoryError showError={notificationsError} />
          {props.filters.notificationCategory.length === 0 ? (
            filteredNotifications.length === 0 ? (
              <MessageWrapper>
                <DefaultTail>
                  <PiWarningOctagonFill fill={theme.colors.brand} size={20} />
                  You are caught up!
                  <LinkToAll onClick={viewReadMessages}>View read messages</LinkToAll>
                </DefaultTail>
              </MessageWrapper>
            ) : (
              <MessageWrapper>
                <PiWarningOctagonFill fill={theme.colors.brand} size={50} />
                <div>
                  You have{' '}
                  <b>
                    {filteredNotifications.length} {props.filters.statusBasedNotification} notifications
                  </b>
                  , but no categories are selected. Please select a category to view the notifications.
                </div>
              </MessageWrapper>
            )
          ) : filteredNotifications.length === 0 ? (
            <MessageWrapper>
              <PiWarningOctagonFill fill={theme.colors.brand} size={50} />
              <div>
                You have <b>no notifications</b> in this category, try adding other categories to view your
                notifications.
              </div>
            </MessageWrapper>
          ) : (
            <MessageWrapper>
              <DefaultTail>
                <PiWarningOctagonFill fill={theme.colors.brand} size={20} />
                You are caught up!
                <LinkToAll onClick={viewReadMessages}>View read messages</LinkToAll>
              </DefaultTail>
            </MessageWrapper>
          )}
        </DropdownBody>
      </Container>
    </WidgetContextProvider>
  );
};

const mapDispatchToProps = {
  setNotificationCenterLinkingId,
};

export default connect(null, mapDispatchToProps)(NotificationsContainer) as unknown as React.FC<OwnProps>;

const StyledMarkAsReadText = styled.span`
  color: ${({ theme }) => theme.colors.foreground};
  cursor: pointer;
  &:hover {
    transition: color 0.2s ease-in-out;
    color: ${({ theme }) => theme.colors.brand};
  }
`;

const Container = styled.div`
  padding: 1px 0px;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.foreground};
  width: 100%;
  font-size: 1em;
  cursor: initial;
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const LinkToAll = styled.a`
  &:hover {
    border-bottom: 1px solid ${props => props.theme.colors.brand};
    color: ${props => props.theme.colors.brand};
    text-decoration: none;
  }
  align-self: center;
  text-align: end;
  text-transform: none;
  margin-left: 0.5em;
`;

const Tail = styled.div`
  padding: 10px;
`;

const MessageWrapper = styled(Tail)`
  display: flex;
  width: 100%;
  gap: 1.5em;
  margin-top: 1em;

  background-color: ${props => hexToRGBA(props.theme.colors.brand, 0.1)};
  border-radius: 8px;
  align-items: flex-start;
  div {
  }
`;

const DefaultTail = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

const DropdownBody = styled.div`
  padding: 10px;
  padding-top: 0px;
  overflow-y: scroll;
  @media screen and (min-height: 800px) {
    max-height: 100vh;
    padding-bottom: 15em;
  }
`;

const StyledChevronDownIcon = styled(IoChevronDown)`
  cursor: pointer;
  &:hover {
    transition: color 0.2s ease-in-out;
    color: ${({ theme }) => theme.colors.brand};
  }
`;
const OptionsWrapper = styled.div`
  background: ${({ theme }) => theme.colors.backgroundActive};
  display: flex;
  justify-content: space-between;
  padding: 8px 16px 8px 16px;
  background: rgba(0, 118, 205, 0.08);
`;
const OptionsDiv = styled.div`
  display: flex;
  align-items: center;
  gap: 1em;
`;

const StyledSpan = styled.span`
  color: ${({ theme }) => theme.colors.foreground};
`;

const Panel = styled(Collapse.Panel)`
  text-align: justify;
  font-size: 14px !important;
  .ant-collapse-header {
    padding: 0px !important;
  }
  .ant-collapse-content-box {
    padding: 0px 8px !important;
  }
`;

const StyledSearchBar = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0px 16px;
  border: ${({ theme }) => `1px solid ${theme.colors.borderLight}`};
`;
const StyledTagSearch = styled(TagSearch)`
  border: none;
`;
const StyledTag = styled.div`
  width: max-content;
  height: max-content;
  background: ${({ theme }) => theme.colors.brand};
  color: ${({ theme }) => theme.colors.foreground};
  border-radius: 16px;
  padding: 3px 8px;
`;

const StyledFunnel = styled(IoMdFunnel)`
  cursor: pointer;
  &:hover {
    transition: color 0.2s ease-in-out;
    color: ${({ theme }) => theme.colors.brand};
  }
`;

const StyledBaseFilterOption = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const StyledRadioWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  .ant-radio-wrapper {
    margin: 0;
  }
  .ant-radio-inner {
    background-color: ${({ theme }) => theme.colors.borderLight};
    border-color: ${({ theme }) => theme.colors.borderLight};
  }
`;

const StyledCheckbox = styled(Checkbox)`
  .ant-checkbox-inner {
    background-color: ${({ theme }) => theme.colors.borderLight};
    border-color: ${({ theme }) => theme.colors.borderLight};
  }
  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: ${({ theme }) => theme.colors.brand};
    border-color: ${({ theme }) => theme.colors.brand};
    color: ${({ theme }) => theme.colors.foreground};
  }
`;
