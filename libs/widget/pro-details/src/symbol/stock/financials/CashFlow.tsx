import React from 'react';

import { NO_VALUE, numberInMillions } from '@benzinga/fission';

import renderDates from './renderDates';
import withSpinner from './withSpinner';
import { CashFlowStatement, Financial } from '@benzinga/securities-manager';

interface Props {
  financials?: Financial[];
  isLoading: boolean;
}

class CashFlow extends React.Component<Props> {
  renderField(financials: Financial[] | undefined, field: keyof CashFlowStatement) {
    return (
      financials?.map((statements, index) => {
        let cellValue = NO_VALUE;
        if (statements.cashFlowStatement) {
          cellValue = numberInMillions(statements.cashFlowStatement[field]) || NO_VALUE;
        }
        return <td key={`${index}-field`}>{cellValue}</td>;
      }) ?? []
    );
  }

  render() {
    const { financials } = this.props;
    return (
      <table className="FinancialTable" id="Financial-Cash_Flow-Table">
        <thead>
          <tr>
            <th>In Millions (Except per share Items)</th>
            {renderDates(financials)}
          </tr>
        </thead>
        <tbody>
          <tr></tr>
          <tr className="FinancialTable-section">
            <td>Net Income</td>
            {this.renderField(financials, 'netIncome')}
          </tr>
          <tr></tr>
          <tr className="FinancialTable-section">
            <td colSpan={5}>Operating Activities</td>
          </tr>
          <tr>
            <td>Depreciation & Amortization</td>
            {this.renderField(financials, 'depreciationAndAmortization')}
          </tr>
          <tr>
            <td>Changes in Accounts Receivables</td>
            {this.renderField(financials, 'changesInAccountReceivables')}
          </tr>
          <tr>
            <td>Changes in Accounts Payable</td>
            {this.renderField(financials, 'changeInAccountPayable')}
          </tr>
          <tr>
            <td>Changes in Inventory</td>
            {this.renderField(financials, 'changeInInventory')}
          </tr>
          <tr className="FinancialTable-total">
            <td>Total Cash Flow From Operating Activities</td>
            {this.renderField(financials, 'operatingCashFlow')}
          </tr>
          <tr></tr>
          <tr className="FinancialTable-section">
            <td colSpan={5}>Investing Activities</td>
          </tr>
          <tr>
            <td>Capital Expenditures</td>
            {this.renderField(financials, 'capitalExpenditure')}
          </tr>
          <tr>
            <td>Investments</td>
            {this.renderField(financials, 'netInvestmentPurchaseAndSale')}
          </tr>
          <tr>
            <td>Other Cash Flows From Investing Activities</td>
            {this.renderField(financials, 'netOtherInvestingChanges')}
          </tr>
          <tr className="FinancialTable-total">
            <td>Total Cash Flow From Investing Activities</td>
            {this.renderField(financials, 'investingCashFlow')}
          </tr>
          <tr></tr>
          <tr className="FinancialTable-section">
            <td colSpan={5}>Financing Activities</td>
          </tr>
          <tr>
            <td>Cash Dividends Paid</td>
            {this.renderField(financials, 'cashDividendsPaid')}
          </tr>
          <tr>
            <td>Net Proceeds From Sale of Common Stock</td>
            {this.renderField(financials, 'netCommonStockIssuance')}
          </tr>
          <tr>
            <td>Other Cash Flows From Financing Activities</td>
            {this.renderField(financials, 'netOtherFinancingCharges')}
          </tr>
          <tr className="FinancialTable-total">
            <td>Total Cash Flow From Financing Activities</td>
            {this.renderField(financials, 'financingCashFlow')}
          </tr>
          <tr className="FinancialTable-section">
            <td>Net Change In Cash</td>
            {this.renderField(financials, 'changesInCash')}
          </tr>
        </tbody>
      </table>
    );
  }
}

export default withSpinner<Props>(CashFlow as typeof React.Component);
