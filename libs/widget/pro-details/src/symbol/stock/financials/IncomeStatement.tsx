import React from 'react';

import { NO_VALUE, numberInMillions } from '@benzinga/fission';

import renderDates from './renderDates';
import withSpinner from './withSpinner';
import { Financial, IncomeStatement } from '@benzinga/securities-manager';

interface Props {
  financials?: Financial[];
  isLoading: boolean;
}

class IncomeStatementTab extends React.Component<Props> {
  renderField(financials: Financial[] | undefined, field: keyof IncomeStatement) {
    return (
      financials?.map((statements, index) => {
        let cellValue = NO_VALUE;
        if (statements.incomeStatement) {
          cellValue = numberInMillions(statements.incomeStatement[field]) || NO_VALUE;
        }
        return <td key={`${index}-field`}>{cellValue}</td>;
      }) ?? []
    );
  }

  render() {
    const { financials } = this.props;
    return (
      <table className="FinancialTable" id="Financial-Income_Statement-Table">
        <thead>
          <tr>
            <th>In Millions (Except per share Items)</th>
            {renderDates(financials)}
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Total Revenue</td>
            {this.renderField(financials, 'totalRevenue')}
          </tr>
          <tr>
            <td>Cost of Revenue</td>
            {this.renderField(financials, 'costOfRevenue')}
          </tr>
          <tr className="FinancialTable-total">
            <td>Gross Profit</td>
            {this.renderField(financials, 'grossProfit')}
          </tr>
          <tr>
            <td>Research & Development Expenses</td>
            {this.renderField(financials, 'researchAndDevelopment')}
          </tr>
          <tr>
            <td>Selling, General, & Admin Expenses</td>
            {this.renderField(financials, 'sellingGeneralAndAdministration')}
          </tr>
          <tr className="FinancialTable-total">
            <td>Total Operating Expenses</td>
            {this.renderField(financials, 'operatingExpense')}
          </tr>
          <tr className="FinancialTable-total">
            <td>Total Operating Income or Loss</td>
            {this.renderField(financials, 'operatingIncome')}
          </tr>
          <tr>
            <td>Income Before Tax</td>
            {this.renderField(financials, 'pretaxIncome')}
          </tr>
          <tr>
            <td>Tax Provision</td>
            {this.renderField(financials, 'taxProvision')}
          </tr>
          <tr>
            <td>Interest Expense</td>
            {this.renderField(financials, 'interestExpense')}
          </tr>
          <tr className="FinancialTable-total">
            <td>Net Income</td>
            {this.renderField(financials, 'netIncome')}
          </tr>
          <tr className="FinancialTable-total">
            <td>Net Income Applicable To Common Shares</td>
            {this.renderField(financials, 'netIncomeCommonStockholders')}
          </tr>
        </tbody>
      </table>
    );
  }
}

export default withSpinner<Props>(IncomeStatementTab as typeof React.Component);
