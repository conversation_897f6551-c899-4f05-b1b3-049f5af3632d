'use client';
import React, { Fragment, ReactNode } from 'react';

import Hooks from '@benzinga/hooks';
import { Expression, LoggingManager } from '@benzinga/session';
import { Permission } from '@benzinga/permission-manager';
import { SessionContext } from '@benzinga/session-context';
import styled from '@benzinga/themetron';
import { deepEqual, hasLength, sortByString, noop, isString } from '@benzinga/utils';
import { FeedbackFormCaller } from '@benzinga/pro-ui';
import { Modal, ModalContent } from '@benzinga/core-ui';
import { Badge, MillerColumns, MillerColumnsNode } from '@benzinga/miller-columns-ui';
import { BenzingaContext, PermissionedComponent } from '@benzinga/user-context';
import {
  Globe,
  Categories,
  Scanner,
  Watchlist as WatchlistIcon,
  Lock,
  Feedback,
  Sectors,
  AlertActive,
  AlertOff,
  Calendar,
} from '@benzinga/themed-icons';

import { PermissionsManager, PermissionsManagerEvent } from '@benzinga/permission-manager';
import {
  AdvancedNewsManager,
  Category,
  Source,
  SourceId,
  NewsfeedFieldType,
  SourceGroup,
  CategoryId,
} from '@benzinga/advanced-news-manager';
import { Watchlist, WatchlistManager, WatchlistManagerEvent } from '@benzinga/watchlist-manager';

import { NewsfeedInternalSettingsContext } from '../../context/internalSettingContext';
import {
  customImportancePreset,
  highImportancePreset,
  ImportanceFilters,
  lowImportancePreset,
  midImportancePreset,
  NewsfeedFeedSettings,
  offImportancePreset,
} from '../../entities/toolbar';
import { getBlankFilters } from '../../utils/filters';
import { ArticleViewMode } from '../ArticleViewMode';
import FilterField from '../FilterField';
import { renderInfo } from './toolbarInfo';
import { PresetFilter } from '../screenerFilters';
import { Checkbox, Slider, Tooltip } from 'antd';
import { DatePicker } from './DatePicker';
import { useTime } from '@benzinga/time-manager-hooks';
import { FiltersPanel as ScannerFilterPanel } from '@benzinga/pro-scanner-widget';
import { TrackingManager } from '@benzinga/tracking-manager';
import { ProContext } from '@benzinga/pro-tools';
import { useNewsfeedGlobalSettings } from '../../utils/useGlobalSettings';

import { DisplayType, Importance } from '@benzinga/news-user-settings';
import { FilterObject, TargetSource } from '@benzinga/quotes-v3-fields-manager';

interface Props {
  changeFeedExpression: (expression: Expression<NewsfeedFieldType>) => void;
  changeFeedSettings: (feedSettings: NewsfeedFeedSettings) => void;
  feedSettings: NewsfeedFeedSettings;
  feedExpression: Expression<NewsfeedFieldType>;
}

interface ToolbarButtonParams {
  badge: ReactNode;
  className: string;
  field: PresetFilter | null;
  icon: ReactNode;
  isFilterFieldComponent: boolean;
  label: ReactNode;
  path: any[];
  permission: Permission | Permission[];
  tooltip: string;
}
interface DateToolbarButtonParams {
  badge: ReactNode;
  icon: ReactNode;
  isFilterActive?: () => boolean;
  label: ReactNode;
  onClick: () => void;
  permission: Permission | Permission[];
  tooltip: string;
}

export interface Tag {
  name: string;
  type: 'symbol' | 'keyword' | 'fund' | 'author';
}

interface State {
  readonly field: PresetFilter | null;
  readonly filterDescription: string;
  readonly hasAccessToCategories: boolean;
  readonly hasAccessToImportance: boolean;
  readonly hasAccessToMarketCap: boolean;
  readonly hasAccessToPrice: boolean;
  readonly hasAccessToScreener: boolean;
  readonly hasAccessToSource: boolean;
  readonly hasAccessToVolume: boolean;
  readonly hasAccessToWatchlist: boolean;
  readonly isFilterField: boolean;
  readonly isOverFilter: boolean;
  readonly isDateModalOpen: boolean;
  readonly millerPath: any[]; // TODO;
  readonly showDesktopNotificationErrorModal: boolean;
  readonly showMillerColumns: boolean;
}

const isActiveFilter = <
  T extends keyof Omit<NewsfeedFeedSettings, 'importance' | 'relevantCategories' | 'denyRelevantCategories'>,
>(
  filters: NewsfeedFeedSettings,
  property: T,
  id: NewsfeedFeedSettings[T][number],
) => filters[property].includes(id as string & FilterObject & SourceId) ?? false;

const isActiveCategoryFilter = (filters: NewsfeedFeedSettings, sourceId: SourceId, categoryId: CategoryId) =>
  filters.relevantCategories[sourceId]?.includes(categoryId) ?? false;

const isDenyCategoryFilter = (filters: NewsfeedFeedSettings, sourceId: SourceId, categoryId: CategoryId) =>
  filters.denyRelevantCategories[sourceId]?.includes(categoryId) ?? false;

const sourcesWithCategories: SourceId[] = [
  'benzinga_wire_india',
  'benzinga_wire_arabic',
  'benzinga_wire_chinese_simplified',
  'benzinga_wire_espanol',
  'benzinga_wire_france',
  'benzinga_wire_french',
  'benzinga_wire_indonesian',
  'benzinga_wire_italia',
  'benzinga_wire_japanese',
  'benzinga_wire_portuguese',
  'benzinga_wire_spanish',
  'benzinga_wire_turkish',
  'story',
];

export const NewsfeedToolbar: React.FC<Props> = props => {
  const session = React.useContext(SessionContext);
  const newsfeedSetting = React.useContext(NewsfeedInternalSettingsContext);
  const newsfeedGlobalSetting = useNewsfeedGlobalSettings();
  const benzingaContext = React.useContext(BenzingaContext);
  const time = useTime();
  const allSources = React.useMemo(() => session.getManager(AdvancedNewsManager).getStoredSources() ?? [], [session]);
  const allGroups = React.useMemo(
    () => session.getManager(AdvancedNewsManager).getStoredSourcesGroups() ?? [],
    [session],
  );

  const update = Hooks.useForceUpdate();

  const [state, setState] = React.useState<State>({
    field: null,
    filterDescription: '',
    hasAccessToCategories: false,
    hasAccessToImportance: false,
    hasAccessToMarketCap: false,
    hasAccessToPrice: false,
    hasAccessToScreener: false,
    hasAccessToSource: false,
    hasAccessToVolume: false,
    hasAccessToWatchlist: false,
    isDateModalOpen: false,
    isFilterField: false,
    isOverFilter: false,
    millerPath: [],
    showDesktopNotificationErrorModal: false,
    showMillerColumns: false,
  });

  const { changeFeedSettings, feedSettings } = props;

  const wrapperRef = React.useRef<HTMLDivElement>(null);

  const filterFieldBackgroundClick = React.useCallback((ev: MouseEvent) => {
    const target = ev.target as Element;
    if (wrapperRef.current && !wrapperRef.current?.contains(target)) {
      setTimeout(() => setState(og => ({ ...og, isFilterField: false })), 100);
    }
  }, []);

  const getNewsFeedPermission = React.useCallback(() => {
    const permissionManager = session.getManager(PermissionsManager);
    const hasAccessToAdvancedQuery = permissionManager.hasAccess('bzpro/newsfeed/advancedQuery').ok ?? false;

    const hasAccess = (action: string) => {
      return permissionManager.hasAccess(`bzpro/newsfeed/filter${action ? `/${action}` : ''}`).ok;
    };

    const hasAccessToAll = hasAccess('');
    const hasAccessToWatchlist =
      (hasAccessToAll ||
        (hasAccess('watchlists') && permissionManager.hasAccess('bzpro/widget/use', 'watchlist').ok)) ??
      false;
    const hasAccessToSource = (hasAccessToAll || hasAccess('sources')) ?? false;
    const hasAccessToCategories = (hasAccessToAll || hasAccess('categories')) ?? false;
    const hasAccessToImportance = (hasAccessToAll || hasAccess('importance')) ?? false;
    const hasAccessToScreener = (hasAccessToAll || hasAccess('screener')) ?? false;
    const hasAccessToPrice = (hasAccessToAll || hasAccess('price')) ?? false;
    const hasAccessToMarketCap = (hasAccessToAll || hasAccess('marketcap')) ?? false;
    const hasAccessToVolume = (hasAccessToAll || hasAccess('volume')) ?? false;
    return {
      hasAccessToAdvancedQuery,
      hasAccessToCategories,
      hasAccessToImportance,
      hasAccessToMarketCap,
      hasAccessToPrice,
      hasAccessToScreener,
      hasAccessToSource,
      hasAccessToVolume,
      hasAccessToWatchlist,
    };
  }, [session]);

  React.useEffect(() => {
    setState(s => ({
      ...s,
      ...getNewsFeedPermission(),
    }));
    document.addEventListener('mousedown', filterFieldBackgroundClick);
    return () => {
      document.removeEventListener('mousedown', filterFieldBackgroundClick);
    };
  }, [filterFieldBackgroundClick, getNewsFeedPermission]);

  const watchlistManager = React.useMemo(() => session.getManager(WatchlistManager), [session]);
  const permissionManager = React.useMemo(() => session.getManager(PermissionsManager), [session]);

  Hooks.useSubscriber(permissionManager, (event: PermissionsManagerEvent) => {
    switch (event.type) {
      case 'permission_changed':
        setState(s => ({
          ...s,
          ...getNewsFeedPermission(),
        }));
    }
  });

  Hooks.useSubscriber(watchlistManager, (event: WatchlistManagerEvent) => {
    switch (event.type) {
      case 'watchlist:updated_watchlists':
        update();
        break;
    }
  });

  React.useEffect(() => {
    // temporary hack;
    const updateWatchlists = async () => {
      await session.getManager(WatchlistManager).getWatchlists(true);
    };

    updateWatchlists();
  }, [session]);

  const proContext = React.useContext(ProContext);
  React.useEffect(() => {
    if (proContext.isPopout) {
      const filters = props.feedSettings;

      const watchlist = session.getManager(WatchlistManager).getStoredWatchlists();
      const sources = session.getManager(AdvancedNewsManager).getStoredSources() ?? [];

      const findCategory = (categories: Category[], id: string): string | undefined =>
        categories.find(cat => cat.tid.toString() === id || findCategory(cat.children, id))?.name;

      const findSources = (source: Source[], id: string): string | undefined =>
        source.find(cat => cat.id.toString() === id)?.name;
      const sourceNames = (filters?.sources
        .map(category => findSources(sources, category))
        .filter(name => isString(name)) ?? []) as string[];

      const findWatchlist = (watchlist: Watchlist[], id: string): string | undefined =>
        watchlist.find(cat => cat.watchlistId === id)?.name;
      const watchlistNames = (filters?.watchlists
        .map(category => findWatchlist(watchlist, category))
        .filter(name => isString(name)) ?? []) as string[];

      let title = [...sourceNames, ...watchlistNames].join(' + ');
      if (title.length > 0) {
        title = `${title} - `;
      }
      document.title = `${title}Benzinga Pro Newsfeed`;
    }
  }, [proContext.isPopout, props.feedSettings, session]);

  const setMillerColumnPath = (millerPath: any[] = []) => {
    setState(prevState => ({
      ...prevState,
      millerPath,
      showMillerColumns: !prevState.showMillerColumns,
    }));
  };

  const setFilterState = (field: PresetFilter | null) => {
    if (!field) return;
    if (field === state.field) {
      setState(og => ({ ...og, isFilterField: !og.isFilterField }));
    } else {
      setState(og => ({ ...og, field, isFilterField: true }));
    }
  };

  const updateFeedExpression = <T extends keyof NewsfeedFeedSettings>(
    filterProperty: T,
    filterValue: NewsfeedFeedSettings[T],
  ) => {
    session.getManager(TrackingManager).trackWidgetEvent('add_filter', 'newsfeed_widget', {
      filter_id: filterProperty,
      filter_value: filterValue.toString(),
    });
    const filters = props.feedSettings;
    if (filters) {
      filters[filterProperty] = filterValue;
      props.changeFeedSettings({ ...filters });
    } else {
      const filters = getBlankFilters({ [filterProperty]: filterValue });
      props.changeFeedSettings(filters);
    }
  };

  const toggleFilter = <
    T extends keyof Omit<
      NewsfeedFeedSettings,
      'screenerFilters' | 'importance' | 'createdAt' | 'relevantCategories' | 'denyRelevantCategories'
    >,
  >(
    filterProperty: T,
    denyFilterProperty: T,
    filterValue: NewsfeedFeedSettings[T][number],
    toggle: 'active' | 'deny' | 'unselected',
  ) => {
    session.getManager(TrackingManager).trackWidgetEvent('add_filter', 'newsfeed_widget', {
      filter_id: filterProperty,
      filter_value: filterValue.toString(),
    });

    const filters = props.feedSettings;
    if (filters) {
      const newProperty = [...filters[filterProperty]];
      if (newProperty.includes(filterValue as string & SourceId)) {
        newProperty.splice(
          newProperty.findIndex((item: NewsfeedFeedSettings[T][number]): boolean => item === filterValue),
          1,
        );
      }

      const newDenyProperty = [...filters[denyFilterProperty]];
      if (newDenyProperty.includes(filterValue as string & SourceId)) {
        newDenyProperty.splice(
          newDenyProperty.findIndex((item: NewsfeedFeedSettings[T][number]): boolean => item === filterValue),
          1,
        );
      }

      if (toggle === 'active') {
        newProperty.push(filterValue as string & SourceId);
      } else if (toggle === 'deny') {
        newDenyProperty.push(filterValue as string & SourceId);
      }
      filters[filterProperty] = newProperty;
      filters[denyFilterProperty] = newDenyProperty;
      props.changeFeedSettings({ ...filters });
    } else {
      const filters = getBlankFilters(
        toggle === 'active'
          ? { [filterProperty]: [filterValue] }
          : toggle === 'deny'
            ? { [denyFilterProperty]: [filterValue] }
            : {},
      );
      props.changeFeedSettings(filters);
    }
  };

  const toggleCategoryFilter = (
    sourceId: SourceId,
    categoryId: CategoryId,
    toggle: 'active' | 'deny' | 'unselected',
  ) => {
    session.getManager(TrackingManager).trackWidgetEvent('add_filter', 'newsfeed_widget', {
      filter_id: 'category_filter',
      filter_value: `${sourceId} - ${categoryId}`,
    });
    const filters = props.feedSettings;
    if (filters) {
      const newCategories = { ...filters.relevantCategories };
      if (!newCategories[sourceId]) {
        newCategories[sourceId] = [];
      }

      const denyNewCategories = { ...filters.denyRelevantCategories };
      if (!denyNewCategories[sourceId]) {
        denyNewCategories[sourceId] = [];
      }

      if (newCategories[sourceId]?.includes(categoryId)) {
        newCategories[sourceId]?.splice(newCategories[sourceId]?.findIndex(item => item === categoryId) ?? 0, 1);
      }

      if (denyNewCategories[sourceId]?.includes(categoryId)) {
        denyNewCategories[sourceId]?.splice(
          denyNewCategories[sourceId]?.findIndex(item => item === categoryId) ?? 0,
          1,
        );
      }

      if (toggle === 'active') {
        newCategories[sourceId]?.push(categoryId);
      } else if (toggle === 'deny') {
        denyNewCategories[sourceId]?.push(categoryId);
      }

      filters.relevantCategories = newCategories;
      filters.denyRelevantCategories = denyNewCategories;
      props.changeFeedSettings({ ...filters });
    } else {
      const filters = getBlankFilters(
        toggle === 'active'
          ? { relevantCategories: { [sourceId]: [categoryId] } }
          : toggle === 'deny'
            ? { denyRelevantCategories: { [sourceId]: [categoryId] } }
            : {},
      );
      props.changeFeedSettings(filters);
    }
  };

  const toggleMillerColumns = () => {
    setState(og => ({ ...og, showMillerColumns: !og.showMillerColumns }));
  };

  const handleScreenerFilters = (filters: FilterObject[]) => {
    updateFeedExpression('screenerFilters', filters as FilterObject[]);
  };

  const handleImportanceChange = React.useCallback(
    (val: number | Importance) => {
      const importance: Importance =
        typeof val === 'object'
          ? val
          : [
              offImportancePreset,
              lowImportancePreset,
              midImportancePreset,
              highImportancePreset,
              {
                ...customImportancePreset,
                // copy existing filter array to the preset, remove duplicates
                filters: [...customImportancePreset.filters, ...feedSettings.importance.filters].filter(
                  (filter, index, self) => index === self.findIndex(filter2 => filter2.filterId === filter.filterId),
                ),
              },
            ][val as number] ?? offImportancePreset;

      if (typeof val === 'object') {
        session.getManager(TrackingManager).trackWidgetEvent('add_filter', 'newsfeed_widget', {
          filter_id: 'importance_filter_custom',
          filter_value: JSON.stringify(val.filters.map(filter => ({ action: filter.action, id: filter.filterId }))),
        });
      } else {
        session.getManager(TrackingManager).trackWidgetEvent('add_filter', 'newsfeed_widget', {
          filter_id: 'importance_filter',
          filter_value: JSON.stringify(['off', 'low', 'medium', 'high', 'custom'][val]),
        });
      }
      const filters = { ...feedSettings, importance };
      changeFeedSettings(filters);
    },
    [feedSettings, changeFeedSettings, session],
  );

  const handleImportanceFilterChange = React.useCallback(
    (filterId: string, enable: boolean) => {
      const newImportance = { ...customImportancePreset, filters: [...feedSettings.importance.filters] };
      if (!enable) newImportance.filters = newImportance.filters.filter(filter => filter.filterId !== filterId);
      else {
        const filter = ImportanceFilters.find(filter => filter.id === filterId);
        if (filter) {
          newImportance.filters.push({ action: filter.defaultAction, filterId });
        }
      }
      handleImportanceChange(newImportance);
    },
    [feedSettings.importance, handleImportanceChange],
  );

  const getCategoriesSelected = React.useCallback(
    (categories: Category[], selectedCategories: string[]): number =>
      categories.reduce(
        (acc, category) =>
          acc +
          (selectedCategories.includes(category.id)
            ? 1
            : getCategoriesSelected(category.children, selectedCategories) > 0
              ? 1
              : 0),
        0,
      ),
    [],
  );

  const getCategoriesSelectedForSource = React.useCallback(
    (source: Source) => {
      if (!sourcesWithCategories.includes(source.id)) {
        return 0;
      }
      const categories = session.getManager(AdvancedNewsManager).getStoredCategories() ?? [];

      return props.feedSettings.sources.includes(source.id) ||
        props.feedSettings.sourceGroups?.includes(source.group ?? '')
        ? categories.length
        : getCategoriesSelected(categories, props.feedSettings.relevantCategories[source.id] ?? []);
    },
    [
      session,
      props.feedSettings.sources,
      props.feedSettings.sourceGroups,
      props.feedSettings.relevantCategories,
      getCategoriesSelected,
    ],
  );

  const getItemsSelectedForSource = React.useCallback(
    (source: Source): number => {
      if (sourcesWithCategories.includes(source.id)) {
        const selectedCategories = getCategoriesSelectedForSource(source);
        return selectedCategories;
      } else {
        return props.feedSettings.sources.includes(source.id) ||
          props.feedSettings.sourceGroups?.includes(source.group ?? '')
          ? 1
          : 0;
      }
    },
    [getCategoriesSelectedForSource, props.feedSettings.sourceGroups, props.feedSettings.sources],
  );

  const isSourceSelected = React.useCallback(
    (source: Source): boolean => {
      return getItemsSelectedForSource(source) > 0;
    },
    [getItemsSelectedForSource],
  );

  const getItemsSelectedForGroup = React.useCallback(
    (groupId: SourceGroup['id']): number => {
      return allSources.filter(source => source.group === groupId).filter(isSourceSelected).length;
    },
    [allSources, isSourceSelected],
  );

  const isGroupSelected = React.useCallback(
    (groupId: SourceGroup['id']): boolean => {
      return getItemsSelectedForGroup(groupId) > 0;
    },
    [getItemsSelectedForGroup],
  );

  const getTotalSourceItems = React.useCallback(() => {
    return (
      allSources.filter(source => source.group == null).filter(isSourceSelected).length +
      allGroups.filter(g => isGroupSelected(g.id)).length
    );
  }, [allSources, isSourceSelected, allGroups, isGroupSelected]);

  const renderMillerColumns = () => {
    const filters = props.feedSettings ?? ({} as NewsfeedFeedSettings);

    const {
      hasAccessToCategories,
      hasAccessToImportance,
      hasAccessToScreener,
      hasAccessToSource,
      hasAccessToWatchlist,
      millerPath,
    } = state;

    const renderScreenerFilters = () => {
      if (filters === undefined) {
        return null;
      }
      if (!hasAccessToScreener) {
        return null;
      }
      const screenerFiltersContent = (
        <RenderInfoDiv key="infoColumn">
          <StyledScannerFilterPanel
            isScreener={false}
            onFiltersChanged={handleScreenerFilters}
            permission={{ action: 'bzpro/newsfeed/filter/screener', resource: '#' }}
            selectedFilters={filters.screenerFilters}
            source={TargetSource.Stocks}
          />
        </RenderInfoDiv>
      );

      return screenerFiltersContent;
    };

    const renderImportanceFilter = () => {
      const marks = {
        0: 'off',
        1: 'low',
        2: 'mid',
        3: 'high',
        4: 'custom',
      };
      const values = ['off', 'low', 'mid', 'high', 'custom'];

      const description = filters.importance.description;

      return (
        <RenderInfoDiv key="infoColumn">
          <ImportanceDiv className="justify-center">
            <PermissionedComponent permission={{ action: 'bzpro/newsfeed/filter/importance', resource: '#' }}>
              <InfoWrapper>
                <ImportanceSliderDiv>
                  Select Level Of importance
                  <ImportanceSlider
                    defaultValue={0}
                    dots={true}
                    marks={marks}
                    max={4}
                    onChange={handleImportanceChange}
                    tooltipVisible={false}
                    value={values.findIndex(item => item === filters.importance.id)}
                  />
                  {description}
                </ImportanceSliderDiv>
                <ImportanceOptionsDiv>
                  Applied Filters:
                  <ImportanceOptionsTable>
                    {ImportanceFilters.flatMap(filter => {
                      if (filter.description == null) return [];
                      if (filters.importance.id !== 'off' && filters.importance.id !== 'custom') {
                        if (!filters.importance.filters.some(impFilter => impFilter.filterId === filter.id)) return [];
                      }
                      return [
                        <Fragment key={filter.id}>
                          <ImportanceOptionsElement centerAlign={true}>
                            <StyledCheckbox
                              checked={filters.importance.filters.some(impFilter => impFilter.filterId === filter.id)}
                              onChange={e => {
                                handleImportanceFilterChange(filter.id, e.target.checked);
                              }}
                            >
                              {filter.description}
                            </StyledCheckbox>
                          </ImportanceOptionsElement>
                        </Fragment>,
                      ];
                    })}
                    <FeedbackTextContainer>
                      <FeedbackFormCaller>
                        Results aren&apos;t what you&apos;d expect? Or have suggestions to improve the filters? Let us
                        know the feedback.&nbsp;
                        <FeedbackIcon />
                      </FeedbackFormCaller>
                    </FeedbackTextContainer>
                  </ImportanceOptionsTable>
                </ImportanceOptionsDiv>
              </InfoWrapper>
            </PermissionedComponent>
          </ImportanceDiv>
        </RenderInfoDiv>
      );
    };

    const categories = session.getManager(AdvancedNewsManager).getStoredCategories() ?? [];
    const sources = session.getManager(AdvancedNewsManager).getStoredSources() ?? [];
    const sourcesGroups = session.getManager(AdvancedNewsManager).getStoredSourcesGroups() ?? [];
    const watchlists = session.getManager(WatchlistManager).getStoredWatchlists();
    const editorialAccess =
      permissionManager.hasAccess('editorial/edit').ok ||
      permissionManager.hasAccess('newsdesk/login').ok ||
      permissionManager.hasAccess('#', '#').ok;

    const sourcesMap = new Map<SourceGroup['id'], Source[]>();
    const sourcesGroupMap = new Map<SourceGroup['id'], SourceGroup>();

    sourcesMap.set('', []);
    sourcesGroups.forEach(s => {
      sourcesMap.set(s.id, []);
      sourcesGroupMap.set(s.id, s);
    });

    sources.forEach(s => {
      const group = sourcesMap.get(s.group);
      if (group) {
        group.push(s);
      } else {
        sourcesMap.get('')?.push(s);
      }
    });

    const sourceGroupChildren = (children: Source[] | undefined) => {
      const updatedSourceChildren = children?.filter(source => source.id !== 'syndicated_links' || editorialAccess);
      return (
        updatedSourceChildren
          ?.map(source => {
            return [
              {
                active:
                  filters.sources.includes(source.id) && (filters.relevantCategories[source.id]?.length ?? 0) === 0
                    ? 'active'
                    : filters.denySources.includes(source.id)
                      ? 'deny'
                      : 'unselected',
                children: sourcesWithCategories.includes(source.id) ? categoriesNode(categories, source.id) : undefined,
                description: source.description,
                hue: newsfeedGlobalSetting.themes.find(
                  theme => theme.target === source.id && theme.category === 'source',
                )?.hue,
                icon: hasAccessToSource ? <Globe /> : <Locked />,
                id: source.id,
                name: source.fullName,
                onToggle: (toggle: 'active' | 'deny' | 'unselected') =>
                  toggleFilter('sources', 'denySources', source.id, toggle),
                renderInfo: renderInfo(
                  true,
                  'source',
                  newsfeedGlobalSetting.changeThemeColor,
                  newsfeedGlobalSetting.removeTheme,
                ),
              } satisfies MillerColumnsNode,
              source.weight ?? 0,
            ] as const;
          })
          .sort((a, b) => a[1] - b[1]) ?? []
      );
    };

    const SourcesNodesG = Array.from(sourcesMap.keys())
      .filter(a => a !== '' && sourcesMap.get(a)?.length)
      .map(groupId => {
        return [
          {
            active: filters.sourceGroups.includes(groupId)
              ? 'active'
              : filters.denySourceGroups.includes(groupId)
                ? 'deny'
                : 'unselected',
            children: sourceGroupChildren(sourcesMap.get(groupId)).map(i => i[0]),
            description: sourcesGroupMap.get(groupId)?.name,
            hue: newsfeedGlobalSetting.themes.find(
              theme => theme.target === groupId && theme.category === 'sourceGroups',
            )?.hue,
            icon: hasAccessToSource ? <Globe /> : <Locked />,
            id: groupId,
            name: sourcesGroupMap.get(groupId)?.name,
            onToggle: (toggle: 'active' | 'deny' | 'unselected') =>
              toggleFilter('sourceGroups', 'denySourceGroups', groupId, toggle),
            renderInfo: renderInfo(
              true,
              'sourceGroups',
              newsfeedGlobalSetting.changeThemeColor,
              newsfeedGlobalSetting.removeTheme,
            ),
          } satisfies MillerColumnsNode,
          sourcesGroupMap.get(groupId)?.weight ?? 0,
        ] as const;
      });

    function categoriesNode(categories: Category[], sourceId: SourceId): MillerColumnsNode[] {
      return categories
        .sort((a, b) => sortByString(a.name, b.name))
        .map(category => ({
          active: isActiveCategoryFilter(filters, sourceId, category.tid.toString())
            ? 'active'
            : isDenyCategoryFilter(filters, sourceId, category.tid.toString())
              ? 'deny'
              : 'unselected',
          children: categoriesNode(category.children, sourceId),
          description: category.description,
          hue: newsfeedGlobalSetting.themes.find(
            theme => theme.target === category.tid.toString() && theme.category === 'category',
          )?.hue,
          icon: hasAccessToCategories ? <Categories /> : <Locked />,
          id: category.tid.toString(),
          name: category.name,
          onToggle: (toggle: 'active' | 'deny' | 'unselected') =>
            toggleCategoryFilter(sourceId, category.tid.toString(), toggle),

          permission: { action: 'bzpro/newsfeed/filter/categories', resource: '#' },
          renderInfo: renderInfo(
            true,
            'category',
            newsfeedGlobalSetting.changeThemeColor,
            newsfeedGlobalSetting.removeTheme,
          ),
        }));
    }

    const SourcesNodes = [...SourcesNodesG, ...sourceGroupChildren(sourcesMap.get(''))]
      .sort((a, b) => a[1] - b[1])
      .map(i => i[0]);

    const sourcesNamespaceNode: MillerColumnsNode = {
      active: 'none',
      children: hasAccessToSource
        ? SourcesNodes.sort((a, b) => sortByString(a.name ?? a.id, b.name ?? b.id))
        : undefined,
      description: 'Main Data Sources',
      displayIconInTitle: !hasAccessToSource,
      icon: hasAccessToSource ? <Globe /> : <Locked />,
      id: 'sources',
      name: 'Sources',
      permission: { action: 'bzpro/newsfeed/filter/sources', resource: '#' },
      renderInfo: renderInfo(false),
    };

    const screenerFiltersNamespaceNode: MillerColumnsNode = {
      active: 'none',
      description: 'Ability to screen articles on market metrics',
      displayIconInTitle: !hasAccessToScreener,
      icon: hasAccessToImportance ? <Scanner /> : <Locked />,
      id: 'screenerFilters',
      name: 'Scanner',
      permission: { action: 'bzpro/newsfeed/filter/screener', resource: '#' },
      renderInfo: hasAccessToImportance ? renderScreenerFilters : renderInfo(false),
    };

    const watchlistsNamespaceNode: MillerColumnsNode = {
      active: 'none',
      children: hasAccessToWatchlist
        ? watchlists
            .sort((a, b) => sortByString(a.name, b.name))
            .map<MillerColumnsNode>(watchlist => ({
              active: isActiveFilter(filters, 'watchlists', watchlist.watchlistId)
                ? 'active'
                : isActiveFilter(filters, 'denyWatchlists', watchlist.watchlistId)
                  ? 'deny'
                  : 'unselected',
              children: watchlist.symbols
                .sort((a, b) => sortByString(a.symbol, b.symbol))
                .map(symbol => ({
                  active: 'none',
                  description: 'Market Symbol',
                  id: symbol.symbol,
                  name: symbol.symbol,
                })),
              description: `${watchlist.symbols.length} symbols`,
              hue: newsfeedGlobalSetting.themes.find(
                theme => theme.target === watchlist.watchlistId && theme.category === 'watchlist',
              )?.hue,
              id: watchlist.watchlistId,
              name: watchlist.name,
              onToggle: (toggle: 'active' | 'deny' | 'unselected') =>
                toggleFilter('watchlists', 'denyWatchlists', watchlist.watchlistId, toggle),
              renderInfo: renderInfo(
                true,
                'watchlist',
                newsfeedGlobalSetting.changeThemeColor,
                newsfeedGlobalSetting.removeTheme,
              ),
            }))
        : undefined,
      description: 'Benzinga Pro Watchlists',
      displayIconInTitle: !hasAccessToWatchlist,
      icon: hasAccessToWatchlist ? <WatchlistIcon /> : <Locked />,
      id: 'watchlists',
      name: 'Watchlists',
      permission: [
        { action: 'bzpro/newsfeed/filter/watchlists', resource: '#' },
        { action: 'bzpro/widget/use', resource: 'watchlist' },
      ],
      renderInfo: renderInfo(false),
    };

    const importanceFiltersNamespaceNode: MillerColumnsNode = {
      active: 'none',
      badge: props.feedSettings?.importance.id !== 'off' ? 'on' : null,
      description: 'Article Importance',
      displayIconInTitle: false,
      icon: <Sectors />,
      id: 'Importance',
      name: 'Importance',
      permission: [],
      renderInfo: renderImportanceFilter,
    };

    const items = [
      sourcesNamespaceNode,
      importanceFiltersNamespaceNode,
      screenerFiltersNamespaceNode,
      watchlistsNamespaceNode,
    ];

    return (
      <Modal
        onClose={toggleMillerColumns}
        size={{ manual: { height: '90%', width: '90%' } }}
        title="Add or Remove Filters"
      >
        <NewsfeedModalContent>
          <MillerColumns closeModal={toggleMillerColumns} initialPath={millerPath} items={items} />
          <NewsfeedModalContentFooter>
            <button className="Button Button--text" onClick={resetFilters}>
              Reset
            </button>
            {session.getManager(LoggingManager).getVerbosity() === 'debug' && (
              <div>
                filters:{' '}
                {JSON.stringify(
                  Object.keys(filters)
                    .filter(key => !hasLength(filters[key]))
                    .reduce((res, key) => ((res[key] = filters[key]), res), {}),
                  null,
                  ' ',
                )}
              </div>
            )}
            <button className="Button Button--primary Button--done" onClick={toggleMillerColumns}>
              Done
            </button>
          </NewsfeedModalContentFooter>
        </NewsfeedModalContent>
      </Modal>
    );
  };

  const renderSingleComponent = () => {
    return (
      <FilterField
        field={state.field}
        filters={props.feedSettings?.screenerFilters ?? []}
        handleScreenerFilters={handleScreenerFilters}
        loadField={state.isFilterField}
      />
    );
  };

  const closeDesktopNotificationsModal = () => {
    setState(og => ({ ...og, showDesktopNotificationErrorModal: false }));
  };

  const resetFilters = React.useCallback(() => {
    const changeFeedSettings = props.changeFeedSettings;
    changeFeedSettings(getBlankFilters({ sources: ['story'] }));
  }, [props.changeFeedSettings]);

  const enableDesktopNotifications = React.useCallback(() => {
    newsfeedSetting.changeNotificationsEnabled?.(true);
  }, [newsfeedSetting]);

  const disableDesktopNotifications = React.useCallback(() => {
    newsfeedSetting.changeNotificationsEnabled?.(false);
  }, [newsfeedSetting]);

  const setDisplayType = React.useCallback(
    (displayType: DisplayType) => () => {
      newsfeedSetting.changeDisplayType?.(displayType);
    },
    [newsfeedSetting],
  );

  const createBadgeContentFrom = (filterItems: unknown[]): number | null => filterItems.length || null;

  const { displayType, notificationsEnabled } = newsfeedSetting;
  const filters = props.feedSettings;
  const sourceCount = React.useMemo(() => getTotalSourceItems(), [getTotalSourceItems]);

  if (filters === undefined) {
    return null;
  }

  const { importance, screenerFilters, watchlists } = filters;
  const allWatchlists = session.getManager(WatchlistManager).getStoredWatchlists();

  const filterWatchlist = allWatchlists.filter(watchlist => {
    return watchlists.indexOf(watchlist?.watchlistId) > -1;
  });

  const sourcesBadge = <Badge content={sourceCount !== 0 ? sourceCount : ''} />;
  const importanceBadge = <Badge content={importance.id !== 'off' ? 'on' : ''} />;
  const screenerFiltersBadge = <Badge content={createBadgeContentFrom(screenerFilters)} />;
  const watchlistsBadge = <Badge content={createBadgeContentFrom(filterWatchlist)} />;
  const priceFilterBadge = <Badge />;
  const marketCapFilterBadge = <Badge />;
  const volumeFilterBadge = <Badge />;

  let desktopNotificationsButton: JSX.Element | null = null;
  let alertTooltip;
  if (notificationsEnabled) {
    alertTooltip = 'Notifications Enabled';
    desktopNotificationsButton = (
      <Tooltip title={alertTooltip}>
        <div
          className="WidgetToolbar-btn WidgetToolbar-btn--text notification--active"
          onClick={disableDesktopNotifications}
        >
          <AlertActive title={alertTooltip} /> Notifications
        </div>
      </Tooltip>
    );
  } else {
    alertTooltip = 'Notifications Paused';
    desktopNotificationsButton = (
      <Tooltip placement="bottom" title={alertTooltip}>
        <div
          className="WidgetToolbar-btn WidgetToolbar-btn--text notification--inactive"
          onClick={enableDesktopNotifications}
        >
          <AlertOff title={alertTooltip} /> Notifications
        </div>
      </Tooltip>
    );
  }

  let desktopNotificationsModal: JSX.Element | null = null;
  if (state.showDesktopNotificationErrorModal) {
    desktopNotificationsModal = (
      <Modal
        isError
        onClose={closeDesktopNotificationsModal}
        size={{ alert: true }}
        title="Desktop Notifications Disabled"
      >
        <div className="Modal-content">
          <p>
            You have blocked notifications for Benzinga Pro in your browser, or are using an unsupported browser. Enable
            notifications to use this feature. Contact support if you have any trouble{' '}
            {benzingaContext.contactPhoneNumber}.
          </p>

          <div className="Modal-proceed">
            <button className="Button Button--primary" onClick={closeDesktopNotificationsModal}>
              Close
            </button>
          </div>
        </div>
      </Modal>
    );
  }

  const createToolbarButton = ({
    badge,
    className,
    field,
    icon,
    isFilterFieldComponent,
    label,
    path,
    permission,
    tooltip,
  }: ToolbarButtonParams) => {
    const activeClass = field === state.field && state.isFilterField ? 'active' : '';
    const activeField = filters.screenerFilters.find(x => x.field === field) ? 'active-field' : '';
    return (
      <PermissionedComponent permissionsAnd={Array.isArray(permission) ? permission : [permission]}>
        {access => (
          <Tooltip placement="bottom" title={tooltip}>
            <div>
              <div
                className={`WidgetToolbar-btn WidgetToolbar-btn--text ${className || ''} ${activeClass} ${activeField}`}
                onClick={() => {
                  if (access) {
                    isFilterFieldComponent ? setFilterState(field) : setMillerColumnPath(path);
                  }
                }}
              >
                {access ? icon : <Locked />}
                {label}
                {badge}
              </div>
              {field === state.field && state.isFilterField && renderSingleComponent()}
            </div>
          </Tooltip>
        )}
      </PermissionedComponent>
    );
  };

  const createDateToolbarButton = ({
    badge,
    icon,
    isFilterActive,
    label,
    onClick,
    permission,
    tooltip,
  }: DateToolbarButtonParams) => {
    const activeField = isFilterActive && isFilterActive() ? 'active-field' : '';
    return (
      <PermissionedComponent permissionsAnd={Array.isArray(permission) ? permission : [permission]}>
        {access => (
          <Tooltip placement="bottom" title={tooltip}>
            <div className={`WidgetToolbar-btn WidgetToolbar-btn--text ${activeField}`} onClick={onClick}>
              {access ? icon : <Locked />}
              {label}
              {badge}
            </div>
          </Tooltip>
        )}
      </PermissionedComponent>
    );
  };

  return (
    <>
      <div className="WidgetToolbar WidgetToolbar-hasToolbar TUTORIAL_Search-input_Newsfeed">
        <div className="WidgetToolbar-toolbar" ref={wrapperRef}>
          {createToolbarButton({
            badge: sourcesBadge,
            className: 'tooltip--left TUTORIAL_NewsfeedToolbar-Sources',
            field: null,
            icon: <Globe title="Source Filters" />,
            isFilterFieldComponent: false,
            label: 'Sources',
            path: ['sources'],
            permission: { action: 'bzpro/newsfeed/filter/sources', resource: '#' },
            tooltip: 'Add/Remove Content Sources',
          })}

          {createToolbarButton({
            badge: importanceBadge,
            className: 'tooltip--left TUTORIAL_NewsfeedToolbar-Sources',
            field: null,
            icon: <Sectors title="Importance Filters" />,
            isFilterFieldComponent: false,
            label: 'Importance',
            path: ['Importance'],
            permission: [],
            tooltip: 'Select Level Of Importance',
          })}

          {createToolbarButton({
            badge: screenerFiltersBadge,
            className: 'TUTORIAL_NewsfeedToolbar-Sectors',
            field: null,
            icon: <Scanner title="Scanner Filters" />,
            isFilterFieldComponent: false,
            label: 'scanner',
            path: ['screenerFilters'],
            permission: { action: 'bzpro/newsfeed/filter/screener', resource: '#' },
            tooltip: 'Limit by Scanner',
          })}

          {createToolbarButton({
            badge: watchlistsBadge,
            className: 'TUTORIAL_NewsfeedToolbar-Watchlists',
            field: null,
            icon: <WatchlistIcon title="Watchlists Filters" />,
            isFilterFieldComponent: false,
            label: 'Watchlists',
            path: ['watchlists'],
            permission: [
              { action: 'bzpro/newsfeed/filter/watchlists', resource: '#' },
              { action: 'bzpro/widget/use', resource: 'watchlist' },
            ],
            tooltip: 'Limit by Watchlist',
          })}

          {newsfeedSetting.changeNotificationsEnabled && desktopNotificationsButton}

          {createToolbarButton({
            badge: priceFilterBadge,
            className: 'TUTORIAL_NewsfeedToolbar-Price',
            field: PresetFilter.price,
            icon: <WatchlistIcon title="Price Filters" />,
            isFilterFieldComponent: true,
            label: 'Price',
            path: ['priceFilter'],
            permission: { action: 'bzpro/newsfeed/filter/price', resource: '#' },
            tooltip: 'Limit by Price',
          })}

          {createToolbarButton({
            badge: marketCapFilterBadge,
            className: 'TUTORIAL_NewsfeedToolbar-Market-Cap',
            field: PresetFilter.mktCap,
            icon: <WatchlistIcon title="Market Cap Filters" />,
            isFilterFieldComponent: true,
            label: 'Market Cap',
            path: ['marketCap'],
            permission: { action: 'bzpro/newsfeed/filter/marketcap', resource: '#' },
            tooltip: 'Limit by Market Cap',
          })}

          {createToolbarButton({
            badge: volumeFilterBadge,
            className: 'TUTORIAL_NewsfeedToolbar-Volume',
            field: PresetFilter.dVolume,
            icon: <WatchlistIcon title="Volume Filters" />,
            isFilterFieldComponent: true,
            label: 'Volume',
            path: ['volume'],
            permission: { action: 'bzpro/newsfeed/filter/volume', resource: '#' },
            tooltip: 'Limit by Volume',
          })}
          <DatePicker
            button={createDateToolbarButton({
              badge: volumeFilterBadge,
              icon: <Calendar title="Date" />,
              isFilterActive: () => !!newsfeedSetting.jumpedToDate,
              label: 'Date',
              onClick: () => {
                setState(s => ({ ...s, isDateModalOpen: true }));
              },
              permission: { action: 'bzpro/newsfeed/filter/date', resource: '#' },
              tooltip: 'Jump To Specified Date',
            })}
            date={newsfeedSetting.jumpedToDate}
            onChangeDate={newsfeedSetting.setJumpedToDate ?? noop}
            onOpenChange={(isDateModalOpen: boolean) => {
              setState(s => ({ ...s, isDateModalOpen }));
            }}
            open={state.isDateModalOpen}
            timezone={time.timezone}
          />
          <Spacer />
          {newsfeedSetting.changeDisplayType && <ArticleViewMode activeType={displayType} onChange={setDisplayType} />}
        </div>
      </div>
      {desktopNotificationsModal}

      {state.showMillerColumns && renderMillerColumns()}
    </>
  );
};

const Locked = styled(Lock)`
  fill: ${props => props.theme.colors.accent} !important;
`;

const Spacer = styled.div`
  flex: 1;
`;

const RenderInfoWrapperDiv = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const InfoWrapper = styled(RenderInfoWrapperDiv)`
  width: 100%;
  height: 100%;
`;

const FeedbackTextContainer = styled.div`
  display: flex;
  align-items: center;
  height: 100%;
  font-size: 16px;
  margin-top: 30px;
`;

const ImportanceSliderDiv = styled.div`
  margin-top: 20px;
  width: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
`;
const ImportanceDiv = styled.div`
  overflow-x: hidden;
  overflow-y: auto;
  min-width: 300px;
`;

const ImportanceOptionsDiv = styled.div`
  display: flex;
  flex-direction: column;
  align-items: baseline;
  padding-top: 40px;
  padding-bottom: 40px;
  min-width: 200px;
  max-width: 400px;
  height: 50%;
`;

const ImportanceOptionsTable = styled.div`
  display: grid;
  width: 100%;
  align-items: center;
  grid-template-columns: auto;
`;

const ImportanceOptionsElement = styled.span<{ centerAlign?: boolean }>`
  padding-top: 10px;
  padding-right: 7px;
  padding-bottom: 5px;
  align-self: 'flex-start';
  justify-self: 'center';
`;

const ImportanceSlider = styled(Slider)`
  width: 85%;
  min-width: 150px;
`;

const RenderInfoDiv = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-width: 250px;
  border-bottom: $border;
`;

const NewsfeedModalContent = styled(ModalContent)`
  max-height: inherit;
  max-width: inherit;
  max-width: inherit;
  overflow: hidden;
`;

const NewsfeedModalContentFooter = styled.div`
  display: flex;
  flex-shrink: 0;
  justify-content: flex-end;
  margin-top: 1em;
`;

const FeedbackIcon = styled(Feedback)`
  font-size: 30px;
  min-width: 30px;
  margin-left: 15px;
`;

const StyledCheckbox = styled(Checkbox)`
  &.ant-checkbox-wrapper {
    color: ${props => props.theme.colors.foregroundMuted} !important;
    font-size: 14px !important;
    line-height: unset !important;
  }
`;

const StyledScannerFilterPanel = styled(ScannerFilterPanel)`
  height: 100%;
  padding: 4px;
  min-width: fit-content;
  width: 100%;
  overflow: hidden;
`;

const TopBar = styled.div`
  display: flex;

  border-top: 1px solid ${props => props.theme.colors.border};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;
