'use client';
import React, { FunctionComponent, RefObject, useCallback } from 'react';
import styled from '@benzinga/themetron';

import NewsfeedScreenerFilters, { PresetFilter } from './screenerFilters';
import { FilterObject } from '@benzinga/quotes-v3-fields-manager';

interface Props {
  field: PresetFilter | null;
  filters: FilterObject[];
  loadField: boolean;
  handleScreenerFilters?(filters: FilterObject[]): void;
}

// TODO: Move the following data to some entities/uil
const priceOptions = [
  { id: 'any', name: 'Any', value: ['', ''] },
  { id: 'under1', name: 'Under $1', value: ['0', '1'] },
  { id: 'under2', name: 'Under $2', value: ['0', '2'] },
  { id: 'under3', name: 'Under $3', value: ['0', '3'] },
  { id: 'under4', name: 'Under $4', value: ['0', '4'] },
  { id: 'under5', name: 'Under $5', value: ['0', '5'] },
  { id: 'under7', name: 'Under $7', value: ['0', '7'] },
  { id: 'under10', name: 'Under $10', value: ['0', '10'] },
  { id: 'under15', name: 'Under $15', value: ['0', '15'] },
  { id: 'under20', name: 'Under $20', value: ['0', '20'] },
  { id: 'under30', name: 'Under $30', value: ['0', '30'] },
  { id: 'under40', name: 'Under $40', value: ['0', '40'] },
  { id: 'under50', name: 'Under $50', value: ['0', '50'] },

  { id: 'over1', name: 'Over $1', value: ['1', ''] },
  { id: 'over2', name: 'Over $2', value: ['2', ''] },
  { id: 'over3', name: 'Over $3', value: ['3', ''] },
  { id: 'over4', name: 'Over $4', value: ['4', ''] },
  { id: 'over5', name: 'Over $5', value: ['5', ''] },
  { id: 'over7', name: 'Over $7', value: ['7', ''] },
  { id: 'over10', name: 'Over $10', value: ['10', ''] },
  { id: 'over20', name: 'Over $20', value: ['20', ''] },
  { id: 'over30', name: 'Over $30', value: ['30', ''] },
  { id: 'over40', name: 'Over $40', value: ['40', ''] },
  { id: 'over50', name: 'Over $50', value: ['50', ''] },
  { id: 'over60', name: 'Over $60', value: ['60', ''] },
  { id: 'over70', name: 'Over $70', value: ['70', ''] },
  { id: 'over80', name: 'Over $80', value: ['80', ''] },
  { id: 'over90', name: 'Over $90', value: ['90', ''] },
  { id: 'over100', name: 'Over $100', value: ['100', ''] },

  { id: '1to5', name: '$1 to $5', value: ['1', '5'] },
  { id: '1to10', name: '$1 to $10', value: ['1', '10'] },
  { id: '1to20', name: '$1 to $20', value: ['1', '20'] },

  { id: '5to10', name: '$5 to $10', value: ['5', '10'] },
  { id: '5to20', name: '$5 to $20', value: ['5', '20'] },
  { id: '5to50', name: '$5 to $50', value: ['5', '50'] },

  { id: '10to20', name: '$10 to $20', value: ['10', '20'] },
  { id: '10to50', name: '$10 to $50', value: ['10', '50'] },

  { id: '20to50', name: '$20 to $50', value: ['20', '50'] },

  { id: '50to100', name: '$50 to $100', value: ['50', '100'] },
];

const marketCapOptions = [
  { id: 'any', name: 'Any', value: ['', ''] },
  { id: 'mega', name: 'Mega ($200B and more)', value: ['200000000000', ''] },

  { id: 'large', name: 'Large ($10B to $200B)', value: ['10000000000', '200000000000'] },
  { id: 'mid', name: 'Mid ($2B to $10B)', value: ['2000000000', '10000000000'] },
  { id: 'small', name: 'Small ($300M to $2B)', value: ['300000000', '2000000000'] },
  { id: 'micro', name: 'Micro ($50M to $300M)', value: ['50000000', '300000000'] },

  { id: 'nano', name: 'Nano (under $50M)', value: ['0', '50000000'] },

  { id: 'large+', name: 'Large (Over $10B)', value: ['10000000000', ''] },
  { id: 'mid+', name: 'Mid (Over $2B)', value: ['2000000000', ''] },
  { id: 'small+', name: 'Small (Over $300M)', value: ['300000000', ''] },
  { id: 'micro+', name: 'Micro (Over $50M)', value: ['50000000', ''] },

  { id: 'large-', name: 'Large (Under $200B)', value: ['0', '200000000000'] },
  { id: 'mid-', name: 'Mid (Under $10B)', value: ['0', '10000000000'] },
  { id: 'small-', name: 'Small (Under $2B)', value: ['0', '2000000000'] },
  { id: 'micro-', name: 'Micro (Under $300M)', value: ['0', '300000000'] },
];

const volumeOptions = [
  { id: 'any', name: 'Any', value: ['', ''] },
  { id: 'under50', name: 'Under 50K', value: ['0', '50000'] },
  { id: 'under100', name: 'Under 100K', value: ['0', '100000'] },
  { id: 'under500', name: 'Under 500K', value: ['0', '500000'] },
  { id: 'under750', name: 'Under 750K', value: ['0', '750000'] },
  { id: 'under1m', name: 'Under 1M', value: ['0', '1000000'] },

  { id: 'over0', name: 'Over 0', value: ['0', ''] },
  { id: 'over50', name: 'Over 50K', value: ['50000', ''] },
  { id: 'over100', name: 'Over 100K', value: ['100000', ''] },
  { id: 'over200', name: 'Over 200K', value: ['200000', ''] },
  { id: 'over300', name: 'Over 300K', value: ['300000', ''] },
  { id: 'over400', name: 'Over 400K', value: ['400000', ''] },
  { id: 'over500', name: 'Over 500K', value: ['500000', ''] },
  { id: 'over750', name: 'Over 750K', value: ['750000', ''] },

  { id: 'over1m', name: 'Over 1M', value: ['1000000', ''] },
  { id: 'over2m', name: 'Over 2M', value: ['2000000', ''] },
  { id: 'over5m', name: 'Over 5M', value: ['5000000', ''] },
  { id: 'over10m', name: 'Over 10M', value: ['10000000', ''] },
  { id: 'over20m', name: 'Over 20M', value: ['20000000', ''] },
];

export const FilterField: FunctionComponent<Props> = ({ field, filters, handleScreenerFilters, loadField }) => {
  const filterBox = React.useRef<HTMLDivElement>(null);

  const handleScannerFiltersChange = useCallback(
    (filters: FilterObject[]) => {
      if (handleScreenerFilters) {
        handleScreenerFilters(filters);
      } else {
        return;
      }
    },
    [handleScreenerFilters],
  );

  if (!loadField) {
    return null;
  }

  return (
    <CustomisedFieldContainer ref={filterBox}>
      <FilterFieldContainer>
        <NewsfeedScreenerFilters
          field={field}
          filters={filters}
          marketCapOptions={marketCapOptions}
          onFiltersChange={handleScannerFiltersChange}
          priceOptions={priceOptions}
          volumeOptions={volumeOptions}
        />
      </FilterFieldContainer>
    </CustomisedFieldContainer>
  );
};

const FilterFieldContainer = styled.div`
  padding: 0px 2px 5px 2px;
`;

const CustomisedFieldContainer = styled.div`
  background-color: ${props => props.theme.colors.background};
  box-shadow: 0 4px 8px 0 rgb(0 0 0 / 20%);
  position: absolute;
  top: 103%;
  transition: 0.3s;
  z-index: 2;
`;

export default FilterField;
