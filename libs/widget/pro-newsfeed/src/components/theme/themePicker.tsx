'use client';
import React, { RefObject } from 'react';
import styled, { hueToColorAlpha } from '@benzinga/themetron';

import { debounce } from 'lodash';
import { Portal } from 'react-portal';
import { NewsfeedThemeSlider } from './themeSlider';
import { StoryColorTheme } from '../../entity';

type changeThemeColor = (hue: number) => void;

interface Props {
  changeThemeColor: changeThemeColor;
  theme: StoryColorTheme;
  themeClass: string;
}

const NewsfeedThemePicker: React.FC<Props> = props => {
  const PICKER = React.useRef<HTMLDivElement>(null);
  const debouncedChangeThemeColor: changeThemeColor = React.useMemo(
    () => debounce(props.changeThemeColor, 50),
    [props.changeThemeColor],
  );

  const [state, setState] = React.useState({
    // The hue is set here as internal (throw away, if you want to think of it that way)
    // ui state because if you set the slider value on the debounced prop.changeThemeColor alone
    // then as the user tries to move the slider, the actual button will not move fluidly
    hue: props.theme.hue,
    showPicker: false,
  });

  const onColorChange = (value: number) => {
    setState(oldState => ({ ...oldState, hue: value }));
    debouncedChangeThemeColor(value);
  };

  const getPickerStyle = () => {
    if (PICKER.current) {
      const rect = PICKER.current.getBoundingClientRect();
      return {
        left: rect.left - 200 + rect.width + rect.width / 2,
        top: rect.top + rect.height + 10,
      };
    }

    return undefined;
  };

  const showPicker = React.useCallback(() => {
    setState(oldState => ({ ...oldState, showPicker: true }));
  }, []);

  const hidePicker = React.useCallback(() => {
    setState(oldState => ({ ...oldState, showPicker: false }));
  }, []);

  return (
    <div style={{ position: 'relative' }}>
      <div
        className="NewsfeedTheme-color"
        onClick={showPicker}
        ref={PICKER}
        style={{ background: hueToColorAlpha(props.theme.hue) }}
      >
        Aa
      </div>
      {state.showPicker && <div className="NewsfeedTheme-color-placeholder" />}
      {state.showPicker && (
        <Portal>
          <div className={props.themeClass}>
            <StyledNewsfeedThemeSlider
              hidePicker={hidePicker}
              onChange={onColorChange}
              style={getPickerStyle()}
              value={state.hue}
            />
          </div>
        </Portal>
      )}
    </div>
  );
};

const StyledNewsfeedThemeSlider = styled(NewsfeedThemeSlider)`
  z-index: 200;
`;

export default NewsfeedThemePicker;
