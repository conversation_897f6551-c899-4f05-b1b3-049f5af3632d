import {
  Children,
  cloneElement,
  Component,
  createRef,
  CSSProperties,
  MouseEvent as ReactMouseEvent,
  ReactElement,
  ReactNode,
  RefObject,
} from 'react';

import fastdom from 'fastdom';
import { DebouncedFunc, throttle } from 'lodash';
import { includes, toLower } from 'ramda';

import { isSafari, MouseEvents, SplitType } from '../entities/chatEntity';
import { PaneSize } from '../entities/workspaceEntity';

interface Props {
  children: ReactNode;
  defaultSize?: PaneSize;
  minSize: number;
  nodeRef?: RefObject<HTMLDivElement>;
  splitType?: SplitType;
  style?: CSSProperties;
}

interface State {
  readonly style: CSSProperties;
}

// This is used to prevent multiple instances of the SplitPane from adding multiple event listeners, instead we register a callback and fire it on the single event listener.
const { addEventSubcriber } = (() => {
  let onMouseMoveSubscriber: ((event: MouseEvent) => void) | null = null;
  let onMouseUpSubscriber: ((event: MouseEvent) => void) | null = null;

  document.addEventListener(MouseEvents.mouseMove, event => {
    if (onMouseMoveSubscriber) {
      onMouseMoveSubscriber(event);
    }
  });

  document.addEventListener(MouseEvents.mouseUp, event => {
    if (onMouseUpSubscriber) {
      onMouseUpSubscriber(event);
      document.removeEventListener(MouseEvents.mouseUp, onMouseUpSubscriber);
      onMouseUpSubscriber = null;
    }
    if (onMouseMoveSubscriber) {
      document.removeEventListener(MouseEvents.mouseMove, onMouseMoveSubscriber);
      onMouseMoveSubscriber = null;
    }
  });

  return {
    addEventSubcriber(onMouseMove: DebouncedFunc<(event: MouseEvent) => void>, onMouseUp: EventListener) {
      onMouseMoveSubscriber = onMouseMove;
      onMouseUpSubscriber = onMouseUp;
    },
  };
})();

export default class ChatSplitPane extends Component<Props, State> {
  static defaultProps = {
    splitType: SplitType.horizontal,
  };

  hasSizeChanged: boolean;
  onMouseMove: DebouncedFunc<(event: MouseEvent) => void>;
  position = -1;
  PANE1: RefObject<HTMLDivElement | null> = createRef();
  PANE2: RefObject<HTMLDivElement | null> = createRef();

  constructor(props: Props) {
    super(props);
    const { defaultSize } = this.props;
    this.hasSizeChanged = false;

    const style = defaultSize || {};
    this.state = {
      style,
    };
    this.onMouseMove = throttle(this._onMouseMove, 16);
  }

  onMouseDown = (event: ReactMouseEvent<HTMLDivElement>): void => {
    event.preventDefault();
    addEventSubcriber(this.onMouseMove, this.onMouseUp);
    this.position = event.clientY;
  };

  _onMouseMove = (event: MouseEvent): void => {
    if (!this.PANE1.current || !this.PANE2.current) {
      return;
    }
    const { minSize } = this.props;
    const node = this.PANE2.current;

    const newPosition: number = event.clientY;
    const size: number = node.offsetHeight;

    const movedBy = newPosition - this.position;
    let newSize = size - movedBy;

    if (newSize < minSize) {
      newSize = minSize;
    } else if (newSize > size) {
      const node = this.PANE1.current;
      const paneSize = node.offsetHeight;
      const newPaneSize = paneSize - movedBy;

      if (newPaneSize < minSize) {
        newSize += minSize - newPaneSize;
      }
    }

    this.position = newPosition;
    this.hasSizeChanged = true;
    fastdom.mutate(() => {
      this.setState({ style: { flexBasis: `${newSize}px`, flexGrow: 0 } });
    });
  };

  onMouseUp = (): void => {
    if (this.hasSizeChanged) {
      this.hasSizeChanged = false;
    }
  };

  // TODO: any because the child can be any ReactElement with variable props
  isStyledComponent = (child: ReactElement<any, any>) => includes('styled', toLower(child.type.displayName as string));

  render() {
    const { children, nodeRef, style } = this.props;

    // TODO any because children can be any ReactElement with variable props
    const [firstChild, secondChild] = Children.toArray(children) as ReactElement<any>[];

    if (!secondChild) {
      return (
        <div className={'SplitPane Workspace-column grid-view'} ref={nodeRef}>
          {cloneElement(firstChild, {
            key: 'firstChild',
          })}
        </div>
      );
    }

    const firstChildRefProp = this.isStyledComponent(firstChild) ? 'ref' : 'nodeRef';
    const secondChildRefProp = this.isStyledComponent(secondChild) ? 'ref' : 'nodeRef';

    const childSafariStyles = {
      ...this.state.style,
      flexBasis: '301px',
      overflow: 'scroll',
    };

    const safariStyles: CSSProperties = {
      overflow: 'hidden',
      position: 'fixed',
      width: '100vw',
    };

    const paneStyles = isSafari() ? (window.innerWidth <= 428 ? safariStyles : style) : style;
    const childStyles = isSafari() ? childSafariStyles : this.state.style;

    return (
      <div className={'SplitPane Workspace-column'} ref={nodeRef} style={paneStyles}>
        {cloneElement(firstChild, {
          [firstChildRefProp]: this.PANE1,
          key: 'firstChild',
        })}

        <div
          className={'SplitPane-resize-handle SplitPane-resize-handle-chat horizontal'}
          onMouseDown={this.onMouseDown}
        />

        {cloneElement(secondChild, {
          key: 'secondChild',
          [secondChildRefProp]: this.PANE2,
          style: childStyles,
        })}
      </div>
    );
  }
}
