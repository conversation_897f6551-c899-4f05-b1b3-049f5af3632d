'use client';
import React from 'react';
import {
  DateSeparator,
  MESSAGE_ACTIONS,
  messageHasReactions,
  MessageInput,
  MessageRepliesCountButton,
  MessageUIComponentProps,
  ReactionSelector,
  useChannelActionContext,
  useChatContext,
  useMessageContext,
} from 'stream-chat-react';
import { Event, User } from 'stream-chat';

import styled, { Theme, TC } from '@benzinga/themetron';
import { mix } from 'polished';
import { equals, gte, isNil, lt } from 'ramda';

import { MessageInputText } from './MessageInputText';
import { ChatBadge, ChatBadgeColor } from '../ChatBadge';
import { ChatBadge as ChatBadgeType, ChatNickname as ChatNicknameType } from '@benzinga/chat-manager';

import { MessageBody } from './MessageBody';
import { ChatNickname } from './../ChatNickname';
import { ChatTime } from './../ChatTime';
import { ChatAvatar } from './../ChatAvatar';
import { MessageLayout, Feature } from '../../entities/chatEntity';
import Hooks from '@benzinga/hooks';
import { UserIdentityContext } from '../StreamWidgetContext';
import { SessionContext } from '@benzinga/session-context';
import { MessageActions } from './MessageActions';
import { Attachments } from '../Attachment/Attachments';
import ChatReactions from '../Reactions/ChatReactionsList';
import { QuotedMessage } from '../Quote/QuoteMessage';
import { DateTime } from 'luxon';
import { MessageInfoContext } from '../MessageInfoContext';
import { TrackingManager } from '@benzinga/tracking-manager';
import { LoggingManager } from '@benzinga/session';
import { useGlobalSetting, WidgetContext } from '@benzinga/widget-tools';
import { ChatWidgetManifest } from '../../StreamWidget';
import { PermissionsManager } from '@benzinga/permission-manager';

export const GUTTER_WIDTH = 26;

enum MessageStatus {
  Failed = 'failed',
}

const MAX_RETRY_ATTEMPTS = 5;
const STARTING_TIMEOUT = 1000;

enum MessageEvents {
  userUpdated = 'user.updated',
}

interface State {
  failed: boolean;
  focus: boolean;
  hover: boolean;
  retrying: boolean;
  updatedUser: User | null;
}

export const isSpecialUser = (badgeName: string) => {
  switch (badgeName.toLowerCase()) {
    case 'admin':
    case 'mod':
    case 'mentor':
      return true;
  }
  return false;
};

export const Message: React.FC<MessageUIComponentProps> = React.memo(props => {
  const [state, setState] = React.useState<State>({
    failed: false,
    focus: false,
    hover: false,
    retrying: false,
    updatedUser: null,
  });

  const messageWrapperRef: React.RefObject<HTMLDivElement | null> = React.createRef();

  const identity = React.useContext(UserIdentityContext);
  const globalSettings = useGlobalSetting(ChatWidgetManifest);
  const chatContext = useChatContext();
  const channelContext = useChannelActionContext();
  const session = React.useContext(SessionContext);

  const { clearEdit, editing: isEditing, setEdit } = React.useContext(MessageInfoContext);
  const { getMessageActions, message } = useMessageContext();
  const messageActions = getMessageActions();
  const canReact = messageActions.includes(MESSAGE_ACTIONS.react);

  const editing = isEditing?.id === message.id;
  const widgetContext = React.useContext(WidgetContext);

  const prevMessage = Hooks.usePrevious(message);
  const [retry, setRetry] = React.useState<{
    attempts: number;
    timeout: number;
  }>({
    attempts: 0,
    timeout: 0,
  });

  Hooks.useEffectDidMount(() => {
    chatContext?.client?.on(streamEventsListener);
    if (message?.status === MessageStatus.Failed) {
      setState(preState => ({ ...preState, failed: true }));
    }
  });

  const retryFunc = React.useCallback(async () => {
    if (message && channelContext?.retrySendMessage) {
      setState(preState => ({ ...preState, retrying: true }));
      await channelContext?.retrySendMessage(message);
      setState(preState => ({ ...preState, retrying: false }));
      setRetry({ attempts: retry.attempts + 1, timeout: retry.timeout + STARTING_TIMEOUT });
    }
  }, [message, channelContext, retry.attempts, retry.timeout]);

  const streamEventsListener = React.useCallback((event: Event) => {
    switch (event.type) {
      case MessageEvents.userUpdated:
        if (event.user) {
          setState(preState => ({ ...preState, updatedUser: event.user ?? null }));
        }
        break;
      default:
        break;
    }
  }, []);

  React.useEffect(() => {
    if (
      !state.retrying &&
      !equals(prevMessage && prevMessage?.status, message?.status) &&
      equals(message?.status, MessageStatus.Failed) &&
      lt(retry.attempts, MAX_RETRY_ATTEMPTS)
    ) {
      setTimeout(retryFunc, retry.timeout);
    } else if (
      !equals(prevMessage && prevMessage?.status, message?.status) &&
      equals(message?.status, MessageStatus.Failed) &&
      gte(retry.attempts, MAX_RETRY_ATTEMPTS)
    ) {
      setState(preState => ({ ...preState, failed: true }));
      session
        .getManager(LoggingManager)
        .log('error', { category: 'Chat', message: 'Message Sending Failed' }, ['toast']);
    }
    if (prevMessage && !equals(message?.reaction_counts, prevMessage?.reaction_counts)) {
      const virtualList = document.querySelector('.str-chat__virtual-list');
      virtualList?.firstElementChild?.scrollBy(0, 31);
    }
  }, [message, prevMessage, retry, retryFunc, session, state.retrying]);

  React.useEffect(() => {
    // returned function will be called on component unmount
    return () => {
      chatContext?.client?.off(streamEventsListener);
    };
  }, [chatContext?.client, streamEventsListener]);

  const handleMouseEvent = React.useCallback(
    (eventName: 'focus' | 'hover', stateVal: boolean) => () => {
      if (eventName === 'focus' && !state.focus) setState(preState => ({ ...preState, focus: stateVal }));
      if (eventName === 'hover' && !state.hover) setState(preState => ({ ...preState, hover: stateVal }));
      else setState(preState => ({ ...preState, hover: false }));
    },
    [state.focus, state.hover],
  );

  const handleKeyPress = React.useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (equals(event.key, 'Escape')) {
        clearEdit();
      } else if (equals(event.key, 'Enter')) {
        event.preventDefault();
        setTimeout(() => {
          clearEdit();
        }, 0);
      }
    },
    [clearEdit],
  );

  const onClickNickName = React.useCallback(
    (nickname: ChatNicknameType) => () => {
      if (nickname) {
        session.getManager(TrackingManager).trackChatEvent('click', {
          button_id: 'nickname',
          button_value: nickname || '',
        });
      }
    },
    [session],
  );

  const handleUpdateFocus = React.useCallback((value: boolean) => {
    setState(preState => ({ ...preState, focus: value }));
  }, []);

  const handleRetryClick = React.useCallback(() => {
    setState(preState => ({ ...preState, failed: false }));
    setRetry({
      attempts: 0,
      timeout: 0,
    });
    retryFunc();
  }, [retryFunc]);

  const handleMessageCancel = React.useCallback(() => {
    const message = props.message;
    if (message && channelContext?.removeMessage) {
      channelContext?.removeMessage(message);
    }
    setState(preState => ({ ...preState, failed: false }));
    setRetry({ attempts: 0, timeout: STARTING_TIMEOUT });
  }, [channelContext, props.message]);

  const featureForMessage: Feature = React.useMemo(
    () => ({
      action: 'chat/message/add',
      resource: (chatContext.channel && chatContext.channel?.id) || '#',
    }),
    [chatContext.channel],
  );

  const { groupedByUser } = props;

  const hasMessagePermission = React.useMemo(
    () =>
      session.getManager(PermissionsManager).hasAccess(featureForMessage.action, featureForMessage.resource).ok ??
      false,
    [featureForMessage.action, featureForMessage.resource, session],
  );

  const messageInput = React.memo(() => <MessageInputText onKeyPress={handleKeyPress} />);

  const textAreaStyles: React.CSSProperties = React.useMemo(
    () => ({
      padding: '0.5em 50px 0.5em 0.5em !important',
      resize: 'none', // has to be !important because the style (on textarea.ant-input) is via the stylesheet
    }),
    [],
  );

  const messageInputBox = React.useMemo(
    () => (
      <MessageInput
        additionalTextareaProps={{
          className: 'ant-input TUTORIAL_Chat-Input',
          style: textAreaStyles,
        }}
        disabled={!hasMessagePermission}
        focus
        grow
        Input={messageInput}
        message={message}
      />
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [hasMessagePermission, message],
  );

  const { created_at: createdAt = '', referenceTime, user } = message;

  const nickname = React.useMemo(() => {
    if (state.updatedUser && state.updatedUser.id === user?.id) {
      return (state.updatedUser.nickname ?? state.updatedUser.name ?? null) as ChatNicknameType;
    } else if (identity?.identityUuid === user?.id) {
      return identity?.nickname ?? user?.name ?? '';
    } else {
      return (user?.nickname ?? user?.name ?? null) as ChatNicknameType;
    }
  }, [identity?.identityUuid, identity?.nickname, state.updatedUser, user?.id, user?.name, user?.nickname]);

  const chatMessageFailedContent = React.useMemo(
    () => (
      <Container>
        <MessageFailed>
          Benzinga Chat couldn't send this message<a onClick={handleRetryClick}> Try Again </a> |
          <a onClick={handleMessageCancel}> Cancel </a>
        </MessageFailed>
      </Container>
    ),
    [handleMessageCancel, handleRetryClick],
  );

  const chatAvatar = React.useMemo(
    () => (
      <ChatAvatar
        colorSeed={user?.id ?? ''}
        iconUrl={(user?.avatar as string) ?? ''}
        nickname={nickname}
        width={`${GUTTER_WIDTH}px`}
      />
    ),
    [nickname, user?.avatar, user?.id],
  );

  const isCompactView = React.useMemo(
    () => equals(globalSettings.settings.messageLayout, MessageLayout.compact),
    [globalSettings.settings.messageLayout],
  );

  const renderMessageBody = React.useMemo(
    () => (
      <>
        <MessageBody
          changeDisplayType={isCompactView}
          isGroupedByUser={groupedByUser}
          message={message.text ?? ''}
          nickname={nickname}
          onClickNickName={onClickNickName}
          referenceTime={`${referenceTime}`}
          user={user ?? undefined}
        />
        {state.failed &&
          !state.retrying &&
          // eslint-disable-next-line no-restricted-globals
          equals(status, MessageStatus.Failed) &&
          equals(identity?.identityUuid, user?.id) &&
          chatMessageFailedContent}
      </>
    ),
    [
      chatMessageFailedContent,
      groupedByUser,
      identity?.identityUuid,
      isCompactView,
      message.text,
      nickname,
      onClickNickName,
      referenceTime,
      state.failed,
      state.retrying,
      user,
    ],
  );

  const compactChatMessage = React.useMemo(
    () => (
      <CompactChatMessageHeader>
        <CompactChatMessageContent>
          {!groupedByUser && <ChatTime createdAt={createdAt} isCompactView={isCompactView} />}
          {editing ? messageInputBox : renderMessageBody}
          <Attachments attachments={message.attachments ?? []} />
          {/* {!!message?.latest_reactions?.length && <ChatReactionsList message={message as MessageResponse} />} */}
        </CompactChatMessageContent>
      </CompactChatMessageHeader>
    ),
    [createdAt, editing, groupedByUser, isCompactView, message.attachments, messageInputBox, renderMessageBody],
  );

  const chatMessageHeader = React.useMemo(
    () => (
      <ChatMessageHeader>
        <>
          <ChatNickname nickname={nickname} onClick={onClickNickName(nickname)} />
          {user &&
            user.badges &&
            (user.badges as ChatBadgeType[]).map(badge => <ChatBadge badge={badge} key={badge.name} />)}
          <ChatTime createdAt={createdAt} />
          <ChatSpacer />
        </>
      </ChatMessageHeader>
    ),
    [createdAt, nickname, onClickNickName, user],
  );

  const cleanChatMessage = React.useMemo(
    () => (
      <>
        {!groupedByUser && chatAvatar}
        <ChatMessageContent>
          {!groupedByUser && chatMessageHeader}
          {editing ? messageInputBox : renderMessageBody}
          <Attachments attachments={message.attachments ?? []} />
          {/* {!!message?.latest_reactions?.length && <ChatReactionsList message={message} />} */}
        </ChatMessageContent>
      </>
    ),
    [chatAvatar, chatMessageHeader, editing, groupedByUser, message.attachments, messageInputBox, renderMessageBody],
  );

  const chatMessage = React.useMemo(
    () => (isCompactView ? compactChatMessage : cleanChatMessage),
    [cleanChatMessage, compactChatMessage, isCompactView],
  );

  const handleReplyClick = React.useCallback(
    (event: any) => {
      const virtualList = widgetContext.widgetRef?.querySelector('.str-chat__main-panel');
      virtualList?.classList.add('thread-open-hide-main-div');
      channelContext.openThread(message, event);
    },
    [channelContext, message, widgetContext.widgetRef],
  );

  const hasReactions = React.useMemo(() => messageHasReactions(message), [message]);

  const is24HourTime = React.useCallback(() => {
    const hour = DateTime.fromJSDate(new Date(createdAt)).hour;
    if (hour < 10) return false;
    return true;
  }, [createdAt]);

  const getSpecialBadgeName = React.useCallback(() => {
    if (globalSettings.settings.chatMediaPreferencesSettings.allowSpecialUserHighlighting === 'off') return undefined;
    if (!user || !user.badges) return undefined;
    const specialBadge = (user.badges as ChatBadgeType[]).find(badge => isSpecialUser(badge.name))?.name;
    if (!specialBadge) return undefined;
    if (globalSettings.settings.chatMediaPreferencesSettings.allowSpecialUserHighlighting === 'theme') return 'theme';
    return specialBadge;
  }, [globalSettings.settings.chatMediaPreferencesSettings.allowSpecialUserHighlighting, user]);

  if (isNil(message) || isNil(message.user)) {
    return null;
  }
  if (message.customType === 'message.date') {
    return <DateSeparator date={new Date()} position="center" />;
  }

  return (
    <MessageWrapper
      $badgeName={getSpecialBadgeName()}
      $editing={editing}
      id={message.id}
      key={message.id}
      ref={messageWrapperRef}
    >
      <TextWrapper
        $24HourTime={is24HourTime()}
        $grouped={groupedByUser}
        $hover={state.hover}
        onMouseEnter={() => {
          handleMouseEvent('hover', true)();
        }}
        onMouseLeave={() => {
          handleMouseEvent('hover', false)();
          handleUpdateFocus(false);
        }}
        tabIndex={0}
      >
        {groupedByUser && state.hover && <ChatTime createdAt={createdAt} leftTimeStamp />}
        {canReact && state.focus && <ReactionSelector />}
        {message.quoted_message && message.quoted_message?.user?.id && !editing ? (
          <QuotedMessage message={message} />
        ) : (
          chatMessage
        )}
        {/* {isHoveredOrFocused && <ChatReactions message={message} />} */}
        <MessageActions
          focus={state.focus}
          hover={state.hover}
          message={message}
          onReplyClick={handleReplyClick}
          onUpdateFocus={() => handleUpdateFocus(true)}
          setEdit={setEdit}
        />
      </TextWrapper>
      {hasReactions && <ChatReactions message={message} />}
      {!!message.reply_count && (
        <div className="str-chat__message-simple-reply-button">
          <MessageRepliesCountButton onClick={handleReplyClick} reply_count={message.reply_count} />
        </div>
      )}
    </MessageWrapper>
  );
});

export const ChatSpacer = styled.span`
  display: flex;
  flex-grow: 1;
`;

interface TextProps {
  $grouped?: boolean;
  $hover?: boolean;
  $24HourTime?: boolean;
}

interface MessageProps {
  $editing?: boolean;
  $badgeName?: string;
}

const hoverBackgroundColor = (theme: Theme) => mix(0.9, theme.colors.background, theme.colors.foregroundInactive);
const focusBackgroundColor = (theme: Theme) => mix(0.85, theme.colors.background, theme.colors.brand);
const editBackgroundColor = (theme: Theme, backgroundColor: string) => mix(0.75, backgroundColor, theme.colors.warning);
const specialBackgroundColor = (theme: Theme, userBadge: string, backgroundColor: string) => {
  if (userBadge === 'theme') {
    switch (theme.name.toLowerCase()) {
      case 'antique':
      case 'highcontrast':
        return mix(0.75, backgroundColor, theme.colors.brand);
      default:
        return mix(0.9, backgroundColor, theme.colors.brand);
    }
  }
  if (isSpecialUser(userBadge)) {
    return mix(0.75, backgroundColor, ChatBadgeColor(userBadge, theme));
  }
  return backgroundColor;
};

export const TextWrapper = styled(TC.Row)<TextProps>`
  border-bottom: 1px solid transparent;
  border-left: 1px solid transparent;
  border-right: 1px solid transparent;
  border-top: 1px solid transparent;
  background: inherit;
  flex-shrink: 0;
  justify-content: flex-start;
  padding: ${props =>
    !props.$grouped
      ? '0px 8px 2px 8px'
      : props.$hover
        ? props.$24HourTime
          ? `0px 8px 2px 11.5px`
          : `0px 8px 2px 16.5px`
        : `0px 8px 2px ${GUTTER_WIDTH + 8}px`};
  position: relative;
  transition: border 0.2s;
  display: inline-flex;
  width: 100%;
`;

export const MessageWrapper = styled.div<MessageProps>`
  :focus {
    background: ${props =>
      props.$editing
        ? editBackgroundColor(props.theme, focusBackgroundColor(props.theme))
        : specialBackgroundColor(props.theme, props.$badgeName ?? '', focusBackgroundColor(props.theme))};
    border: 1px solid ${props => mix(0.75, props.theme.colors.brand, props.theme.colors.background)};
  }
  :hover {
    background: ${props =>
      props.$editing
        ? editBackgroundColor(props.theme, hoverBackgroundColor(props.theme))
        : specialBackgroundColor(props.theme, props.$badgeName ?? '', hoverBackgroundColor(props.theme))};
  }
  background: ${props =>
    props.$editing
      ? mix(0.75, props.theme.colors.background, props.theme.colors.warning)
      : specialBackgroundColor(props.theme, props.$badgeName ?? '', props.theme.colors.background)};
`;

export const HoverDate = styled(TC.Row)`
  flex-shrink: 0;
  width: ${GUTTER_WIDTH}px;
`;

export const ChatMessageContent = styled(TC.Column)`
  flex-grow: 1;
`;

export const CompactChatMessageContent = styled(TC.Column)`
  display: flex;
  flex-direction: row;
`;

export const ChatMessageHeader = styled(TC.Row)`
  align-items: baseline;
  flex-wrap: wrap;
  padding: 0.25em;
  margin-right: 0.75em;
  padding-left: 0.5em;
`;

export const CompactChatMessageHeader = styled(TC.Row)`
  align-items: baseline;
  display: contents;
  flex-wrap: wrap;
  padding: 0.25em;
  margin-right: 0.75em;
  padding-left: 0.5em;
`;

const Container = styled(TC.Div)`
  background: ${props => props.theme.colors.backgroundActive};
  color: ${props => props.theme.colors.foregroundInactive};
  line-height: 1.25em;
  margin-left: 0.5em;
  margin-right: 0.5em;
  padding-right: 0.25em;
  white-space: pre-wrap;
  word-break: break-word;
`;

const MessageFailed = styled(TC.Div)`
  color: ${props => props.theme.colors.foregroundInactive};
`;
