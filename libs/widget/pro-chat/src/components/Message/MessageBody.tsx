'use client';
import { User } from 'stream-chat';
import React, { ReactNode } from 'react';
import styled, { TC, ThemeContext, ThemeNames } from '@benzinga/themetron';
import { StockSymbol, UnixTimestamp } from '@benzinga/session';
import { mapIndexed } from 'ramda-adjunct';
import { toUpper } from 'ramda';

import { Token, tokenizeMessage, TokenType } from '../../utils/Chat';
import { ChatNickname } from '../ChatNickname';
import { ChatNickname as ChatNicknameType } from '@benzinga/chat-manager';
import { CashtagPopover, SendLinkContext } from '@benzinga/pro-ui';

interface Props {
  changeDisplayType: boolean;
  isGroupedByUser?: boolean;
  message: string;
  nickname?: string | null;
  onClickNickName?: (nickname: ChatNicknameType) => () => void;
  referenceTime?: UnixTimestamp;
  user?: User;
}

const createMessageTokens = (
  theme: ThemeNames,
  onSymbolClick: (symbol: StockSymbol) => void,
  referenceTime?: UnixTimestamp,
) =>
  mapIndexed<Token, ReactNode>((token: Token, index: number) => {
    const key = `${token.type}:${index}:${token.text}`;
    switch (token.type) {
      case TokenType.text:
        return <span key={key}>{token.text}</span>;
      case TokenType.cashtag:
        return (
          <CashtagPopover
            exchangeSymbol={toUpper(token.text)}
            key={key}
            onSymbolClick={onSymbolClick}
            referenceTime={referenceTime}
            theme={theme}
          />
        );
      case TokenType.url:
        return (
          <a href={token.text} key={key} rel="noreferrer" target="_blank">
            {token.text}
          </a>
        );
    }
  });

export const MessageBody: React.FC<Props> = React.memo(props => {
  const {
    changeDisplayType,
    isGroupedByUser,
    message,
    nickname = '',
    onClickNickName,
    referenceTime = '',
    user,
  } = props;
  const tokens = tokenizeMessage(message ?? '');

  const context = React.useContext(ThemeContext);

  const nicknameContent =
    changeDisplayType && !isGroupedByUser ? (
      <ChatNickname
        changeDisplayType={changeDisplayType}
        nickname={nickname as string | null}
        onClick={onClickNickName ? onClickNickName(nickname as string | null) : undefined}
        user={user}
      />
    ) : null;

  const sendLink = React.useContext(SendLinkContext);

  const messageTokens = createMessageTokens(
    (context.name as ThemeNames) ?? 'dark',
    sendLink.onSymbolClick,
    referenceTime,
  )(tokens);

  return (
    <Container>
      {nicknameContent}
      {messageTokens}
    </Container>
  );
});

export const Container = styled(TC.Div)`
  color: ${props =>
    props.theme.name === 'antique' ? props.theme.colors.foregroundActive : props.theme.colors.foreground};
  line-height: 1.25em;
  margin-left: 0.5em;
  margin-right: 0.5em;
  padding-right: 0.25em;
  white-space: pre-wrap;
  word-break: break-word;
`;
