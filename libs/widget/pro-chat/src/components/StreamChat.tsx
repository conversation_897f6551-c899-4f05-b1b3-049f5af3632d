'use client';
import React, { useContext } from 'react';
import {
  Channel,
  ChatContext,
  Window,
  VirtualizedMessageList,
  StreamMessage,
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  ChannelStateContext,
  Thread,
  DateSeparator,
} from 'stream-chat-react';
import { MessageResponse, StreamChat, UpdatedMessage, User } from 'stream-chat';
import { BzDateTime } from '@benzinga/date-utils';
import styled, { TC, ThemeContext } from '@benzinga/themetron';
import { ChatVideo } from './ChatVideo';
import { Spinner } from '@benzinga/core-ui';
import { StreamInternalContext, UserIdentityContext, ChannelContext } from './StreamWidgetContext';
import { Message, MessageInputText } from './Message';
import { CustomTriggerProvider } from './useTriggers';
import { Feature } from '../entities/chatEntity';
import { ThreadHeader } from './Thread/ThreadHeader';
import { ThreadStart } from './Thread/ThreadStart';
import { ThreadMessage } from './Thread/ThreadMessage';
import { QuotedMessagePreview } from './Quote/QuotedMessagePreview';
import { MessageInput } from './MessageInput/MessageInput';
import { ChatModuleItem, Search } from './Search/Search';
import { useTime } from '@benzinga/time-manager-hooks';
import { MessageInfoContext, MessageInfoContextProvider } from './MessageInfoContext';
import { QuotedMessagePreviewProps } from 'stream-chat-react/dist/components/MessageInput/QuotedMessagePreview';
import { DefaultStreamChatGenerics } from 'stream-chat-react/dist/types/types';
import {
  isSymbolSearchItem,
  isLinkingSearchItem,
  isHoldingSearchItem,
  isVerticalSearchItem,
} from '@benzinga/search-modules';
import { SessionContext } from '@benzinga/session-context';
import { WidgetLinkingManager, isTickerSelectedEvent } from '@benzinga/widget-linking';
import { PermissionsManager } from '@benzinga/permission-manager';
import {
  AuthorSearchItem,
  createAuthorSearchItem,
  isAuthorSearchItem,
  isKeywordSearchItem,
} from '@benzinga/search-modules';

import { Lock } from '@benzinga/themed-icons';
import { ChatChannel } from '@benzinga/chat-manager';
import { useWidgetParameters } from '@benzinga/widget-tools';
import { ChatWidgetManifest } from '../StreamWidget';
import Hooks from '@benzinga/hooks';
import { HoldingsManager } from '@benzinga/data-manager-holdings';
import { VerticalsManager } from '@benzinga/verticals-manager';
import { deepEqual } from '@benzinga/utils';

interface State {
  hasMessagePermission: boolean;
  loading: boolean;
  next: string | undefined;
  nickname: string | undefined | null;
  page: number;
  quote_id: string;
  renderNotification: boolean;
  searchMessages: MessageResponse[];
}

enum ChatEvents {
  connectionChanged = 'connection.changed',
  mutesUpdated = 'notification.mutes_updated',
  newMessage = 'message.new',
}

const getMessageWithTimeOffset = (
  message: StreamMessage | MessageResponse,
  timeOffset = 0,
): StreamMessage | MessageResponse => {
  const minutes = timeOffset + new Date().getTimezoneOffset();
  const created_at = new Date(BzDateTime.from(message.created_at).plus({ minutes }).valueOf());
  const updated_at = new Date(BzDateTime.from(message.updated_at).plus({ minutes }).valueOf());
  //
  return {
    ...message,
    created_at,
    key: message.id,
    updated_at,
  } as StreamMessage;
};

export const StreamChatComponentInternals: React.FC = () => {
  const themeContext = useContext(ThemeContext);
  const chatContext = useContext(ChatContext);
  const session = React.useContext(SessionContext);

  const linkingManager = session.getManager(WidgetLinkingManager);

  const internalContext = useContext(StreamInternalContext);
  const channelContext = useContext(ChannelContext);
  const identity = useContext(UserIdentityContext);
  const messageEdit = useContext(MessageInfoContext);
  const lastMessagesUsedInEditMode = React.useRef<StreamMessage<DefaultStreamChatGenerics>[]>([]);

  const time = useTime();

  const activeChannel = React.useMemo(() => chatContext?.channel ?? null, [chatContext?.channel]);
  const widgetParameters = useWidgetParameters(ChatWidgetManifest);

  const [state, setStreamChatState] = React.useState<State>({
    hasMessagePermission: false,
    loading: false,
    next: '',
    nickname: identity?.nickname,
    page: 0,
    quote_id: '',
    renderNotification: false,
    searchMessages: [],
  });

  const defaultAuthors = React.useRef<AuthorSearchItem[]>([]);

  const setChannel = React.useCallback(
    (chatChannel: ChatChannel) => {
      const onChatChannelSwitch = internalContext.onChatChannelSwitch;
      onChatChannelSwitch(chatChannel);
      setStreamChatState(preState => ({
        ...preState,
        page: 0,
        searchMessages: [],
      }));
    },
    [internalContext.onChatChannelSwitch],
  );

  const customMessage = React.useCallback(
    (message: UpdatedMessage, quotedMessage?: StreamMessage) => {
      if (message.text || message.attachments) {
        message = {
          ...message,
          created_by: identity?.nickname?.toLowerCase() ?? state.nickname?.toLowerCase(),

          //Server expects a string array,
          //client type says this is a string array, but it is in fact a User[] that's being sent to the server.
          //Refer CustomTriggerProvider for our implementation of user autocomplete.
          mentioned_users: (message.mentioned_users as unknown as User[])?.map(user => user.id),
          quoted_message_id: quotedMessage?.id ?? undefined,
        };
        const words = ('text' in message && typeof message.text === 'string' && message.text.split(' ')) || [];
        for (const idx in words) {
          if (words[idx].startsWith('$')) {
            message = {
              ...message,
              ticker:
                '$' +
                words[idx]
                  ?.replace(/[^\w\s]|_/g, '')
                  ?.replace(/\s+/g, ' ')
                  .toUpperCase(),
            };
            break;
          }
        }
      }
      return message;
    },
    [identity?.nickname, state.nickname],
  );

  type EditHandler = (cid: string, updatedMessage: UpdatedMessage) => ReturnType<StreamChat['updateMessage']>;

  const handleEdit: EditHandler = React.useCallback(
    (_id, message) => {
      const clonedMessage: UpdatedMessage = Object.assign({}, message);
      delete clonedMessage.quoted_message;
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      return chatContext.client?.updateMessage(customMessage(clonedMessage));
    },
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    [chatContext.client, customMessage],
  );

  React.useEffect(() => {
    if (channelContext && !internalContext.switchedChannel) {
      const targetChannel =
        channelContext.find(channel => channel?.name === 'benzinga-pro-lounge') ?? channelContext[0];

      setChannel(targetChannel);
    }
  }, [channelContext, internalContext.switchedChannel, setChannel]);

  React.useEffect(() => {
    const switchedChatChannel = internalContext.switchedChannel;
    if (switchedChatChannel && activeChannel?.id !== switchedChatChannel.channelUuid) {
      const setActiveChannelGetStream = chatContext?.setActiveChannel;
      const switchedChannel = Object.entries(chatContext?.client?.activeChannels ?? {}).find(
        ([k, _]) => matchKeys(k, switchedChatChannel.channelUuid).isKeySame,
      );
      if (!setActiveChannelGetStream || !switchedChannel) {
        return;
      }
      setActiveChannelGetStream(switchedChannel[1]);
    }
  }, [
    activeChannel?.id,
    chatContext?.client?.activeChannels,
    chatContext?.setActiveChannel,
    internalContext.switchedChannel,
  ]);

  const containsRestrictedTermsOrMutedUserOrDeletedMessages = React.useCallback(
    obj => {
      const allMutedUsers = chatContext?.client.mutedUsers ?? [];
      if (!obj || typeof obj?.text !== 'string' || obj.deleted_at) {
        return false;
      }
      const restrictedTerms = ['discord.gg', 'discord.com'];
      const message = obj?.text.toLowerCase();

      const mutedUserIds = new Set(allMutedUsers.map(mutedUser => mutedUser.target.id));
      return !restrictedTerms.some(term => message.includes(term)) && !mutedUserIds.has(obj?.user?.id);
    },
    [chatContext?.client.mutedUsers],
  );

  const getFilteredMessages = React.useCallback(
    (messages?: StreamMessage[]) => {
      if (!messages) {
        return [];
      }

      return messages
        .filter(message => containsRestrictedTermsOrMutedUserOrDeletedMessages(message))
        .sort((a, b) => {
          if (a.created_at != null && b.created_at != null)
            return BzDateTime.from(a.created_at).valueOf() - BzDateTime.from(b.created_at).valueOf();
          else return 0;
        });
    },
    [containsRestrictedTermsOrMutedUserOrDeletedMessages],
  );

  const convertFilter = React.useCallback(
    (items: ChatModuleItem[]) => {
      const author: string[] = [];
      const symbol: string[] = [];
      const keyword: string[] = [];
      items.forEach(item => {
        if (isAuthorSearchItem(item)) {
          author.push(`${item.data.authorName.toLowerCase()}`);
        } else if (isSymbolSearchItem(item)) {
          symbol.push(`$${item.data.symbol.toLowerCase()}`);
          symbol.push(`$${item.data.symbol}`);
        } else if (isLinkingSearchItem(item)) {
          const sym = linkingManager
            .getWidgetLinkFeedByID(item.data.linkId)
            ?.getLink()
            ?.events.find(isTickerSelectedEvent)?.symbol;
          if (sym != null) {
            symbol.push(`$${sym}`);
            symbol.push(`$${sym.toLowerCase()}`);
          }
        } else if (isKeywordSearchItem(item)) {
          keyword.push(item.data.keyword);
        } else if (isHoldingSearchItem(item)) {
          const holdingSymbols = session
            .getManager(HoldingsManager)
            .getCachedHoldingByName(item.data.holdingName)?.holdingSymbols;
          if (holdingSymbols == null) return;
          holdingSymbols.forEach(s => {
            symbol.push(`$${s.toLowerCase()}`);
            symbol.push(`$${s}`);
          });
        } else if (isVerticalSearchItem(item)) {
          const verticalSymbols = session
            .getManager(VerticalsManager)
            .getCachedVerticalByVerticalName(item.data.verticalName)?.verticalSymbols;
          if (verticalSymbols == null) return;
          verticalSymbols.forEach(s => {
            symbol.push(`$${s.toLowerCase()}`);
            symbol.push(`$${s}`);
          });
        }
      });
      const filter: Record<string, object> = {};
      if (author.length > 0) {
        //filter = { $or: [{ created_by: { $in: author } }, { text: { $autocomplete: author.toString() } }]};
        filter['created_by'] = { $in: author };
      }
      if (symbol.length > 0) {
        filter['ticker'] = { $in: Array.from(new Set(symbol)) };
        //filter = { $or: [{ ticker: { $in: symbol } }, { text: { $autocomplete: symbol.toString() } }]};
      }
      if (keyword.length > 0) {
        filter['text'] = { $autocomplete: keyword.toString() };
      }

      return Object.keys(filter).length === 0 ? undefined : filter;
    },
    [linkingManager, session],
  );

  const prevSearchRequest = React.useRef<any>(undefined);
  const prevActiveChannelSearch = React.useRef<typeof activeChannel | undefined>(undefined);
  const onSearch = React.useCallback(
    async (filter: ChatModuleItem[], page: number): Promise<number> => {
      if (activeChannel !== prevActiveChannelSearch.current) {
        setStreamChatState(preState => {
          return {
            ...preState,
            searchMessages: [],
          };
        });
      }
      prevActiveChannelSearch.current = activeChannel;
      if (prevSearchRequest.current) {
        await prevSearchRequest.current;
      }

      setStreamChatState(preState => ({ ...preState, loading: true }));

      if ((page !== 0 && !state.next) || activeChannel === undefined) {
        //not our first query but no pagination data either
        setStreamChatState(preState => ({ ...preState, loading: false }));
        return 0;
      }

      prevSearchRequest.current = activeChannel?.search(
        convertFilter(filter) as any,
        page === 0 ? { limit: 50, sort: [{ created_at: -1 }] } : { limit: 50, next: state.next },
      );

      const data = await prevSearchRequest.current;
      if (prevActiveChannelSearch.current === activeChannel) {
        const messages: MessageResponse[] = [];
        data?.results.reverse().forEach(result => {
          messages.push(result.message as MessageResponse);
        });
        const filteredMessages = getFilteredMessages(messages as StreamMessage[]) as MessageResponse[];
        if (state.searchMessages.length === 0 && messages.length === 0) {
          setStreamChatState(preState => ({ ...preState, loading: false }));
        } else {
          setStreamChatState(preState => {
            return {
              ...preState,
              loading: false,
              next: data?.next ?? '',
              searchMessages: [...preState.searchMessages, ...filteredMessages],
            };
          });
        }

        return messages.length;
      }
      return 0;
    },
    [activeChannel, convertFilter, getFilteredMessages, state.next, state.searchMessages.length],
  );

  const filter = React.useCallback(
    (filter: ChatModuleItem[]) => {
      const setParameters = widgetParameters.setParameters;
      setParameters(preState => ({ ...preState, searchFilters: filter }));
      if (filter.length !== 0) {
        setStreamChatState(preState => ({
          ...preState,
          searchMessages: [],
        }));
        onSearch(filter, 0);
        setStreamChatState(preState => ({ ...preState, page: 1 }));
      } else {
        setStreamChatState(preState => ({
          ...preState,
          loading: false,
          page: 0,
          searchMessages: [],
        }));
      }
    },
    [onSearch, widgetParameters.setParameters],
  );
  const matchKeyword = (arr1: string[], arr2: string[]) => {
    return arr1.some(text => arr2.some(text2 => text2.includes(text)));
  };

  const dateFormatter = React.useCallback(
    (date: Date) => {
      const dateTime = BzDateTime.fromLocaleDate(date);
      const isDay = time.isDay;
      let prefix = '';
      if (isDay(dateTime, 'today')) {
        prefix = 'Today at';
      } else if (isDay(dateTime, 'yesterday')) {
        prefix = 'Yesterday at';
      } else if (
        BzDateTime.dateNow().plus({ days: -7 }).isBefore(dateTime) &&
        dateTime.getSafeDate().getDay() !== BzDateTime.dateNow().getDay()
      ) {
        prefix = `Last ${dateTime.getWeekday()} at`;
      } else {
        return new Date(dateTime.valueOf()).toLocaleDateString();
      }
      return `${prefix} ${dateTime.toISOTimeString({ timeFormat: time.timeFormat, withoutSeconds: true })}`;
    },
    [time.isDay, time.timeFormat],
  );

  const loadMore = React.useCallback((): Promise<number> => {
    const records = onSearch(widgetParameters.parameters.searchFilters, state.page);
    setStreamChatState(preState => ({ ...preState, page: preState.page + 1 }));
    return records;
  }, [onSearch, state.page, widgetParameters.parameters.searchFilters]);

  const matchKeys = (key1: any, key2: any) => {
    const keyMatched = key1.split(':');
    const cid = keyMatched[1];

    return {
      channelType: keyMatched[0],
      isKeySame: keyMatched && cid === key2,
    };
  };

  React.useEffect(() => {
    chatContext?.client.on(ChatEvents.connectionChanged, (con: any) => {
      setStreamChatState(preState =>
        preState.renderNotification !== !con.online ? { ...preState, renderNotification: !con.online } : preState,
      );
    });
  }, [chatContext?.client]);

  const featureForMessage: Feature = React.useMemo(
    () => ({
      action: 'chat/message/add',
      resource: (activeChannel && activeChannel?.id) || '#',
    }),
    [activeChannel],
  );

  const getUniqueAuthors = React.useCallback((messages: StreamMessage[]) => {
    const authors: [string, string][] = [];
    messages.forEach(message => {
      if (message.user?.id && !authors.some(author => author[0] === message.user?.id)) {
        authors.push([message.user.id, message.user.name ?? '']);
      }
    });
    return authors.map(i => createAuthorSearchItem(i[0], i[1]));
  }, []);

  //update message permissions
  React.useEffect(() => {
    let hasMessagePermission = true;
    if (activeChannel == null) return;
    if (activeChannel.type === 'placeholder' || activeChannel.type === 'preview') {
      hasMessagePermission = false;
    }
    if (hasMessagePermission) {
      hasMessagePermission =
        session.getManager(PermissionsManager).hasAccess(featureForMessage.action, featureForMessage.resource).ok ??
        false;
    }
    if (hasMessagePermission && activeChannel.type === 'announcement') {
      if (!activeChannel.state.membership.is_moderator) {
        hasMessagePermission = false;
      }
    }

    setStreamChatState(s => ({ ...s, hasMessagePermission }));
  }, [activeChannel, featureForMessage.action, featureForMessage.resource, session]);

  React.useEffect(() => {
    const chan = activeChannel ?? undefined;
    if (chan && 'watch' in chan) {
      chan.watch();
    }
    return () => {
      if (chan && 'stopWatching' in chan && !chan.disconnected) {
        chan.stopWatching();
      }
    };
  }, [activeChannel]);

  const applySearchFilter = React.useCallback(
    (messages: any[]) => {
      if (!widgetParameters.parameters.searchFilters) {
        return messages;
      }

      let validMessages = messages.filter(message => message?.id);

      if (validMessages.length === 0) {
        return [];
      }

      const author: string[] = [];
      const symbol: string[] = [];
      const keyword: string[] = [];

      widgetParameters.parameters.searchFilters.forEach(tag => {
        if (isAuthorSearchItem(tag)) {
          author.push(tag.data.authorName.toLowerCase());
        } else if (isSymbolSearchItem(tag)) {
          symbol.push(`$${tag.data.symbol.toLowerCase()}`);
        } else if (isLinkingSearchItem(tag)) {
          const sym = linkingManager
            .getWidgetLinkFeedByID(tag.data.linkId)
            ?.getLink()
            ?.events.find(isTickerSelectedEvent)?.symbol;
          if (sym != null) {
            symbol.push(`$${sym.toLowerCase()}`);
          }
        } else if (isKeywordSearchItem(tag)) {
          keyword.push(tag.data.keyword.toLowerCase());
        } else if (isHoldingSearchItem(tag)) {
          const holdingSymbols = session
            .getManager(HoldingsManager)
            .getCachedHoldingByName(tag.data.holdingName)?.holdingSymbols;
          if (holdingSymbols == null) return;
          holdingSymbols.forEach(s => {
            symbol.push(`$${s.toLowerCase()}`);
          });
        } else if (isVerticalSearchItem(tag)) {
          const verticalSymbols = session
            .getManager(VerticalsManager)
            .getCachedVerticalByVerticalName(tag.data.verticalName)?.verticalSymbols;
          if (verticalSymbols == null) return;
          verticalSymbols.forEach(s => {
            symbol.push(`$${s.toLowerCase()}`);
          });
        }
      });

      if (symbol.length > 0) {
        validMessages = validMessages.filter(message => symbol.includes((message.ticker ?? '').toLowerCase()));
      }

      if (author.length > 0) {
        validMessages = validMessages.filter(message => author.includes((message.created_by ?? '').toLowerCase()));
      }

      if (keyword.length > 0) {
        validMessages = validMessages.filter(message => {
          const messageText = (message.text ?? '').toLowerCase();
          return keyword.some(kw => messageText.includes(kw));
        });
      }

      return validMessages.filter(
        (message, idx, arr) => !!message?.id && !arr.slice(idx + 1).some(m => m?.id === message.id),
      );
    },
    [linkingManager, session, widgetParameters.parameters.searchFilters],
  );

  const [filterCondition] = Hooks.useThrottle(filter, 300);

  const prevSearchFilters = React.useRef<ChatModuleItem[] | undefined>(undefined);
  const prevActiveChannel = React.useRef<typeof activeChannel | undefined>(undefined);
  React.useEffect(() => {
    if (
      activeChannel &&
      (!deepEqual(widgetParameters.parameters.searchFilters, prevSearchFilters.current) ||
        prevActiveChannel.current !== activeChannel)
    ) {
      filterCondition(widgetParameters.parameters.searchFilters);
      prevSearchFilters.current = widgetParameters.parameters.searchFilters;
      prevActiveChannel.current = activeChannel;
    }
  }, [widgetParameters.parameters.searchFilters, filterCondition, activeChannel]);

  return React.useMemo(() => {
    if (chatContext && chatContext.client && typeof chatContext.setActiveChannel === 'function') {
      const activeChannels = chatContext.client.activeChannels;
      if (Object.keys(activeChannels).length === 0) {
        return (
          <LoadingContainer>
            <Spinner />
            <LoadingTitle>Loading chat</LoadingTitle>
          </LoadingContainer>
        );
      }
    }
    return (
      <>
        {widgetParameters.parameters.searchVisible && widgetParameters.parameters.searchFilters && (
          <div>
            <Search defaultAuthors={defaultAuthors.current} />
          </div>
        )}
        {state.loading && <LoadingIndicator />}
        <ChatVideo theme={themeContext.name ?? 'dark'} />
        <Channel
          DateSeparator={props => <DateSeparator date={props.date} formatDate={dateFormatter} position="right" />}
          doUpdateMessageRequest={handleEdit}
          key={time.timeFormat}
          LoadingIndicator={LoadingIndicator}
          QuotedMessagePreview={QuotedMessagePreview as React.ComponentType<QuotedMessagePreviewProps> | undefined}
          ThreadHeader={ThreadHeader}
          ThreadStart={ThreadStart}
          TriggerProvider={CustomTriggerProvider}
          TypingIndicator={() => null}
        >
          <Window>
            {activeChannel?.type === 'placeholder' ? (
              <MessageListPlaceholder>
                <LockDiv>
                  <Lock />
                </LockDiv>
                <LockMessageDiv>Please upgrade plan to access content</LockMessageDiv>
              </MessageListPlaceholder>
            ) : (
              <ChannelStateContext.Consumer>
                {(channelCtx: any) => {
                  const newMessages = getFilteredMessages(channelCtx.messages);
                  const messages = messageEdit.editing ? lastMessagesUsedInEditMode.current : newMessages;
                  lastMessagesUsedInEditMode.current = messages;
                  const searchMode =
                    state.searchMessages.length > 0 || widgetParameters.parameters.searchFilters.length > 0;
                  const filteredMessages = searchMode
                    ? applySearchFilter([...state.searchMessages, ...messages])
                    : messages;
                  const displayMessages = filteredMessages.map(message => {
                    return getMessageWithTimeOffset(message, time.timeOffset);
                  });
                  defaultAuthors.current = getUniqueAuthors([...messages].reverse());

                  return (
                    <VirtualizedMessageList
                      additionalVirtuosoProps={{
                        increaseViewportBy: { bottom: 300, top: 10 },
                        itemSize: el => el.getBoundingClientRect().height,
                      }}
                      defaultItemHeight={54}
                      hasMoreNewer={messages[messages.length - 1]?.id !== newMessages[newMessages.length - 1]?.id}
                      hideDeletedMessages
                      hideNewMessageSeparator
                      key={state.searchMessages.length > 0 ? 'searchMessageList' : 'messageList'}
                      Message={Message as any}
                      {...(searchMode
                        ? {
                            disableDateSeparator: false,
                            loadMore: loadMore,
                            messages: displayMessages,
                          }
                        : { disableDateSeparator: false, messages: displayMessages, shouldGroupByUser: true })}
                    />
                  );
                }}
              </ChannelStateContext.Consumer>
            )}
            <MessageInput
              customMessage={customMessage}
              hasMessagePermission={state.hasMessagePermission}
              nickname={state.nickname}
            />
          </Window>
          <Thread
            additionalMessageInputProps={{
              Input: MessageInputText,
              additionalTextareaProps: {
                className: 'ant-input TUTORIAL_Chat-Input',
                style: textAreaStyles,
              },
              disabled: !state.hasMessagePermission,
              grow: true,
            }}
            additionalVirtualizedMessageListProps={{
              Message: Message,
              disableDateSeparator: false,
              hideDeletedMessages: true,
              shouldGroupByUser: true,
              threadList: true,
            }}
            //fullWidth
            Message={ThreadMessage}
            virtualized
          />
        </Channel>
      </>
    );
  }, [
    chatContext,
    widgetParameters.parameters.searchVisible,
    widgetParameters.parameters.searchFilters,
    state.loading,
    state.hasMessagePermission,
    state.nickname,
    state.searchMessages,
    themeContext.name,
    handleEdit,
    time.timeFormat,
    time.timeOffset,
    activeChannel?.type,
    customMessage,
    dateFormatter,
    getFilteredMessages,
    messageEdit.editing,
    applySearchFilter,
    getUniqueAuthors,
    loadMore,
  ]);
};

export const StreamChatComponent: React.FC = () => (
  <MessageInfoContextProvider>
    <StreamChatComponentInternals />
  </MessageInfoContextProvider>
);

const LockDiv = styled.div`
  font-size: 30px;
  svg {
    fill: ${props => props.theme.colors.accent};
  }
`;

const LockMessageDiv = styled.div`
  margin-top: 10px;
`;

const LoadingTitle = styled.div`
  margin-top: 30px;
  font-size: 13px;
  z-index: 2;
`;

const LoadingContainer = styled(TC.Column)`
  align-items: center;
  background-color: ${props => props.theme.colors.background};
  flex-basis: 0;
  height: 100%;
  position: absolute;
  text-align: center;
  justify-content: center;
  width: 100%;
  z-index: 1;
`;

const HistorySpinnerContainer = styled(TC.Row)`
  text-align: center;
  display: block;
  position: relative;
`;

const MessageListPlaceholder = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-size: 100%;
  height: 100%;
  width: 100%;
`;

const LoadingIndicator = () => (
  <HistorySpinnerContainer>
    <Spinner />
  </HistorySpinnerContainer>
);

const textAreaStyles: React.CSSProperties = {
  padding: '0.5em 50px 0.5em 0.5em !important',
  resize: 'none', // has to be !important because the style (on textarea.ant-input) is via the stylesheet
};
