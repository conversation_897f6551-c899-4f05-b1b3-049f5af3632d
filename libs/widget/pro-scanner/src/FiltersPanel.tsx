'use client';
import React from 'react';

import styled from '@benzinga/themetron';
import { Close } from '@benzinga/themed-icons';
import { FilterBox, Tools } from './filters/FilterBox';
import { FilterSearch } from './filters2/FilterSearch';
import { Icon } from '@benzinga/core-ui';
import { faFilterSlash } from '@fortawesome/pro-solid-svg-icons';
import { faSearch } from '@fortawesome/pro-regular-svg-icons';
import { Button, Input, Result, Tooltip } from 'antd';
import { Permission } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { useScannerDefs, useScannerFilterSource } from '@benzinga/scanner-manager-hooks';
import { DataField, FilterObject, QuotesV3FieldsManager, TargetSource } from '@benzinga/quotes-v3-fields-manager';
import { Warning as WarningIcon } from '@benzinga/valentyn-icons';
import { filterSearchSelected, searchResults } from '@benzinga/scanner-manager';

/**
 * Component to list/search/select filters for the scanner
 */
export const FiltersPanel: React.FC<{
  className?: string;
  permission?: Permission;
  selectedFilters: FilterObject[];
  onFiltersChanged: (filters: FilterObject[]) => void;
  isScreener?: boolean;
  source?: TargetSource;
}> = props => {
  const session = React.useContext(SessionContext);
  const { isFilterMatchSource } = useScannerFilterSource(props.source);

  const [searchText, setSearchText] = React.useState<string>('');

  const updateFilters = (filters: FilterObject[]) => {
    props.onFiltersChanged(filters);
  };

  const handleRemove = (removedFilter: FilterObject) => {
    updateFilters(props.selectedFilters.filter(filter => filter.field !== removedFilter.field));
  };

  const removeAll = () => {
    updateFilters([]);
  };

  const defs = useScannerDefs();
  const dataFields = React.useMemo(() => defs?.filter(df => df.filterable) ?? [], [defs]);
  const searchResult = React.useMemo(() => {
    return searchResults(dataFields, searchText);
  }, [dataFields, searchText]);

  const selectedSearchedFilters = React.useMemo(() => {
    return filterSearchSelected(props.selectedFilters, session, searchText);
  }, [props.selectedFilters, searchText, session]);

  const isFilterDisabled = React.useCallback(
    (filter: DataField): boolean => {
      return !isFilterMatchSource(filter);
    },
    [isFilterMatchSource],
  );

  const filterApplicationWarning = React.useMemo(() => {
    return props?.selectedFilters?.some(
      filter =>
        !isFilterMatchSource(session.getManager(QuotesV3FieldsManager).getScannerDefFromId(filter.field) as DataField),
    );
  }, [session, props.selectedFilters, isFilterMatchSource]);

  return (
    <FiltersContainer className={props.className}>
      <FiltersContainerRow>
        <Input allowClear onChange={ev => setSearchText(ev.target.value)} placeholder="Search.." size="middle" />
        <Tooltip placement="bottomRight" title="Search Filters">
          <StyledButton size="middle">
            <StyledIcon icon={faSearch} />
          </StyledButton>
        </Tooltip>
        <Tooltip placement="bottomRight" title="Clear Filters">
          <StyledButton disabled={props.selectedFilters.length === 0} onClick={removeAll} size="middle">
            <StyledIcon icon={faFilterSlash} />
          </StyledButton>
        </Tooltip>
      </FiltersContainerRow>
      <FiltersContainerRowBody>
        {searchResult.length > 0 && (
          <FilterSearchStyler>
            <StyledFilterSearch
              isScreener={props.isScreener}
              onAdd={filter => updateFilters([...props.selectedFilters, filter])}
              onRemove={removed =>
                updateFilters(props.selectedFilters.filter(filter => filter.field !== removed.field))
              }
              searchText={searchText}
              selectedFilters={props.selectedFilters}
            />
          </FilterSearchStyler>
        )}
        <Main>
          {searchResult.length === 0 ? (
            <Result title="No filters available with Search criteria." />
          ) : selectedSearchedFilters.length === 0 ? (
            <Result title="No filters selected. Add filters from the left." />
          ) : (
            selectedSearchedFilters
              .filter((item): item is { df: DataField; filter: FilterObject } => !!item)
              .map(({ df, filter }) => {
                return (
                  <FilterCt key={filter.field}>
                    <FilterBox
                      dataField={df}
                      disabled={isFilterDisabled(df)}
                      filter={filter}
                      isScreener={props.isScreener}
                      isSingleNewsfeedField={true}
                      onChange={filter => updateFilters(mergeFilter(filter, props.selectedFilters))}
                      permission={props.permission}
                      tools={[
                        <div className="remove" onClick={() => handleRemove(filter)}>
                          <CloseIcon />
                        </div>,
                      ]}
                    />
                  </FilterCt>
                );
              })
          )}
        </Main>
      </FiltersContainerRowBody>
      {filterApplicationWarning && (
        <Caution>
          <Warning />
          <span>Filters marked with the caution symbol do not impact the table results.</span>
        </Caution>
      )}
    </FiltersContainer>
  );
};

const CloseIcon = styled(Close)`
  fill: ${props => props.theme.colors.foregroundInactive} !important;
  &:hover {
    fill: ${props => props.theme.colors.danger} !important;
  }
`;

const Caution = styled.div`
  position: absolute;
  bottom: 5px;
  right: 30px;
  display: flex;
  align-items: center;
`;

const StyledButton = styled(Button)`
  height: 34px;
  margin-left: -1px;

  &:hover {
    z-index: 1;
  }
`;

const StyledIcon = styled(Icon)`
  display: flex;
`;

const StyledFilterSearch = styled(FilterSearch)`
  border-right: solid 1px ${props => props.theme.colors.backgroundActive};
  @media (max-width: 620px) {
    margin-left: 13px;
  }
`;

const FiltersContainer = styled.div`
  display: flex;
  flex-direction: column;
  @media (max-width: 620px) {
    padding-left: 7px;
  }
`;

const FiltersContainerRow = styled.div`
  display: flex;

  .anticon-search {
    color: ${props => props.theme.colors.foregroundInactive};
  }
`;

const FiltersContainerRowBody = styled(FiltersContainerRow)`
  padding-left: 4px;
  overflow: hidden;
  position: relative;
  @media (max-width: 620px) {
    flex-direction: column;
  }
`;

const FilterSearchStyler = styled.div`
  width: 210px;
  @media (max-width: 620px) {
    height: 20vh;
    width: auto;
    border-bottom: 1px solid #77afff;
    padding-bottom: 13px;
  }
`;

const Main = styled.div`
  margin-left: 5px;
  margin-top: 5px;
  width: 100%;
  min-width: 250px;
  overflow-y: auto;
  position: relative;
`;

const FilterCt = styled.div`
  padding: 5px;
  display: flex;

  ${Tools} {
    line-height: 25px;
    padding-left: 2px;
  }
`;

function mergeFilter(filter: FilterObject, filters: FilterObject[]) {
  return filters.map(f => (f.field === filter.field ? filter : f));
}

const Warning = styled(WarningIcon)`
  fill: ${props => props.theme.colors.accent};
  margin-right: 8px;
`;
