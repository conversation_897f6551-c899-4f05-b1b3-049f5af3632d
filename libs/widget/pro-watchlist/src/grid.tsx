import React, { startTransition } from 'react';

import { StockSymbol } from '@benzinga/session';
import { Watchlist } from '@benzinga/watchlist-manager';
import { SessionContext } from '@benzinga/session-context';
import { addTutorialClasses } from '@benzinga/frontend-utils';
import { arrayDeepDifference, arrayDifference, hasLength } from '@benzinga/utils';
import {
  gridOptions as globalGridOptions,
  BenzingaGrid,
  GridExportParams,
  SendLinkContext,
  exportToCSV,
  useLayoutFill,
  useQuoteColDef,
} from '@benzinga/pro-ui';
import { DefaultTableParameters, TableParameters } from '@benzinga/ag-grid-utils';

import {
  ColDef,
  ColGroupDef,
  CellFocusedEvent,
  CellKeyDownEvent,
  GridApi,
  GridOptions,
  CellRange,
  MenuItemDef,
  GetContextMenuItemsParams,
  CsvExportParams,
} from '@ag-grid-community/core';
import { TrackingManager } from '@benzinga/tracking-manager';
import { Watchlist as WatchlistIcon } from '@benzinga/themed-icons';
import { useDataHook } from './hooks/useDataHook';

interface Props {
  autoselectTicker: boolean;
  gridLayout: TableParameters;
  onGridLayoutChanged: (parameters: Partial<TableParameters>) => void;
  removeSymbolsFromList: (symbol: StockSymbol[]) => void;
  watchlist: Watchlist;
}

interface State {
  defaultColDef: ColDef & { isWatchlists: boolean };
  defaultColGroupDef: ColDef;
}

export interface WatchlistGridHolderRef {
  exportToCSV: () => void;
  setSavedGridParameters: () => void;
}

export const WatchlistGrid = React.forwardRef<WatchlistGridHolderRef, Props>((props, forwardedRef) => {
  const { onGridLayoutChanged, removeSymbolsFromList, watchlist } = props;
  const sendLink = React.useContext(SendLinkContext);
  const session = React.useContext(SessionContext);
  const gridApi = React.useRef<GridApi | null>(null);
  const gridLayout = React.useMemo(
    () => ({
      ...DefaultTableParameters,
      ...props.gridLayout,
      columns:
        props.gridLayout?.columns.map(col => ({
          ...col,
          hide: col.hide ?? false,
        })) ?? [],
    }),
    [props.gridLayout],
  );
  const columnLayout = useLayoutFill(gridLayout.columns);

  const columnDefs = useQuoteColDef(columnLayout);

  const watchlistColumnDefs = React.useMemo<ColDef[]>(() => {
    const colDefs: (ColDef | ColGroupDef)[] = columnDefs;
    return addTutorialClasses(colDefs, 'watchlist') as ColDef[];
  }, [columnDefs]);

  const { transaction } = useDataHook({
    gridApi,
    gridLayout,
    symbols: props.watchlist.symbols.map(w => w.symbol),
    watchlistId: watchlist.watchlistId,
  });

  const state = React.useMemo<State>(
    () => ({
      defaultColDef: {
        filter: undefined,
        isWatchlists: true,
        minWidth: 75,
        resizable: true,
        sortable: true,
      },
      defaultColGroupDef: {
        headerClass: ['u-alignCenter'],
      },
    }),
    [],
  );

  const watchlistProcessCellCallback: CsvExportParams['processCellCallback'] = params => {
    if (params.column.getColId() === 'notes') {
      return params.value?.notes.length > 0 ? params.value.notes[0].text : '';
    }
    return params.value;
  };
  const exportToCSVHelper = React.useCallback(() => {
    if (gridApi.current) {
      const columnDefs = gridApi.current.getColumnDefs() as ColGroupDef[];
      const fileName = watchlist?.name ?? 'BZ Pro Watchlist';
      const params = exportToCSV(fileName);
      params.columnKeys = columnDefs.flatMap(
        col =>
          col.children?.flatMap(child => (typeof child === 'object' && 'field' in child ? child.field || '' : '')) ||
          [],
      );
      params.processCellCallback = watchlistProcessCellCallback;

      gridApi.current.exportDataAsCsv(params);
    }
  }, [watchlist?.name]);

  const exportParams = React.useMemo<GridExportParams>(
    () => ({
      filename: watchlist?.name ?? 'BZ Pro Watchlist',
      processCellCallback: watchlistProcessCellCallback,
    }),
    [watchlist?.name],
  );

  const deselectAll = React.useCallback(() => {
    if (gridApi.current) {
      gridApi.current.deselectAll();
      gridApi.current.clearRangeSelection();
    }
  }, []);

  const onGridReady = React.useCallback(
    (params: { api: GridApi }) => {
      if (params) {
        gridApi.current = params.api;
        transaction.setGridApi(params.api);
      }
    },
    [transaction],
  );

  const customMenuItems = React.useMemo(
    () => ({
      openSymbolDetailsWidgetFor: (symbol: StockSymbol | undefined) => ({
        action: () => {
          if (symbol) {
            sendLink.onSymbolClick(symbol);
            session.getManager(TrackingManager).trackTickerEvent('click', { source: 'watchlist', symbol });
          }
        },
        name: `Open Details for <strong>${symbol}</strong>`,
      }),
      removeSelectionFromWatchlist: {
        action: () => {
          if (!gridApi.current) {
            return;
          }

          const selections = gridApi.current.getCellRanges();
          const symbolsArr: string[] = [];
          gridApi.current.forEachNodeAfterFilterAndSort(node => {
            symbolsArr.push(node.id ?? '');
          });

          selections?.forEach(selection => {
            let start = selection.startRow?.rowIndex ?? 0;
            let end = selection.endRow?.rowIndex ?? 0;
            if (start > end) {
              [start, end] = [end, start];
            }

            const symbolsToRemove = symbolsArr.slice(start, end + 1);

            removeSymbolsFromList(symbolsToRemove);
          });
          deselectAll();
        },
        name: 'Remove Selection from This Watchlist',
      },
      removeSymbolFromList: (symbol: StockSymbol) => ({
        action: () => {
          const nodeToRemove = gridApi.current?.getRowNode(symbol);
          if (nodeToRemove) {
            //cb func to remove symbol through manager
            removeSymbolsFromList([symbol]);
            //updating grid
            gridApi.current?.applyTransaction({
              remove: [nodeToRemove.data],
            });
          }
        },
        name: `Remove <strong>${symbol}</strong> from This Watchlist`,
      }),
    }),
    [deselectAll, removeSymbolsFromList, sendLink, session],
  );

  const getAdditionalContextMenuItems = React.useCallback(
    (params: GetContextMenuItemsParams) => {
      if (!params.node) {
        return [];
      }
      const symbol = params.node.id;
      const selectionGroupRows = params.api?.getSelectedNodes();
      if ((params.api?.getCellRanges?.()?.length ?? 0) === 1 && selectionGroupRows.includes(params.node)) {
        const columnName = params.column?.getColId();
        selectionGroupRows.forEach(selectedRow => {
          if (params.node?.rowIndex !== selectedRow.rowIndex) {
            params.api?.addCellRange({
              columnEnd: columnName,
              columnStart: columnName,
              rowEndIndex: selectedRow.rowIndex,
              rowStartIndex: selectedRow.rowIndex,
            });
          }
        });
      }
      const selectionGroups = params.api?.getCellRanges();

      const hasMultipleRows = (selectionGroup: CellRange) =>
        selectionGroup.startRow?.rowIndex !== selectionGroup.endRow?.rowIndex;

      const oneGroupMultipleRows = selectionGroups?.length === 1 && hasMultipleRows(selectionGroups[0]);
      const hasRangeSelections = (selectionGroups?.length ?? 0) > 1 || oneGroupMultipleRows;

      const remove = hasRangeSelections
        ? customMenuItems.removeSelectionFromWatchlist
        : symbol && customMenuItems.removeSymbolFromList(symbol);

      const additionalMenuItems = [] as MenuItemDef[];

      if (symbol) {
        additionalMenuItems.push(customMenuItems.openSymbolDetailsWidgetFor(symbol));
      }
      if (remove) {
        additionalMenuItems.push(remove);
      }

      return additionalMenuItems;
    },
    [customMenuItems],
  );

  const selectSymbol = React.useCallback(
    (symbol: StockSymbol) => {
      sendLink.onSymbolClick(symbol);

      session.getManager(TrackingManager).trackTickerEvent('click', { source: 'watchlist', symbol });
    },
    [sendLink, session],
  );
  const onKeyPressedSymbol = React.useCallback(
    (e: CellKeyDownEvent) => {
      if (e.event) {
        const keyboardEvent = e.event as KeyboardEvent;
        switch (keyboardEvent.key) {
          case 'Spacebar':
          case ' ':
          case 'Enter':
            selectSymbol(e.data.symbol);
            break;
          default:
            break;
        }
      }
    },
    [selectSymbol],
  );
  const onKeyPressed = React.useCallback<Required<GridOptions>['onCellKeyDown']>(
    (e: CellKeyDownEvent) => {
      switch (e.column.getColId()) {
        case 'symbol':
          onKeyPressedSymbol(e);
          break;
        default:
          break;
      }
    },
    [onKeyPressedSymbol],
  );
  const autoselectTickerRef = React.useRef<{ autoselectEnabled: boolean; autoselectTimer: NodeJS.Timeout | null }>({
    autoselectEnabled: props.autoselectTicker,
    autoselectTimer: null,
  });
  autoselectTickerRef.current.autoselectEnabled = props.autoselectTicker;
  const onCellFocused = React.useCallback(
    (e: CellFocusedEvent) => {
      if (
        typeof e.column === 'string' ||
        !autoselectTickerRef.current.autoselectEnabled ||
        e.column?.getColId() !== 'symbol'
      ) {
        return;
      }
      const focusedCell = e.api.getFocusedCell();
      const row = focusedCell && e.api.getDisplayedRowAtIndex(focusedCell.rowIndex);
      const cellValue = row && e.api.getValue(focusedCell.column, row);
      if (!cellValue) {
        return;
      }
      if (autoselectTickerRef.current.autoselectTimer) {
        clearTimeout(autoselectTickerRef.current.autoselectTimer);
      }
      autoselectTickerRef.current.autoselectTimer = setTimeout(() => {
        selectSymbol(cellValue);
      }, 500);
    },
    [selectSymbol],
  );

  const setSavedGridParameters = React.useCallback(() => {
    if (gridApi.current && hasLength(gridLayout.columns)) {
      const { columns, filter } = gridLayout;

      gridApi.current.setFilterModel(filter);
      gridApi.current.applyColumnState({
        applyOrder: true,
        state: columns,
      });
    }
  }, [gridLayout]);

  React.useImperativeHandle(forwardedRef, () => ({
    exportToCSV: exportToCSVHelper,
    setSavedGridParameters,
  }));

  const gridOptions: GridOptions = React.useMemo<GridOptions>(
    () => ({
      asyncTransactionWaitMillis: 100,
      enableRangeSelection: true,
      getRowId: props => props.data.symbol,
      headerHeight: 30,
      onCellFocused: onCellFocused,
      onCellKeyPress: onKeyPressed,
      onGridReady: onGridReady,
      rowHeight: 20,
      sideBar: globalGridOptions.sideBar,
      statusBar: globalGridOptions.statusBar,
      useValueFormatterForExport: true,
    }),
    [onKeyPressed, onCellFocused, onGridReady],
  );

  const overlay = React.useMemo(
    () => ({
      noData: (
        <div className="NoResults NoResults-simple">
          <div className="NoResults-headerIcon">
            <WatchlistIcon />
          </div>
          <div className="title">This watchlist is empty.</div>
        </div>
      ),
    }),
    [],
  );

  return (
    <BenzingaGrid
      columnDefs={watchlistColumnDefs}
      defaultColDef={state.defaultColDef}
      defaultColGroupDef={state.defaultColGroupDef}
      exportParams={exportParams}
      getAdditionalContextMenuItems={getAdditionalContextMenuItems}
      gridLayout={gridLayout}
      gridOptions={gridOptions}
      onGridLayoutChanged={onGridLayoutChanged}
      onSymbolClick={sendLink.onSymbolClick}
      overlays={overlay}
      rowClass="TUTORIAL_ag_header_Watchlist"
      sizeColumnsToFit={true}
      suppressRowTransform
      symbolColIDs={['symbol']}
    />
  );
});
