'use client';
import React from 'react';
import { SessionContext } from '@benzinga/session-context';
import { IframeWidgetWithOptionalSingleSearch } from '@benzinga/pro-utils-widget';

import { WidgetManifest, useWidgetParameters } from '@benzinga/widget-tools';
import { ThemeContext } from '@benzinga/themetron';
import { UplegWidgetIteration, UplegWidgetAllIterations } from './entities';
import { useAutoCompleteSymbols } from '@benzinga/pro-ui';
import { createSymbolSearchItem } from '@benzinga/search-modules';
import { uplegWidgetMigrator } from './migrate';
import { useIsUserEditor, usePermission } from '@benzinga/user-context';
import { ReactComponent as SensaLogo } from './sensaMarket.svg';

import { useQuery } from '@tanstack/react-query';
import { safeJsonFetch } from '@benzinga/safe-await';
import { ShopManager } from '@benzinga/shop-manager';
import { StockSymbol } from '@benzinga/session';

export const ResearchURL = 'https://www.sensamarket.com/benzinga/option-builder/';

const SensaMarketWidget: React.FC = () => {
  const theme = React.useContext(ThemeContext);

  const paying_user = usePermission('bzpro/widget/upleg', '#');
  const logged_in_user = useIsUserEditor();

  const token = useQuery({
    queryFn: async () => {
      const result = await safeJsonFetch<{ external_token: string }>(
        'https://accounts.benzinga.com/api/v1/account/external_token/upleg',
        {
          credentials: 'include',
        },
      );
      return result.ok?.external_token ?? '';
    },
    queryKey: ['fetch-external-token'],
  });

  const session = React.useContext(SessionContext);
  const product = useQuery({
    queryFn: async () => {
      const result = await session.getManager(ShopManager).getProduct('upleg');
      return result.ok;
    },
    queryKey: ['fetch-package'],
  });

  const params = useWidgetParameters(SensaMarketWidgetManifest);
  const symbols = useAutoCompleteSymbols(
    React.useMemo(
      () => (params.parameters.autocomplete ? [params.parameters.autocomplete] : []),
      [params.parameters.autocomplete],
    ),
  );

  const firstSymbol = React.useRef<undefined | StockSymbol>(undefined);
  if (firstSymbol.current === undefined) {
    firstSymbol.current = symbols?.[0];
  }
  const checkoutPage = product.data?.packages.find(p => p.checkoutUrl)?.checkoutUrl;

  const iframe = React.useRef<HTMLIFrameElement>(null);
  const url = React.useMemo(() => {
    const url = new URL(ResearchURL);
    url.searchParams.set('ref', 'pro');
    url.searchParams.set('theme', theme.name === 'light' ? 'light' : 'dark');
    url.searchParams.set('theme_name', theme.name);
    url.searchParams.set('paying_user', paying_user.toString());
    url.searchParams.set('logged_in_user', logged_in_user.toString());
    url.searchParams.set('token', token.data ?? '');
    url.searchParams.set('q', firstSymbol.current ?? '');
    return url;
  }, [logged_in_user, paying_user, theme.name, token.data]);

  const widgetParams = useWidgetParameters(SensaMarketWidgetManifest);

  const title = React.useMemo(() => SensaMarketWidgetManifest.name, []);

  return token.isLoading ? (
    <div>Loading...</div>
  ) : (
    <IframeWidgetWithOptionalSingleSearch
      checkoutPage={checkoutPage}
      parameters={widgetParams.parameters}
      ref={iframe}
      setParameters={widgetParams.setParameters}
      title={title}
      url={url}
    />
  );
};

export const SensaMarketWidgetManifest: WidgetManifest<'sensa-market', UplegWidgetIteration, UplegWidgetAllIterations> =
  {
    WidgetRender: SensaMarketWidget,
    defaultGlobalParameters: {},
    defaultWidgetParameters: {
      autocomplete: createSymbolSearchItem('AAPL'),
      flightMode: false,
    },
    description: 'Sensa Market Widget',
    icon: SensaLogo,
    id: 'sensa-market',
    menuItem: true,
    migrator: uplegWidgetMigrator,
    name: 'Sensa Market',
    state: 'production',
    version: 2,
  };
