'use client';
import React from 'react';
import { AxisType, BzWaterfallData, DataTypes } from './entity';
import { ThemeContext } from '@benzinga/themetron';
import { AxisName, Plotly } from 'plotly.js';

export const useBzLayout = (data: DataTypes[], axes: AxisType[]): Partial<Plotly.Layout> => {
  const theme = React.useContext(ThemeContext);
  const doesWaterfallExists = React.useMemo(() => data.find(d => d.type === 'waterfall'), [data]);
  const doesTreeMapExists = React.useMemo(() => data.some(d => d.type === 'heatmap'), [data]);
  const backgroundColor = theme.colors.background;
  const foregroundColor = theme.colors.foreground;

  const generateShape = React.useCallback(
    (startY: number, endY: number, sectionTitle: string, backgroundIndex: number): Partial<Plotly.Shape> => ({
      fillcolor: backgroundIndex % 2 === 0 ? theme.colors.backgroundInactive : theme.colors.backgroundActive,

      label: {
        font: { color: theme.colors.foregroundInactive, size: 10 },
        padding: 12,
        text: `<b><i>${sectionTitle}</i></b>`,
        textposition: 'top right',
      },
      layer: 'below',

      line: {
        width: 0,
      },
      opacity: 0.6,
      type: 'rect',
      x0: -1,
      x1: 1,
      xref: 'paper',
      y0: startY,
      y1: endY,
      yref: 'paper',
    }),
    [theme.colors.backgroundActive, theme.colors.backgroundInactive, theme.colors.foregroundInactive],
  );

  const ChartBasedLayoutConfig = React.useCallback(
    (layout: Partial<Plotly.Layout>) => {
      let layoutConfig = { ...layout };
      if (doesWaterfallExists) {
        layoutConfig = {
          ...layoutConfig,
          barmode: 'stack',
          margin: { b: 50, l: 200, r: 10, t: 30 },
          shapes:
            (doesWaterfallExists as BzWaterfallData).financialMode === 'cashFlowStatement'
              ? [
                  generateShape(0.62, 1, 'Total Operating Cash Flow', 0),
                  generateShape(0.38, 0.62, 'Total Investing Cash Flow', 1),
                  generateShape(0.15, 0.38, 'Total Financing Cash Flow', 2),
                ]
              : (doesWaterfallExists as BzWaterfallData).financialMode === 'balanceSheetStatement'
                ? [generateShape(0.5, 1, 'Total Assets', 0), generateShape(0, 0.45, 'Total Liabilities', 1)]
                : [],
        };
      }
      if (doesTreeMapExists) {
        layoutConfig = {
          ...layoutConfig,
          margin: { b: 10, l: 10, r: 10, t: 30 },
        };
      }
      return layoutConfig;
    },
    [doesTreeMapExists, doesWaterfallExists, generateShape],
  );

  const layout: Partial<Plotly.Layout> = React.useMemo(() => {
    const axisMapping = axes.reduce<{ xAxis: string[]; yAxis: string[] }>(
      (acc, axis) => {
        if (axis.side === 'bottom' || axis.side === 'top') {
          acc.xAxis.push(axis.uuid);
        } else {
          acc.yAxis.push(axis.uuid);
        }
        return acc;
      },
      { xAxis: [], yAxis: [] },
    );
    const axisMap = new Map<string, AxisName>([
      ...axisMapping.xAxis.map((uuid, index) => [uuid, index === 0 ? `x` : `x${index + 1}`] as [string, AxisName]),
      ...axisMapping.yAxis.map((uuid, index) => [uuid, index === 0 ? `y` : `y${index + 1}`] as [string, AxisName]),
    ]);

    const generatedLayout: Partial<Plotly.Layout> = axes.reduce((acc, axis) => {
      const updatedAxis: Plotly.Layout['xaxis'] | Plotly.Layout['yaxis'] = {
        anchor: axis.overlaying === 'free' ? 'free' : axisMap.get(axis.overlaying ?? axis.uuid) ?? 'free',
        autorange: true,
        domain: axis.domain ? axis.domain.map(d => d / 100) : [0, 1],
        overlaying: axis.overlaying === 'free' ? 'free' : axisMap.get(axis.overlaying) ?? 'free',
        position: axis.anchor === 'free' ? axis.position : undefined,
        rangeslider: { visible: axis.showRangeSlider !== undefined ? axis.showRangeSlider : false },
        side: axis.side,
        ticksuffix: '   ',
        title: doesWaterfallExists ? '' : axis.title,
      };
      const currentAxis = axisMap.get(axis.uuid);

      const axisName = currentAxis
        ? currentAxis?.includes('x')
          ? `xaxis${currentAxis.length > 2 ? currentAxis.substring(1, 2) : currentAxis.charAt(1)}`
          : `yaxis${currentAxis.length > 2 ? currentAxis.substring(1, 2) : currentAxis.charAt(1)}`
        : 'axis';

      return {
        ...acc,
        [axisName]: updatedAxis,
      };
    }, {});
    return ChartBasedLayoutConfig(generatedLayout);
  }, [ChartBasedLayoutConfig, axes, doesWaterfallExists]);

  return React.useMemo(
    () => ({
      autosize: true,
      font: { color: foregroundColor },
      legend: {
        bgcolor: 'transparent',
      },
      paper_bgcolor: backgroundColor,
      plot_bgcolor: backgroundColor,
      uirevision: 'true',
      ...layout,
      margin: layout.margin ?? { b: 50, l: 80, r: 80, t: 50 },
    }),
    [backgroundColor, foregroundColor, layout],
  );
};
