'use client';
import { useAutoCompleteSymbols } from '@benzinga/pro-ui';
import { getColorArray } from './dataTypes/utils';
import { IncomeStatements } from './dataTypes/sankey/incomeStatement';
import { ThemeContext } from '@benzinga/themetron';
import React from 'react';
import { BzPlotlyArgs } from './BzPlotly';
import { CashFlow } from './dataTypes/sankey/cashFlow';
import { BalanceSheetStatements } from './dataTypes/sankey/balanceSheet';
import { IncomeStatementWaterfall } from './dataTypes/waterfall/IncomeStatementWaterfall';
import { HQ, VisualizationArgs, getColor, getMinMax } from '@benzinga/visualization-utils';
import {
  Bz2DHeatmapData,
  BzBarData,
  BzBubbleData,
  BzHeatmapData,
  BzLineData,
  BzPieData,
  BzSankeyData,
  BzScatterData,
  BzWaterfallData,
} from './entity';
import { CashFlowWaterfall } from './dataTypes/waterfall/CashFlowWaterfall';
import { BalanceSheetWaterfall } from './dataTypes/waterfall/BalanceSheetWaterfall';
import { ColorScale, Plotly } from 'plotly.js';
import { capitalize } from '@benzinga/utils';
import { useGetFields } from './useGetFields';
import { HoldingsQuote } from '@benzinga/quotes-v3-holdings-manager';
import { useFinancials } from './hooks/useFinancials';
import { useBatchQuotesCallback } from '@benzinga/quotes-v3-manager-hooks';
import { Quote } from '@benzinga/quotes-v3-manager';

export const useBzPlots = (props: VisualizationArgs<BzPlotlyArgs>) => {
  const symbols = useAutoCompleteSymbols(props.sources);

  const fields = useGetFields(props.args.traces);
  const [quotes, setQuotes] = React.useState<HoldingsQuote[] | undefined>();
  const handleQuotesUpdate = React.useCallback((newQuotes: HoldingsQuote[]) => {
    if (newQuotes?.length) {
      setQuotes(newQuotes);
    }
  }, []);

  useBatchQuotesCallback(symbols ?? ['AAPL'], fields as Set<keyof Quote>, handleQuotesUpdate);

  const theme = React.useContext(ThemeContext);

  const defaultColorScale = React.useMemo<ColorScale>(
    () => [
      [0, theme.colors.statistic.negative],
      [0.5, theme.colors.foreground],
      [1, theme.colors.statistic.positive],
    ],
    [theme],
  );

  // Get financials
  const { balanceSheetStatements, cashFlowStatements, incomeStatements } = useFinancials(
    React.useMemo(() => symbols ?? [], [symbols]),
    props.args.traces,
  );

  const data = React.useMemo(
    () =>
      props.args.traces.flatMap<Plotly.Data>(
        (d): (Plotly.Data & { toolbarValues?: number | HQ[]; label?: string[] })[] => {
          switch (d.type) {
            case 'heatmap': {
              const heatmapType = d as BzHeatmapData;

              const colorBy = heatmapType.byColor;
              const { max, min } = colorBy ? getMinMax(quotes ?? [], colorBy) : { max: 0, min: 0 };

              const groupBy = heatmapType.byGroup;
              const groups = groupBy
                ? new Set(quotes?.map(quote => quote[groupBy] ?? `Other ${capitalize(groupBy)}`))
                : new Set<string>();
              const emptyDef: { colors: string[]; labels: string[]; parents: string[]; values: number[] } = {
                colors: [],
                labels: [],
                parents: [],
                values: [],
              };
              const treeMapData = Array.from(groups).reduce(
                (acc, group) => {
                  if (group) {
                    acc.labels.push(group);
                    acc.parents.push('');
                    acc.values.push(0);
                    if (colorBy) {
                      acc.colors.push(theme.colors.backgroundInactive);
                    }
                  }
                  return acc;
                },
                { ...emptyDef },
              );

              const items = quotes?.reduce((acc, quote) => {
                const value = quote[heatmapType.byValue ?? 'marketCap'] ?? 0;

                if (groupBy) {
                  const groupValue = quote[groupBy] ?? `Other ${capitalize(groupBy)}`;
                  acc.parents.push(groupValue as string);
                } else {
                  acc.parents.push('');
                }

                if (colorBy) {
                  const indicator = heatmapType.byColor ?? ('percentChange' as HQ);
                  acc.colors.push(getColor(quote, indicator, min, max));
                }
                acc.labels.push(quote.symbol);
                acc.values.push(value as number);
                return acc;
              }, treeMapData);

              return [
                {
                  ...items,
                  customdata: symbols,
                  hoverinfo: 'none',
                  marker: colorBy ? { colors: (items && items.colors) ?? [] } : {},
                  toolbarValues: [heatmapType.byValue, colorBy, groupBy].filter(Boolean) as number | HQ[],
                  type: 'treemap',
                },
              ];
            }
            case 'waterfall': {
              const waterfallType = d as BzWaterfallData;
              switch (waterfallType.financialMode) {
                case 'balanceSheetStatement':
                  return BalanceSheetWaterfall(balanceSheetStatements, waterfallType.colorMode);
                case 'cashFlowStatement':
                  return CashFlowWaterfall(cashFlowStatements, waterfallType.colorMode);
                default:
                  return IncomeStatementWaterfall(incomeStatements, waterfallType.colorMode);
              }
            }
            case 'sankey': {
              const sankeyType = d as BzSankeyData;
              switch (sankeyType.sankeyFinancial) {
                case 'cashFlowStatement':
                  return [CashFlow(cashFlowStatements, theme)];
                case 'balanceSheetStatement':
                  return [BalanceSheetStatements(balanceSheetStatements, theme)];
                default:
                  return [IncomeStatements(incomeStatements, theme)];
              }
            }

            // case 'histogram':
            // case 'histogram2dcontour':
            case 'scatter': {
              const plotType = d as BzScatterData;
              const color = plotType.byColor ?? 'currentPrice';
              const { max, min } = getMinMax(quotes ?? [], color);
              const colorsArray = getColorArray(quotes ?? [], color, max, min);

              const xy = quotes
                ?.map(q => ({
                  x: plotType.x && q[plotType.x ?? ('currentPrice' as HQ)],
                  y: plotType.y && q[plotType.y ?? ('currentPrice' as HQ)],
                }))
                .filter(v => Boolean(v.x) && Boolean(v.y))
                .reduce<{ x: string[]; y: string[] }>(
                  (acc, val) => {
                    acc.x.push(val.x as string);
                    acc.y.push(val.y as string);
                    return acc;
                  },
                  { x: [], y: [] },
                ) ?? { x: [], y: [] };

              return [
                {
                  hoverinfo: 'none',
                  marker: { color: colorsArray },
                  mode: 'markers',
                  text: symbols,
                  toolbarValues: [plotType.x, plotType.y, plotType.byColor].filter(Boolean),
                  type: 'scatter',
                  xaxis: d.xaxis,
                  yaxis: d.yaxis,
                  ...xy,
                },
              ];
            }
            case 'bar': {
              const barType = d as BzBarData;
              const byColor = barType.byColor;

              const value = barType.byValue ?? 'currentPrice';
              const y = quotes?.map<number>(q => (q[value] as number) ?? 0) ?? [];

              let colorsArray: string[] | undefined = undefined;
              if (byColor) {
                const { max, min } = getMinMax(quotes ?? [], byColor);
                colorsArray = getColorArray(quotes ?? [], byColor, max, min);
              }
              return [
                {
                  customdata: symbols,
                  hoverinfo: 'none',
                  label: symbols,
                  marker: colorsArray ? { color: colorsArray } : {},
                  toolbarValues: [value, barType.byColor].filter(Boolean) as number | HQ[],
                  type: 'bar',
                  x: symbols,
                  xaxis: 'x12',
                  y,
                },
              ];
            }
            case 'bubble': {
              const plotType = d as BzBubbleData;
              const indicator = plotType.byColor ?? 'currentPrice';
              const { max, min } = getMinMax(quotes ?? [], indicator);
              const colorsArray = getColorArray(quotes ?? [], indicator, max, min);
              const { max: maxSize, min: minSize } = getMinMax(quotes ?? [], plotType.bySize ?? 'bidSize');
              const size = quotes?.map(q => {
                const value = q[plotType.bySize || 'bidSize'] as number;
                return minSize === maxSize ? value / 10 : (((value ?? 0) - minSize) / (maxSize - minSize)) * 95 + 10;
              });

              const xy = quotes
                ?.map(q => ({
                  x: plotType.x && q[plotType.x ?? ('currentPrice' as HQ)],
                  y: plotType.y && q[plotType.y ?? ('currentPrice' as HQ)],
                }))
                .filter(v => Boolean(v.x) && Boolean(v.y))
                .reduce<{ x: string[]; y: string[] }>(
                  (acc, val) => {
                    acc.x.push(val.x as string);
                    acc.y.push(val.y as string);
                    return acc;
                  },
                  { x: [], y: [] },
                ) ?? { x: [], y: [] };

              return [
                {
                  hoverinfo: 'none',
                  marker: { color: colorsArray, size },
                  mode: 'markers',
                  text: symbols,
                  toolbarValues: [plotType.x, plotType.y, plotType.byColor, plotType.bySize].filter(Boolean),
                  type: 'scatter',
                  xaxis: d.xaxis,
                  yaxis: d.yaxis,
                  ...xy,
                },
              ];
            }
            case 'line': {
              const plotType = d as BzLineData;
              const indicator = plotType.byColor ?? ('currentPrice' as HQ);
              const { max, min } = getMinMax(quotes ?? [], indicator);
              const colorsArray = getColorArray(quotes ?? [], indicator, max, min);

              const xy = quotes
                ?.map(q => ({
                  x: plotType.x && q[plotType.x ?? ('currentPrice' as HQ)],
                  y: plotType.y && q[plotType.y ?? ('currentPrice' as HQ)],
                }))
                .filter(v => Boolean(v.x) && Boolean(v.y))
                .reduce<{ x: string[]; y: string[] }>(
                  (acc, val) => {
                    acc.x.push(val.x as string);
                    acc.y.push(val.y as string);
                    return acc;
                  },
                  { x: [], y: [] },
                ) ?? { x: [], y: [] };

              return [
                {
                  hoverinfo: 'none',
                  marker: { color: colorsArray },
                  mode: 'lines+markers',
                  text: symbols,
                  toolbarValues: [plotType.x, plotType.y, indicator].filter(Boolean),
                  type: 'scatter',
                  xaxis: d.xaxis,
                  yaxis: d.yaxis,
                  ...xy,
                },
              ];
            }

            case 'heatmap2d': {
              const plotType = d as Bz2DHeatmapData;
              const x: number[] = (quotes?.map(q => q[plotType.x ?? 'currentPrice']) as number[]) ?? [];
              const y: number[] = (quotes?.map(q => q[plotType.y ?? 'currentPrice']) as number[]) ?? [];
              return [
                {
                  // autocolorscale: false,
                  colorscale: defaultColorScale,
                  hoverinfo: 'none',
                  text: symbols,
                  toolbarValues: [plotType.x, plotType.y].filter(Boolean),
                  type: 'heatmap',
                  x: symbols,
                  xaxis: 'x13',
                  y: symbols,
                  yaxis: 'y13',
                  z: x.map(xi => y.map(yi => xi - yi)),
                },
              ];
            }

            case 'pie': {
              const pieType = d as BzPieData;
              const indicator = pieType.byColor;
              let colorsArray: string[] | undefined = undefined;

              if (indicator) {
                const { max, min } = getMinMax(quotes ?? [], indicator);
                colorsArray = getColorArray(quotes ?? [], indicator, max, min);
              }
              return [
                {
                  hoverinfo: 'none',
                  labels: symbols,
                  marker: colorsArray ? { colors: colorsArray } : {},
                  text: symbols,
                  toolbarValues: [indicator, pieType.byValue].filter(Boolean) as number | HQ[],
                  type: 'pie',
                  values: quotes?.map(q => q[pieType.byValue ?? 'currentPrice'] as number),
                },
              ];
            }
            default:
              return [];
          }
        },
      ),
    [
      balanceSheetStatements,
      cashFlowStatements,
      defaultColorScale,
      incomeStatements,
      props.args.traces,
      quotes,
      symbols,
      theme,
    ],
  );

  return data;
};
