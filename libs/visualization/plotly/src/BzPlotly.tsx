'use client';
import React from 'react';
import Plot from 'react-plotly.js';
import { Datum, PlotData, PlotType, PlotHoverEvent } from 'plotly.js';
import {
  TickerHeaderModule,
  useAutoCompleteQuoteSubscription,
  ProTickerHover,
  SendLinkContext,
  HorizontalRuleModule,
  SparkLineModule,
} from '@benzinga/pro-ui';
import { createSymbolSearchItem } from '@benzinga/search-modules';
import styled from 'styled-components';
import { AxisType, BarModes, DataTypes } from './entity';
import { BaseTrace, HQ, VisualizationArgs, formatNumberWithSuffix } from '@benzinga/visualization-utils';
import { useBzPlots } from './useBzPlotly';
import { useBzLayout } from './useBzLayout';
import { camelCaseToTitleCase, debounce } from '@benzinga/utils';
import { StockSymbol } from '@benzinga/session';
import Hooks from '@benzinga/hooks';
import { useBzTraces } from './useBzTraces';

type BzPlotDatum = {
  data: PlotData;
  label?: string;
  fullData?: { text?: Array<string>; type: DataTypes['type'] };
  value?: number;
  source?: { label?: string };
  target?: { label?: string; value?: number };
  customdata: Datum | undefined;
  text?: string;
  x: Datum | undefined;
  y: Datum | undefined;
};

type BzHoverEvent = {
  event: PlotHoverEvent['event'];
  points: Array<BzPlotDatum>;
};

const config = {
  autosizable: true,
  displaylogo: false,
  responsive: true,
};

export interface BzPlotlyArgs {
  traces: (DataTypes & BaseTrace)[];
  layout: {
    axes: AxisType[];
    barmode?: BarModes;
  };
  global?: object;
}

type TooltipTypes =
  | {
      type: Exclude<PlotType, 'sankey' | 'waterfall'>;
      value: HQ[];
      label?: string;
      source?: never;
      target?: never;
    }
  | {
      type: 'sankey';
      source?: string;
      target?: string;
      value: number;
      label?: string;
    }
  | {
      type: 'waterfall';
      source: number;
      target: string;
      value?: never;
      label?: never;
    };

type Tooltip = TooltipTypes & {
  symbol?: string | string[];
  x?: Datum;
  y?: Datum;
};

const QuoteTooltip: React.FC<{ symbol: string; value: HQ[] }> = props => {
  const quotes = useAutoCompleteQuoteSubscription(
    [props.symbol].map(s => createSymbolSearchItem(s as StockSymbol)),
    (props as any).value ?? [],
  );
  const sendLink = React.useContext(SendLinkContext);
  return (
    !!quotes?.length && (
      <>
        <TickerHeaderModule onSymbolClick={sendLink.onSymbolClick} />
        <HorizontalRuleModule />
        <SparkLineModule />
        <HorizontalRuleModule />
        {quotes && Array.isArray(props.value) && (
          <QuoteDataValues>
            {quotes.map((item, index) => (
              <QuoteDataValues key={index}>
                {Object.keys(item)
                  .filter(i => (props.value as HQ[]).includes(i as HQ))
                  .map((key, innerIndex) => (
                    <TooltipValueWrapper key={innerIndex}>
                      {`${camelCaseToTitleCase(key)} : ${formatNumberWithSuffix(item[key], key)}`}
                    </TooltipValueWrapper>
                  ))}
              </QuoteDataValues>
            ))}
          </QuoteDataValues>
        )}
      </>
    )
  );
};
const TooltipModules: React.FC<Tooltip> = props => {
  const sendLink = React.useContext(SendLinkContext);

  return (
    <div>
      {(() => {
        switch (props.type) {
          case 'waterfall':
            return (
              props.symbol !== '' && (
                <>
                  <TickerHeaderModule onSymbolClick={sendLink.onSymbolClick} />
                  <HorizontalRuleModule />
                  <SparkLineModule />
                  <HorizontalRuleModule />
                  <TooltipValueWrapper>
                    <span>{props.target?.replace(/<[^>]+>/g, '')}</span> :{' '}
                    {typeof props.source === 'number' && <span>$ {formatNumberWithSuffix(props.source)}</span>}
                  </TooltipValueWrapper>
                </>
              )
            );
          case 'sankey':
            return props.source && props.target ? (
              <>
                <TickerHeaderModule onSymbolClick={sendLink.onSymbolClick} />
                <HorizontalRuleModule />
                <SparkLineModule />
                <HorizontalRuleModule />
                <TooltipValueWrapper>
                  <span>{props.source}</span> -
                  {typeof props.value === 'number' && (
                    <span>{formatNumberWithSuffix(props.value, props.label) ?? props.value}</span>
                  )}{' '}
                  -<span>{props.target}</span>
                </TooltipValueWrapper>
              </>
            ) : (
              <TooltipValueWrapper>
                <span>{props.label}</span> -
                {typeof props.value === 'number' && (
                  <span>{formatNumberWithSuffix(props.value, props.label) ?? props.value}</span>
                )}
              </TooltipValueWrapper>
            );

          default:
            return <QuoteTooltip symbol={props.symbol as string} value={props.value as HQ[]} />;
        }
      })()}
    </div>
  );
};

export const BzPlotly: React.FC<VisualizationArgs<BzPlotlyArgs>> = props => {
  const [showTooltip, setShowTooltip] = React.useState<boolean>(false);

  const [tooltip, setTooltip] = React.useState<Tooltip | undefined>(undefined);

  const SymbolHover = (e: BzHoverEvent) => {
    const type = ((e.points[0]?.data ?? e?.points[0]?.fullData)?.type as PlotType) ?? '';
    switch (type) {
      case 'scatter':
        return {
          symbol: e.points[0].text,
          type,
          value: (e.points[0].data as { toolbarValues?: HQ[] })?.toolbarValues ?? [],
        };
      case 'waterfall':
        return {
          symbol: e.points[0].data.text,
          type,
          value: (e.points[0].data as { toolbarValues?: HQ[] })?.toolbarValues ?? [],
        };

      case 'bar':
        return e.points[0].data.orientation === 'h'
          ? {
              source: e.points[0].x,
              symbol: (e.points[0].customdata as string) ?? (e.points[0].data.customdata as string),
              target: e.points[0].y,
              type: 'waterfall',
            }
          : {
              symbol: (e.points[0].customdata as string) ?? (e.points[0].data.customdata as string),
              type,
              value: (e.points[0].data as { toolbarValues?: HQ[] })?.toolbarValues ?? e.points[0].data.text,
            };

      case 'pie':
        return {
          symbol: e.points[0].text && e.points[0].text.split('<')[0],
          type,
          value: (e.points[0].data as { toolbarValues?: HQ[] })?.toolbarValues ?? [],
        };
      case 'treemap':
        return {
          symbol: e.points[0].label as string,
          type,
          value: (e.points[0].data as { toolbarValues?: HQ[] })?.toolbarValues ?? [],
        };
      case 'heatmap':
        return {
          symbol:
            e.points[0].x === e.points[0].y ? (e.points[0].x as string) : ([e.points[0].x, e.points[0].y] as string[]),
          type,
          value: (e.points[0].data as { toolbarValues?: HQ[] })?.toolbarValues ?? [],
        };
      case 'sankey':
        if (e?.points[0]?.source && e?.points[0]?.target) {
          return {
            source: e.points[0]?.source?.label ?? '',
            symbol: e.points[0]?.customdata ?? '',
            target: e.points[0]?.target?.label ?? '',
            type: 'sankey',
            value: e.points[0]?.value ?? 0,
          } as Tooltip;
        } else {
          return {
            label: e.points[0]?.label ?? '',
            symbol: e.points[0]?.customdata ?? '',
            type: 'sankey',
            value: e.points[0]?.value ?? 0,
          } as Tooltip;
        }
      default:
        return {
          symbol: `No Symbol Found`,
          type,
          value: (e.points[0].data as { toolbarValues?: HQ[] })?.toolbarValues ?? [],
        };
    }
  };

  const data = useBzPlots(props);
  const isTreemMapStockSymbol = React.useCallback(
    (symbol: string) => data.some(d => d.type === 'treemap' && (d.customdata as Datum[])?.includes(symbol)),
    [data],
  );
  const TooltipRender: React.FC<Tooltip> = React.useCallback(
    props => {
      // If tooltip.symbol is a string and type is treemap
      if (typeof tooltip?.symbol === 'string' && tooltip?.type === 'treemap') {
        // Check if it's a valid stock symbol using isStockSymbol function
        if (!isTreemMapStockSymbol(tooltip.symbol)) return <></>; // If not a valid stock symbol, return empty fragment
      }

      // Render ProTickerHover components based on symbol type
      return Array.isArray(tooltip?.symbol) ? (
        // If symbol is an array, render multiple ProTickerHover components
        <>
          {tooltip.symbol.map(symbol => (
            <ProTickerHover
              key={symbol}
              modules={<TooltipModules {...props} symbol={symbol ?? 'No Symbol Found.'} />}
              symbol={symbol ?? 'No Symbol Found.'}
            />
          ))}
        </>
      ) : (
        // If symbol is a string or invalid treemap type, render single ProTickerHover component
        <ProTickerHover
          modules={<TooltipModules {...props} symbol={tooltip?.symbol ?? 'No Symbol Found.'} />}
          symbol={tooltip?.symbol ?? 'No Symbol Found.'}
        />
      );
    },
    [isTreemMapStockSymbol, tooltip?.symbol, tooltip?.type],
  );

  const plotRef = React.useRef<HTMLDivElement | null>(null);
  const layout = useBzLayout(props.args.traces, props.args.layout.axes ?? []);
  const sendLink = React.useContext(SendLinkContext);
  const plotDimensions = Hooks.useRefElementSize(plotRef);
  const getToolTipCoordinates = React.useCallback(
    (e: BzHoverEvent) => {
      const plotWidth = plotDimensions.width || 0;
      const plotHeight = plotDimensions.height || 0;
      const tooltipWidth = 220;
      const tooltipHeight = 200;

      let tooltipX = e.event.offsetX;
      let tooltipY = e.event.offsetY;

      // Check if there's enough space to position the tooltip to the right
      tooltipX + tooltipWidth <= plotWidth ? (tooltipX += 10) : (tooltipX -= tooltipWidth + 10);

      // Check if there's enough space to position the tooltip below
      tooltipY + tooltipHeight <= plotHeight ? (tooltipY += 10) : (tooltipY -= tooltipHeight + 10);

      return { x: tooltipX, y: tooltipY };
    },
    [plotDimensions],
  );

  const debouncedHover = debounce((e: BzHoverEvent) => {
    const value = SymbolHover(e);
    setShowTooltip(true);
    setTooltip(
      prevState =>
        ({
          ...prevState,
          ...value,
          ...getToolTipCoordinates(e),
        }) as Tooltip,
    );
  }, 200);
  const debouncedUnhover = debounce(() => {
    setShowTooltip(false);
    setTooltip(undefined);
  }, 200);

  return (
    <PlotWrapper>
      {showTooltip && tooltip && (
        <div
          style={{
            left: tooltip?.x + 'px',
            position: 'absolute',
            top: tooltip?.y + 'px',
            zIndex: 1,
          }}
        >
          <TooltipRender {...tooltip} />
        </div>
      )}

      <div
        ref={plotRef}
        style={{
          height: '100%',
          width: '100%',
        }}
      >
        <Plot
          config={config}
          data={useBzTraces(data, props.args.layout.axes ?? [])}
          layout={layout}
          onClick={e => {
            const symbolConfig = SymbolHover(e);
            if (SymbolHover(e).type === 'treemap' && typeof symbolConfig.symbol === 'string')
              return isTreemMapStockSymbol(symbolConfig.symbol) && sendLink.onSymbolClick(symbolConfig.symbol);
            typeof symbolConfig.symbol === 'string' && sendLink.onSymbolClick(symbolConfig.symbol);
          }}
          onHover={(e: BzHoverEvent) => debouncedHover(e)}
          onUnhover={() => debouncedUnhover()}
          style={{
            height: '100%',
            width: '100%',
          }}
        />
      </div>
    </PlotWrapper>
  );
};

const PlotWrapper = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
`;

const TooltipValueWrapper = styled.div`
  padding: 6px;
  display: flex;
  gap: 6px;
  font-size: 15px;
`;

const QuoteDataValues = styled.div`
  padding: 4px;
  padding-left: 8px;
`;
