import React from 'react';
import { AxisType } from './entity';
import { AxisName, Plotly } from 'plotly.js';

export const useBzTraces = (data: Plotly.Data[], axes: AxisType[]) => {
  const axisMapping = axes.reduce<{ xAxis: string[]; yAxis: string[] }>(
    (acc, axis) => {
      if (axis.side === 'bottom' || axis.side === 'top') {
        acc.xAxis.push(axis.uuid);
      } else {
        acc.yAxis.push(axis.uuid);
      }
      return acc;
    },
    { xAxis: [], yAxis: [] },
  );
  const axisMap = React.useMemo(
    () =>
      new Map<string, AxisName>([
        ...axisMapping.xAxis.map((uuid, index) => [uuid, index === 0 ? `x` : `x${index + 1}`] as [string, AxisName]),
        ...axisMapping.yAxis.map((uuid, index) => [uuid, index === 0 ? `y` : `y${index + 1}`] as [string, AxisName]),
      ]),
    [axisMapping.xAxis, axisMapping.yAxis],
  );

  return React.useMemo(
    () =>
      data.map(trace => {
        if (trace.type === ('scatter' || 'line' || 'bubble')) {
          return {
            ...trace,
            xaxis: axisMap.get(trace.xaxis ?? 'x') ?? 'x',
            yaxis: axisMap.get(trace.yaxis ?? 'y') ?? 'y',
          };
        }
        return trace;
      }),
    [axisMap, data],
  );
};
