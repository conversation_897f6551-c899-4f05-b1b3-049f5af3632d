'use client';
import React from 'react';
import ReactECharts from 'echarts-for-react';
import { HoldingsQuote } from '@benzinga/quotes-v3-holdings-manager';
import {
  HorizontalRuleModule,
  ProTickerHover,
  SendLinkContext,
  SparkLineModule,
  TickerHeaderModule,
  Sparkline,
  autoCompleteSymbols,
} from '@benzinga/pro-ui';
import styled from '@benzinga/themetron';
import { VisualizationArgs, getColor, getMinMax, formatNumberWithSuffix, HQ } from '@benzinga/visualization-utils';
import { HeatMapProps } from './entity';
import { StockSymbol, UUID } from '@benzinga/session';
import { camelCaseToTitleCase, isEmpty, toTitleCase } from '@benzinga/utils';
import Hooks from '@benzinga/hooks';
import ReactDOM from 'react-dom';
import { useConfig } from './hooks/useConfig';
import { useBatchQuotesCallback } from '@benzinga/quotes-v3-manager-hooks';
import { Quote } from '@benzinga/quotes-v3-manager';
import { SessionContext } from '@benzinga/session-context';
interface BaseTooltipProps {
  name: string;
  id: UUID;
  x?: number;
  y?: number;
}

export interface TooltipProps extends BaseTooltipProps {
  children?: BaseTooltipProps[];
}
interface TooltipValuesProps {
  quoteProperty: string;
  value: number;
}

const TooltipValues: React.FC<TooltipValuesProps> = React.memo(props => {
  return (
    <TooltipDataWrapper>
      {`${camelCaseToTitleCase(props.quoteProperty)} : ${formatNumberWithSuffix(props.value, props.quoteProperty)}`}
    </TooltipDataWrapper>
  );
});

const useFields = (traces: HeatMapArgs[], isSubscribed = true) => {
  const groupCategory = traces[0].groupBy === 'sector' ? 'gicsSectorName' : 'gicsIndustryName';

  return React.useMemo(() => {
    if (!isSubscribed) {
      return new Set<HQ>(['symbol', 'changePercent']);
    }
    const requiredFields = new Set<HQ>(['symbol', traces[0].color, traces[0].display, groupCategory]);

    return requiredFields;
  }, [groupCategory, isSubscribed, traces]);
};

const TooltipModules: React.FC<TooltipProps & HeatMapArgs & { quotes: HoldingsQuote[] }> = React.memo(props => {
  const sendLink = React.useContext(SendLinkContext);

  const currentQuote = React.useMemo(() => props.quotes.find(q => q.symbol === props.name), [props.quotes, props.name]);

  if (!currentQuote) return null;

  const displayValue = currentQuote[props.display];
  const colorValue = currentQuote[props.color];

  if (displayValue === undefined || colorValue === undefined) return null;

  return (
    <>
      <TickerHeaderModule onSymbolClick={sendLink.onSymbolClick} />
      <HorizontalRuleModule />
      <SparkLineModule />
      <HorizontalRuleModule />
      <TooltipValues quoteProperty={props.display} value={displayValue} />
      <TooltipValues quoteProperty={props.color} value={colorValue} />
    </>
  );
});

const GroupTooltip: React.FC<TooltipProps & HeatMapArgs & { limitGroupChildren: number; quotes: HoldingsQuote[] }> =
  React.memo(props => {
    const children = React.useMemo(
      () => props.children?.slice(0, props.limitGroupChildren),
      [props.children, props.limitGroupChildren],
    );

    if (!props.quotes?.length) return null;

    const QuoteDataRenderer: React.FC<{ display: string; color: string; symbol: StockSymbol }> = React.memo(
      ({ color, display, symbol }) => {
        const quote = React.useMemo(() => props.quotes.find(quote => quote.symbol === symbol), [symbol]);

        if (!quote) return null;

        const displayValue = quote[display];
        const colorValue = quote[color];

        if (displayValue === undefined || colorValue === undefined) return null;

        return (
          <QuoteDataWrapper>
            <DisplayText>{formatNumberWithSuffix(displayValue, display)}</DisplayText>
            <ColorText color={colorValue}>{formatNumberWithSuffix(colorValue, color)}</ColorText>
          </QuoteDataWrapper>
        );
      },
    );

    return (
      <ProTickerHover
        modules={
          <GroupTooltipWrapper>
            <GroupTitle>{props.name}</GroupTitle>
            <QuoteConfig>
              <Label>Value</Label>
              <Label>Color</Label>
            </QuoteConfig>
            {children?.map(child => (
              <ChildWrapper key={child.name}>
                <ChildName>{child.name}</ChildName>
                <SparklineWrapper>
                  <Sparkline height="compact" omitTextInfo symbol={child.name} />
                </SparklineWrapper>
                <QuoteDataWrapper>
                  <QuoteDataRenderer color={props.color} display={props.display} symbol={child.name} />
                </QuoteDataWrapper>
              </ChildWrapper>
            ))}
          </GroupTooltipWrapper>
        }
        symbol={props.name}
      />
    );
  });
const Tooltip: React.FC<TooltipProps & HeatMapArgs & { limitGroupChildren: number; quotes }> = React.memo(props => {
  if (props.children && props.children.length > 0) {
    return <GroupTooltip {...props} />;
  }
  return <ProTickerHover modules={<TooltipModules {...props} />} symbol={props.name} />;
});
export interface HeatMapArgs {
  display: keyof HoldingsQuote;
  color: keyof HoldingsQuote;
  groupBy: 'sector' | 'industry';
}

export const HeatMap: React.FC<VisualizationArgs<HeatMapProps>> = props => {
  const fields = useFields(props.args.traces, true);
  const [quotes, setQuotes] = React.useState<HoldingsQuote[] | undefined>();
  const session = React.useContext(SessionContext);

  const handleQuotesUpdate = React.useCallback((newQuotes: HoldingsQuote[] | undefined) => {
    setQuotes(newQuotes ?? []);
  }, []);

  const symbols = React.useMemo(() => {
    const symbolSet = autoCompleteSymbols(props.sources ?? [], session.getSession());
    return symbolSet ? Array.from(symbolSet) : undefined;
  }, [props.sources, session]);

  const [throttledQuotesUpdate] = Hooks.useThrottle(handleQuotesUpdate, 300);

  useBatchQuotesCallback(symbols ?? [], fields as Set<keyof Quote>, throttledQuotesUpdate);
  const sendLink = React.useContext(SendLinkContext);
  const echartsRef = React.useRef<ReactECharts>(null);

  const categorizedSymbolMap = React.useMemo(() => {
    if (!quotes) return {};

    return quotes.reduce((acc, quote) => {
      if (!quote) return acc;
      const groupCategory = props.args.traces[0].groupBy === 'sector' ? 'gicsSectorName' : 'gicsIndustryName';
      const key = quote[groupCategory ?? 'Other sectors'] ?? 'Other sectors';
      const displayValue = quote[props.args.traces[0].display];
      const colorValue = quote[props.args.traces[0].color];
      if (displayValue === undefined || colorValue === undefined) {
        return acc;
      }

      if (!acc[key]) {
        acc[key] = [];
      }

      acc[key].push({
        symbol: quote.symbol,
        [props.args.traces[0].display]: displayValue,
        [props.args.traces[0].color]: colorValue,
      });

      return acc;
    }, {});
  }, [quotes, props.args.traces]);

  const data = React.useMemo(() => {
    let globalMin = Infinity;
    let globalMax = -Infinity;

    const data = Object.entries(categorizedSymbolMap).map(([parentGroup, groupQuotes]: [any, any]) => {
      const { max, min } = getMinMax(groupQuotes ?? [], props.args.traces[0].color);
      const children = (groupQuotes ?? []).map(groupQuote => {
        const colorValue = groupQuote[props.args.traces[0].color];
        globalMin = Math.min(globalMin, isNaN(colorValue) ? 0 : colorValue);
        globalMax = Math.max(globalMax, isNaN(colorValue) ? 0 : colorValue);
        return {
          depth: 1,
          id: groupQuote.symbol,
          itemStyle: { color: getColor(groupQuote, props.args.traces[0].color, min, max) },
          name: groupQuote.symbol,
          symbol: groupQuote.symbol,
          value: [Math.abs(groupQuote[props.args.traces[0].display]) ?? 0, colorValue],
        };
      });
      return {
        children,
        id: isEmpty(parentGroup) ? `Other ${toTitleCase(props.args.traces[0].groupBy)}` : parentGroup,
        name: isEmpty(parentGroup) ? `Other ${toTitleCase(props.args.traces[0].groupBy)}` : parentGroup,
        value: children.reduce(
          (acc, child) => {
            acc[0] += isNaN(child.value[0]) ? 0 : child.value[0];
            acc[1] += isNaN(child.value[1]) ? 0 : child.value[1];
            return acc;
          },
          [0, 0],
        ),
      };
    });

    return { data, max: globalMax, min: globalMin };
  }, [categorizedSymbolMap, props.args.traces]);

  const onEvents = React.useMemo(
    () => ({
      click: event => {
        const isValidTicker = /^[A-Z]+$/.test(event.name);
        if (isValidTicker) {
          sendLink.onSymbolClick(event.name);
        } else if (echartsRef.current) {
          const chartInstance = echartsRef.current.getEchartsInstance();
          chartInstance.dispatchAction({
            targetNodeId: event.data.id,
            type: 'treemapRootToNode',
          });
        }
      },
    }),
    [sendLink],
  );

  const { limitGroupChildren, options, tooltip } = useConfig(props.args, symbols, data, echartsRef);

  const tooltipContainer = React.useMemo(() => {
    return tooltip ? document.getElementsByClassName(tooltip.id)?.[0] : undefined;
  }, [tooltip]);

  return data.data.length > 0 ? (
    <>
      {tooltipContainer &&
        ReactDOM.createPortal(
          <TooltipContainer>
            <Tooltip
              {...tooltip}
              color={props.args.traces[0].color}
              display={props.args.traces[0].display}
              groupBy={props.args.traces[0].groupBy}
              limitGroupChildren={limitGroupChildren}
              quotes={quotes}
            />
          </TooltipContainer>,
          tooltipContainer,
        )}
      <div style={{ height: '100%', width: '100% !important' }}>
        <ReactECharts
          onEvents={onEvents}
          option={options}
          ref={echartsRef}
          style={{ height: '100%', width: '100% !important' }}
        />
      </div>
    </>
  ) : (
    <Text>No data to display for the value selected. Please select another value</Text>
  );
};

const TooltipContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
`;

const Text = styled.div`
  text-align: center;
  align-items: center;
  padding: 100px;
  margin: auto;
  font-size: 30px;
`;

const TooltipDataWrapper = styled.div`
  padding: 6px;
  display: flex;
  font-weight: 500;
  gap: 6px;
  font-size: 14px;
  font-family: 'Nimbus Sans', helvetica, arial, sans-serif;
`;
const QuoteConfig = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  padding-right: 10px;
  font-weight: bold;
`;

const Label = styled.div`
  width: 60px;
  text-align: right;
  margin-left: 20px;
`;

const QuoteDataWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  width: 140px;
`;

const DisplayText = styled.div`
  width: 60px;
  text-align: right;
`;

const ColorText = styled.span<{ color: number }>`
  width: 60px;
  text-align: right;
  margin-left: 20px;
  color: ${props => (props.color < 0 ? props.theme.colors.statistic.negative : props.theme.colors.statistic.positive)};
`;

const ChildWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  font-size: 14px;
  min-width: 340px;
  max-width: max-content;
  div {
    border-bottom: none !important;
  }
`;

const ChildName = styled.div`
  flex: 0 0 50px;
  text-align: left;
`;

const SparklineWrapper = styled.div`
  width: 45%;
  margin: 0 10px;
`;

const GroupTitle = styled.div`
  padding: 10px;
  font-size: 16px;
  font-weight: 500;
  font-style: italic;
`;

const GroupTooltipWrapper = styled.div`
  border-radius: 5px;
`;
