import { Meta } from '@benzinga/advanced-news-manager';
import { NewsDisplayOutputTypes, TidParam } from '@benzinga/internal-news-manager';
export type { StoryObject } from '@benzinga/advanced-news-manager';

export interface ParselyPost {
  author: string;
  authors: string[];
  image_url: string;
  link: string;
  metadata: string;
  pub_date: string;
  section: string;
  tags: string[];
  thumb_url_medium: string;
  title: string;
  url: string;
}

export interface ParselyPostParams {
  limit?: number;
  days?: number;
  period_start?: string; // '1 day ago'
  section?: string;
  author?: string;
  tag?: string;
}

export interface ChartbeatPost {
  path: string;
  host: string;
  title: string;
  stats: any;
  sections: string[];
  authors: string[];
}

export interface ChartbeatParams {
  host?: string;
  limit?: number;
  type?: string;
  section?: string;
}

export interface BreakingNewsResponse {
  data: BreakingNews[];
  message?: string;
  status: string;
}

export interface BreakingNews {
  is_countdown?: string;
  link?: string;
  nid: string;
  text: null;
  /**
   * Time start in seconds
   *
   * @type {string}
   * @memberof BreakingNews
   */
  time_start: string;
  /**
   * Time end in seconds
   *
   * @type {string}
   * @memberof BreakingNews
   */
  time_end: string;
  title: string;
  type: 'breaking' | 'notice' | 'event' | 'news';
}

type SortType = 'updated:desc' | 'updated:asc' | 'created:desc' | 'created:asc';

export interface NodeQueryParams {
  channels?: string[] | number[];
  date?: string;
  dateFrom?: string;
  dateTo?: string;
  displayOutput?: NewsDisplayOutputTypes;
  page?: number;
  pageSize?: number;
  sort?: SortType;
  symbols?: string[];
  tokenType?: NodeQueryParamsTokenType;
  topics?: string[];
  type?: string;
}

export type NodeQueryParamsTokenType =
  | 'sec'
  | 'press-release'
  | 'partner'
  | 'korea'
  | 'japan'
  | 'spain'
  | 'italy'
  | 'france';

export interface NewsInterface {
  nodes: News[];
  query: NewsSectionQueryInterface;
  title?: string;
}

export interface NewsSectionQueryInterface {
  symbols?: string[];
  channels?: string[] | number[];
  page?: number;
  pageSize?: number;
  t?: number;
  tickers?: string;
  title?: string;
  tags?: string[] | number[];
  type?: string;
  displayOutput?: NewsDisplayOutputTypes;
  excludeAutomated?: boolean;
  excludeSponsored?: boolean;
  excludeContentTypes?: string;
}

export interface NewsSectionInterface {
  query: NodeQueryParams;
  title?: string;
  pageSize?: number;
}

export interface NewsFeedQueryParams {
  channels?: string[];
  date?: string;
  dateFrom?: string;
  dateTo?: string;
  displayOutput?: NewsDisplayOutputTypes;
  page?: number;
  pageSize?: number;
  sort?: SortType;
  tickers?: string[];
  token?: string;
  topics?: string[];
  type?: 'stories' | 'headlines';
}

export interface ArticleSearchResponse {
  data: {
    [id: string]: {
      link: string;
      pub_date: string;
      title: string;
    };
  };
}

export interface NodeCategory {
  description?: string;
  name: string;
  primary?: boolean;
  tid?: number;
  vid?: number;
  cusip?: string;
}

export interface News {
  author: string;
  body: string;
  campaigns?: string;
  channels: NodeCategory[];
  created: string;
  id: number;
  image: Image[] | string;
  assets?: Asset[];
  stocks: NodeCategory[] | null;
  tags: NodeCategory[] | null;
  teaser: string | null;
  title: string | null;
  updated: string | null;
  url: string;
  blocks?: any[] | null;
  meta?: Meta | null;
}

export interface Asset {
  alt?: string;
  attributes?: {
    fid: string;
    filename: string;
    filepath: string;
    filesize: number;
    image_attributes?: {
      resolution?: {
        height: number;
        width: number;
        dpi: number;
      };
    };
  };
  mime?: string;
  primary?: boolean;
  source?: string;
  title?: string;
  type: string;
}

export interface Image {
  alt?: string;
  size?: 'small' | 'large' | 'thumb';
  url: string;
  height?: number;
  width?: number;
}

// TODO - Create Article Manager, didn't want to duplicate code from the manager in the Article UI library
export interface ArticleData {
  dfpTags?: any;
  assets: any;
  author?: any;
  authorChange?: string;
  body: string;
  parsedBody: string[];
  campaigns?: any;
  campaignStrategy?: any;
  canonicalPath?: string;
  relatedArticles?: News[];
  channels: TidParam[];
  createdAt: string;
  id: number | string;
  image: string;
  primaryImage?: any;
  imageLarge?: string;
  isHeadline?: boolean;
  isBzPost?: boolean;
  isBzProPost?: boolean;
  keyItems?: { value: string }[];
  meta: any;
  metaProps?: any;
  profileUrl?: string;
  headStyles?: string;
  name: string;
  nodeId?: number;
  quotes: any[];
  tags: TidParam[];
  terms?: any[];
  teaserText: string;
  tickers: any[];
  title: string;
  titleSeo?: string;
  type: string;
  updatedAt: string;
  url?: string;
  blocks: any[];
  translationMeta?: any;
}
