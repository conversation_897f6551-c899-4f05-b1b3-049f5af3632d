import { DateTime } from 'luxon';
import { length } from 'ramda';
import { PageType } from '@benzinga/seo';
import { getSessionSingleton, Session } from '@benzinga/session';
import { QuotesManager } from '@benzinga/quotes-manager';
import { StoryCategory } from '@benzinga/advanced-news-manager';
import { appEnvironment } from '@benzinga/utils';
import { formatOgImage } from '@benzinga/frontend-utils';
import { getCanonicalUrl, isContentAutomated, isStoryTaggedPressRelease } from './utils';
import { authorTranslatedStoryTypes } from '@benzinga/internal-news-manager';
import { checkIfIsSponsoredSourceContent } from '../utils';
import { ArticleData, InternalNodeCamelCase, InternalNodeQuotes } from '../entities/article';
import { decode } from 'html-entities';

// (TODO: Come back to. Duplicated from @benzinga/article utils - to prevent circulcar dependency)
const isTagged = (article: ArticleData, tids: number[]) => {
  return article.tags?.some(tag => tids.includes(tag?.tid));
};

const getPaywallType = (article: ArticleData) => {
  const paywallTags = {
    'c-suite-buys-of-the-week': 943954,
    'easy-income-portfolio': 944696,
    'insider-report': 938584,
    interviews: 714112,
    mavens: 154244,
    'options-trade-of-the-day': 946635,
    'perfect-stock-portfolio': 944719,
    'stock-of-the-day': 931449,
    'stock-whisper-index': 866728,
    'under-the-radar': 943823,
  };

  if (isTagged(article, Object.values(paywallTags))) return 'hard';
  return 'soft-hard';
};

const articleMetaBuildKeyswords = (article: ArticleData): string[] => {
  const keywords = new Set<string>();

  // ToDo: Add logic for +Pro and Pro-only articles when we have the data JIRA: https://benzinga.atlassian.net/browse/BZFE-105
  // PRO, BZ+PRO
  const PageIsBzPro = 'BZ';
  keywords.add(`"PageIsBzPro: ${PageIsBzPro}"`);

  // Add CMS Tag, If meta['wp_post_meta']['post_type'] exists then it is from WP
  if (article?.meta?.wp_post_meta?.post_type || false) {
    keywords.add(`"CMS: WordPress"`);
  } else {
    keywords.add(`"CMS: Drupal"`);
  }

  // ToDo: Add data for campaigns when we have the data JIRA: https://benzinga.atlassian.net/browse/BZFE-106
  // 'campaign: ' + (template / sponsorship campaign)

  article &&
    article.channels &&
    article.channels.forEach(rowChannel => {
      // prettier-ignore
      if (rowChannel.name) {
        keywords.add(`"category: ${rowChannel.name?.replace(/"/g, '&quot;')}"`);
      }
    });
  article &&
    article.tickers &&
    article.tickers.forEach(rowTickers => {
      // prettier-ignore
      if (rowTickers.name) {
        keywords.add(`"symbol: ${rowTickers.name?.replace(/"/g, '&quot;')}"`);
      }
    });
  article &&
    article.tags &&
    article.tags.forEach(rowTags => {
      // prettier-ignore
      if (rowTags.name) {
        keywords.add(`"tag: ${rowTags.name?.replace(/"/g, '&quot;')}"`);
      }
    });

  return [...keywords];
};

// @ToDo: Include `Press Releases` if in the Press Release group
const articleMetaBuildArticleSection = (channels: StoryCategory[]) => {
  if (length(channels) && channels[0].name) {
    return channels[0].name;
  }
  return null;
};

const articleMetaBuildMentions = (symbols: StoryCategory[]): string[] => {
  const mentions = new Set<string>();

  // ToDo: Get the proper company name when reliable data is available JIRA: https://benzinga.atlassian.net/browse/BZFE-107
  // ToDo: Change base URL based on env
  symbols &&
    symbols.forEach(symbol => {
      const companyName = symbol.description || symbol.name;
      // prettier-ignore
      mentions.add(`{
      "@type": "Corporation",
      "tickerSymbol": "${symbol.name?.replace(/"/g, '&quot;')}",
      "name": "${companyName?.replace(/"/g, '&quot;')}",
      "url": "${process.env.BASE_URL}/quote/${symbol.name?.replace(/"/g, '&quot;')}"
    }`);
    });

  return [...mentions];
};

const getArticleChannelsNames = (article: ArticleData) => {
  if (Array.isArray(article?.channels)) {
    return article.channels.map(item => item.name);
  } else {
    return [];
  }
};

const decodeDescription = (text: string) => {
  // return text ? decode(text) : '';
  return text ?? '';
};

export const getQuoteDetailsFromSymbols = async (symbols: string[]): Promise<InternalNodeQuotes[]> => {
  if (!Array.isArray(symbols)) return [];
  const session = getSessionSingleton();
  const quoteManager = session.getManager(QuotesManager);
  const quotesRes = await quoteManager.getDelayedQuotes(symbols);
  const quotes = quotesRes?.ok ? Object.values(quotesRes.ok) : [];

  const quotesList = quotes.map(quote => {
    const isCrypto = quote.type === 'CRYPTO';
    return {
      exchange: quote.bzExchange ?? null,
      name: isCrypto ? quote.description : quote.name ?? null,
      price: quote.lastTradePrice ?? null,
      symbol: quote.symbol ?? null,
      type: quote.type ?? null,
      volume: quote.volume ?? null,
    };
  });
  return quotesList;
};

const formatTickerName = (ticker: { exchange: string; name: string; symbol: string }) => {
  if (!ticker?.symbol) return null;
  const result = ticker?.name
    ? `${ticker.name?.replace('- United States Dollar', '')} (${ticker.exchange ? `${ticker.exchange}:` : ''}${
        ticker.symbol
      })`
    : `(${ticker.symbol})`;
  return result;
};

const getLimitedTickers = (tickers: StoryCategory[]): string[] => {
  if (!Array.isArray(tickers)) return [];
  let symbols: string[] = [];
  const primaries = tickers?.filter(ticker => ticker.primary).map(ticker => ticker.name);
  const secondaries = tickers?.slice(0, 2).map(ticker => ticker.name);

  if (primaries?.length > 0) symbols = primaries;
  else if (secondaries?.length) symbols = secondaries;
  return symbols;
};

const generateMetaTitleTickers = async (article: ArticleData) => {
  let result = '';
  const symbols = getLimitedTickers(article.tickers);
  const quoteDetails = await getQuoteDetailsFromSymbols(symbols);
  const formattedResult = quoteDetails
    .map(formatTickerName)
    .splice(0, 4)
    .filter(quote => quote)
    .join(', ');
  if (formattedResult) result = formattedResult;
  result = result ? `- ${result}` : '';
  return result;
};

export const articlesMetaInfo = async (article: ArticleData) => {
  const canonicalURL = getCanonicalUrl(article);
  const metaTitleTickers = await generateMetaTitleTickers(article);

  const title = `${article.titleSeo?.length ? decode(article.titleSeo) : article.title} ${metaTitleTickers}`;
  const zone = appEnvironment().config().zone;
  const createdAtDate = new Date(article.createdAt);
  const createdAtISO = DateTime.fromJSDate(createdAtDate).setZone(zone).toISO();
  const updatedDate = new Date(article.updatedAt);
  const updatedAtISO = DateTime.fromJSDate(updatedDate).setZone(zone).toISO();
  const translationMeta = article?.translationMeta ? article.translationMeta : {};

  const primaryTicker =
    article?.tickers?.length > 0 && article.tickers[0].primary ? article.tickers[0].name : undefined;

  const aboutProperty = primaryTicker
    ? {
        about: {
          '@type': 'Corporation',
          name: primaryTicker,
          tickerSymbol: primaryTicker,
          url: `https://www.benzinga.com/quote/${primaryTicker}`,
        },
      }
    : undefined;

  // Determine if content is behind paywall server-side for proper schema markup
  const paywallType = getPaywallType(article);
  const isNotAccessibleForFree = paywallType === 'hard';

  return {
    author: article.name,
    authorId: (article.author?.uid as number) ?? null,
    authorType: article.author?.byLine ?? null,
    authorURL: article.author?.profileUrl || '',
    canonical: `${canonicalURL}`,
    dateCreated: createdAtISO,
    dateUpdated: updatedAtISO,
    description: decodeDescription(
      article.teaserText
        ? article.teaserText
            ?.replace(/"/g, '&quot;')
            ?.replace(/<\/?[^>]+(>|$)/g, '')
            ?.replace(/\s+/g, ' ')
            ?.replace(/(\r\n|\n|\r)/gm, '')
        : '',
    ),
    dimensions: {
      authorName: article.name,
      channels: getArticleChannelsNames(article),
      contentType: article.type || 'story',
      isBZisBZPRO: article.isBzProPost,
    },
    image: formatOgImage(article.image),
    isNotAccessibleForFree: isNotAccessibleForFree,
    nodeId: article.nodeId,
    ogImage: formatOgImage(article.image),
    ogType: 'article',
    pageType: PageType.Story,
    shortUrl: `https://benzinga.com/z/${article.nodeId}`,
    structuredData: {
      articleBody: article.body,
      articleSection: articleMetaBuildArticleSection(article.channels),
      keywords: articleMetaBuildKeyswords(article),
      mentions: articleMetaBuildMentions(article.tickers),
      pageType: 'NewsArticle',
      speakable: `{
        "@type": "SpeakableSpecification",
        "xpath": ["/html/head/title", "/html/head/meta[@name='description']/@content"],
        "cssSelector": ["#article-body p"]
      }`,
      ...(aboutProperty && { about: aboutProperty.about }),
    },
    title: title,
    translationMeta: translationMeta,
  };
};

export const comtexPublishers = [
  'pr_360prwire',
  'pr_auranddigital',
  'pr_biopharmajournal',
  'pr_cdnnewswire',
  'pr_concurwire',
  'pr_edmmedia',
  'pr_fairfieldmarketresearch',
  'pr_fnmedia',
  'pr_heraldkeeper',
  'pr_imarc',
  'pr_investorbrandmedia',
  'pr_issuewire',
  'pr_marketinsightreports',
  'pr_newswire11',
  'pr_reportmines',
  'pr_reportocean',
  'pr_reviewrumble',
  'pr_vehement',
];

export const comtexPublishersNoIndex = [
  'pr_etrendy',
  'pr_globeprwire',
  'pr_kingnewswire',
  'pr_lawfirmnewswire',
  'pr_lawfirmnewswire2',
  'pr_marketersmedia',
  'pr_newsworthy',
  'pr_pressservices',
  'pr_prlog',
  // https://benzinga.slack.com/archives/C01S5DQ9N3U/p1706696612814709?thread_ts=1706598493.594609&cid=C01S5DQ9N3U
  'pr_send2press',
  'pr_theexpresswire',
  'pr_yolowire',
];

//30 days indexable publishers
const indexablePublisher = ['pr_dataskrive', 'kisspr'];

// Only Indexable publisher with nofollow
const onlyIndexablePublisher = ['pr_pressadvantage'];

const noIndexAuthors = [
  1763484, // EIN Presswire for 'hacking' content
  // duplicateContentFeedAuthorIds below - https://gitlab.benzinga.io/benzinga/fusion/-/issues/10241
  65208, // InvestorsObserver
  476826, // StockNews
  201561, // InvestorPlace,
  82531, // Seeking Alpha,
  82406, // TipRanks
  302, // Zacks
  131793, // TalkMarkets
  259589, // InvestorsHub NewsWire
  54581, // GuruFocus
];

export const canArticleBeMarkedAsNoIndex = async (
  session: Session,
  article: ArticleData | InternalNodeCamelCase,
  wordCount?: number,
) => {
  const isTaggedPressRelease = await isStoryTaggedPressRelease(session, article);
  const diff = DateTime.now().diff(DateTime.fromISO(article.createdAt), ['days']);

  const userId = article.author?.uid || (article as InternalNodeCamelCase).userId;

  const isHeadline = (article as ArticleData).isHeadline || !article.body || (article.isBzProPost && !article.isBzPost);

  const isTypeBenzingaSlideshow = article?.type === 'benzinga_slideshow';

  if (isTypeBenzingaSlideshow) {
    return 'noindex, nofollow';
  }

  if (isHeadline) {
    return 'noindex, nofollow';
  }

  if (authorTranslatedStoryTypes.includes(article.type)) {
    return 'noindex, nofollow';
  }

  if (comtexPublishers.includes(article.type)) {
    return 'noindex, nofollow';
  }

  if (comtexPublishersNoIndex.includes(article.type)) {
    return 'noindex, nofollow';
  }

  if (indexablePublisher.includes(article.type)) {
    return diff.days > 30 ? 'noindex, nofollow' : null;
  }

  if (onlyIndexablePublisher.includes(article.type)) {
    return 'nofollow';
  }

  if (isTaggedPressRelease) {
    return 'noindex, nofollow';
  }

  if (userId && noIndexAuthors.includes(userId)) {
    return 'noindex, nofollow';
  }

  if (diff.days > 30 && isContentAutomated(userId)) {
    return 'noindex, nofollow';
  }

  const isSponsoredSourceContent = await checkIfIsSponsoredSourceContent(article.type, session);
  if (isSponsoredSourceContent) {
    return 'noindex, nofollow';
  }

  const ARBITRARY_MAX_WORD_COUNT = 175;
  if (wordCount && wordCount < ARBITRARY_MAX_WORD_COUNT && diff.days > 90) {
    return 'noindex, nofollow';
  }

  if (article?.meta?.Flags?.ShowAdvertiserDisclosure) {
    return 'nofollow';
  }

  return null;
};
