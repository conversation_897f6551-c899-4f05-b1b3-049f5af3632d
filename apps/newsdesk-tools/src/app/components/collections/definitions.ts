import { IconNames } from '@blueprintjs/icons';
import { comparators } from '@benzinga/pro-ui';
import moment from 'moment';

import { DateField } from '../../reducers/entities/collectionEntities';
import {
  cellRenderer,
  defaultColumnDefinition,
  filterParams,
  isPrimaryAutoCompleteItems,
  suppressKeyboardNavigation,
  valueGetter,
  valueParser,
  valueSetter,
} from '../grid/agGridUtils';
import AutoComplete from '../grid/AutoComplete';
import MultiselectField from '../grid/MultiselectField';
import CollectionActions from './CollectionActions';
import {
  CollectionDefinitionsById,
  CollectionGroupDefinitionsById,
  CollectionGroupId,
  CollectionId,
} from './collectionEntities';

import dividendGenerator from '../../generators/dividend';
import generateStoryEarning from '../../generators/earning';
import generateStoryRefinitiv from '../../generators/refinitiv';
import economicGenerator from '../../generators/economic';
import guidanceGenerator from '../../generators/guidance';
import generateStoryRating from '../../generators/rating';
import generateStoryWiim from '../../generators/wiim';
import DatePickerPopup from '../grid/DatePicker';
import { ColDef } from '@ag-grid-community/core';
import RatingsFileUpload from '../RatingsFileUpload';
import CheckboxField from '../grid/CheckboxField';
import TranscriptActions from './TranscriptActions';

export const collectionGroupDefinitionsById: CollectionGroupDefinitionsById = {
  [CollectionGroupId.calendar]: {
    collectionGroupId: CollectionGroupId.calendar,
    defaultDateField: DateField.DATE,
    defaultDateRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
    icon: IconNames.CALENDAR,
    name: {
      plural: 'Calendars',
      singular: 'Calendar',
    },
  },
  [CollectionGroupId.dictionary]: {
    collectionGroupId: CollectionGroupId.dictionary,
    icon: IconNames.BOOK,
    name: {
      plural: 'Dictionaries',
      singular: 'Dictionary',
    },
  },
};

export const collectionDefinitionsById: CollectionDefinitionsById = {
  [CollectionId.acquisition]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.acquisition,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Announcement Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('date'),
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { fill: 'cusip', name: 'acquirer_cusip' },
            { fill: 'exchange', name: 'acquirer_exchange' },
            { fill: 'isin', name: 'acquirer_isin' },
            { fill: 'name', name: 'acquirer_name', priorityFill: 'shortName' },
          ],
          dictionary: 'tickers',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'acquirer_ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Acquirer Ticker',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'acquirer_isin',
        filter: 'agTextColumnFilter',
        headerName: 'Acquirer ISIN',
        initialHide: true,
      },
      {
        field: 'acquirer_cusip',
        filter: 'agTextColumnFilter',
        headerName: 'Acquirer CUSIP',
        initialHide: true,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'exchanges',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'acquirer_exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Acquirer Exchange',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'acquirer_name',
        filter: 'agTextColumnFilter',
        headerName: 'Acquirer Company',
        type: 'width-medium',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { fill: 'cusip', name: 'target_cusip' },
            { fill: 'exchange', name: 'target_exchange' },
            { fill: 'isin', name: 'target_isin' },
            { fill: 'name', name: 'target_name', priorityFill: 'shortName' },
          ],
          dictionary: 'tickers',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'target_ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Target Ticker',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'target_isin',
        filter: 'agTextColumnFilter',
        headerName: 'Target ISIN',
        initialHide: true,
      },
      {
        field: 'target_cusip',
        filter: 'agTextColumnFilter',
        headerName: 'Target CUSIP',
        initialHide: true,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'exchanges',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'target_exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Target Exchange',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'target_name',
        filter: 'agTextColumnFilter',
        headerName: 'Target Company',
        type: 'width-medium',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'currency_codes',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'currency',
        filter: 'agTextColumnFilter',
        headerName: 'Currency',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'date_expected',
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Expected Completion Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('date_expected'),
      },
      {
        field: 'date_completed',
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date Completed',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('date_completed'),
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'ma_deal_status',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'deal_status',
        filter: 'agTextColumnFilter',
        headerName: 'Deal Status',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'deal_type',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'deal_type',
        filter: 'agTextColumnFilter',
        headerName: 'Deal Type',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'deal_size',
        filter: 'agTextColumnFilter',
        headerName: 'Deal Size',
        valueSetter: valueSetter.setShortHandField('deal_size'),
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'payment_type',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'deal_payment_type',
        filter: 'agTextColumnFilter',
        headerName: 'Payment Type',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'cash_amt',
        filter: 'agTextColumnFilter',
        headerName: 'Cash Amount',
        valueSetter: valueSetter.setShortHandField('cash_amt'),
      },
      {
        field: 'share_swap_ratio',
        filter: 'agTextColumnFilter',
        headerName: 'Share Swap Ratio',
      },
      {
        field: 'exchange_ratio',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange Ratio',
      },
      {
        field: 'target_share_price_offered',
        filter: 'agTextColumnFilter',
        headerName: 'Target Share Price Offered	',
      },
      {
        field: 'deal_premium_announced',
        filter: 'agTextColumnFilter',
        headerName: 'Deal Premium Announced',
      },
      {
        field: 'deal_premium_last',
        filter: 'agTextColumnFilter',
        headerName: 'Deal Premium Last Close',
      },
      {
        field: 'deal_terms_extra',
        filter: 'agTextColumnFilter',
        headerName: 'Additional Deal Terms',
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',

        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Date' }],
    defaultEditingColumn: 'acquirer_ticker',
    icon: IconNames.INTERSECTION,
    mongoCollectionId: 'ma',
    name: 'Merger and Acquisition',
    permissionResourceId: 'acquisition-calendar',
    skippableFields: [
      'acquirer_cusip',
      'acquirer_exchange',
      'acquirer_isin',
      'acquirer_name',
      'importance',
      'target_cusip',
      'target_exchange',
      'target_isin',
      'target_name',
    ],
  },
  [CollectionId.analyst]: {
    collectionGroupId: CollectionGroupId.dictionary,
    collectionId: CollectionId.analyst,
    columnDefinitions: [
      {
        editable: false,
        field: DateField.CREATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Created',
        initialHide: false,
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.CREATED),
      },
      {
        editable: false,
        field: DateField.UPDATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Last Updated',
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.UPDATED),
      },
      {
        field: 'last_name',
        filter: 'agTextColumnFilter',
        headerName: 'Last Name',
      },
      {
        field: 'first_name',
        filter: 'agTextColumnFilter',
        headerName: 'First Name',
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          dictionary: 'firm_name',
          field: 'name',
          isMulti: false,
          itemField: 'name',
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.renderNameWithValidation,
        field: 'firm',
        filter: 'agTextColumnFilter',
        headerName: 'Analyst Firm',

        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'phone',
        filter: 'agTextColumnFilter',
        headerName: 'Phone Number',
        type: 'numeric-field',
      },
      {
        field: 'email',
        filter: 'agTextColumnFilter',
        headerName: 'Email',
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'hidden',
        filter: 'agSetColumnFilter',
        headerName: 'Hidden UI',
        valueGetter: valueGetter.booleanGetterHOF('hidden'),
        valueSetter: valueSetter.booleanSetter('hidden'),
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.dictionary,
        },
        colId: 'action',
        editable: false,
        headerName: 'Action',
        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [
      { field: DateField.CREATED, title: 'Creation Date' },
      { field: DateField.UPDATED, title: 'Modified Date' },
    ],
    defaultEditingColumn: 'last_name',
    icon: IconNames.PREDICTIVE_ANALYSIS,
    mongoCollectionId: 'calendar_ratings_analysts',
    name: 'Analyst',
    permissionResourceId: 'analyst-dictionary',
  },
  [CollectionId.trade]: {
    collectionGroupId: CollectionGroupId.dictionary,
    collectionId: CollectionId.trade,
    columnDefinitions: [
      {
        editable: false,
        field: DateField.CREATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Created',
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.CREATED),
      },
      {
        editable: false,
        field: DateField.UPDATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Last Updated',
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.UPDATED),
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Name',
      },
      {
        field: 'email',
        filter: 'agTextColumnFilter',
        headerName: 'Email',
      },
      {
        field: 'website',
        filter: 'agTextColumnFilter',
        headerName: 'Website',
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'hidden',
        filter: 'agSetColumnFilter',
        headerName: 'Hidden UI',
        valueGetter: valueGetter.booleanGetterHOF('hidden'),
        valueSetter: valueSetter.booleanSetter('hidden'),
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.dictionary,
        },
        colId: 'action',
        editable: false,
        headerName: 'Action',

        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [
      { field: DateField.CREATED, title: 'Creation Date' },
      { field: DateField.UPDATED, title: 'Modified Date' },
    ],
    defaultEditingColumn: 'name',
    icon: IconNames.PREDICTIVE_ANALYSIS,
    mongoCollectionId: 'trader_profile',
    name: 'Trader Profile',
    permissionResourceId: 'trader-profile-dictionary',
  },
  [CollectionId.categories]: {
    collectionGroupId: CollectionGroupId.dictionary,
    collectionId: CollectionId.categories,
    columnDefinitions: [
      {
        editable: false,
        field: DateField.CREATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Created',
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.UPDATED),
      },
      {
        editable: false,
        field: DateField.UPDATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Last Updated',
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.UPDATED),
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'hidden',
        filter: 'agSetColumnFilter',
        headerName: 'Hidden',
        valueGetter: valueGetter.booleanGetterHOF('hidden'),
        valueSetter: valueSetter.booleanSetter('hidden'),
      },
      {
        field: 'name',
        headerName: 'Name',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.dictionary,
        },
        colId: 'action',
        editable: false,
        headerName: 'Action',
        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [
      { field: DateField.CREATED, title: 'Creation Date' },
      { field: DateField.UPDATED, title: 'Modified Date' },
    ],
    defaultEditingColumn: 'name',
    icon: IconNames.GRID_VIEW,
    mongoCollectionId: 'calendar_economic_event_category',
    name: 'Economics Categories',
    permissionResourceId: 'economic-dictionary',
  },
  [CollectionId.fda_event_type]: {
    collectionGroupId: CollectionGroupId.dictionary,
    collectionId: CollectionId.fda_event_type,
    columnDefinitions: [
      {
        editable: false,
        field: DateField.CREATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Created',
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.CREATED),
      },
      {
        editable: false,
        field: DateField.UPDATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Last Updated',
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.UPDATED),
      },
      {
        field: 'name',
        headerName: 'Name',
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'is_outcome_brief',
        filter: 'agSetColumnFilter',
        headerName: 'Is categorized outcome',
        valueGetter: valueGetter.booleanGetterHOF('is_outcome_brief'),
        valueSetter: valueSetter.booleanSetter('is_outcome_brief'),
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.dictionary,
        },
        colId: 'action',
        editable: false,
        headerName: 'Action',
        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [
      { field: DateField.CREATED, title: 'Creation Date' },
      { field: DateField.UPDATED, title: 'Modified Date' },
    ],
    defaultEditingColumn: 'name',
    icon: IconNames.GRID_VIEW,
    mongoCollectionId: 'fda_event_type',
    name: 'FDA Event Type',
    permissionResourceId: 'fda-event-type-dictionary',
  },
  [CollectionId.companies]: {
    collectionGroupId: CollectionGroupId.dictionary,
    collectionId: CollectionId.companies,
    columnDefinitions: [
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Company Name',
      },
      {
        field: 'company_id',
        filter: 'agTextColumnFilter',
        headerName: 'Company ID',
      },
      {
        field: 'short_name',
        filter: 'agTextColumnFilter',
        headerName: 'Short Name',
      },
      {
        field: 'cik',
        filter: 'agTextColumnFilter',
        headerName: 'CIK',
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          arrayType: 'object',
          dictionary: 'tickers',
          field: 'symbol',
          isMulti: true,
          itemField: 'symbol',
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.stringifyTickers,
        field: 'securities',
        headerName: 'Securities',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
        valueSetter: valueSetter.tickersSetter('securities'),
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.dictionary,
        },
        colId: 'action',
        editable: false,
        headerName: 'Action',
        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [
      { field: DateField.CREATED, title: 'Creation Date' },
      { field: DateField.UPDATED, title: 'Modified Date' },
    ],
    defaultEditingColumn: 'id',
    icon: IconNames.GRID_VIEW,
    mongoCollectionId: 'companies',
    name: 'Companies',
    permissionResourceId: 'companies-dictionary',
  },

  [CollectionId.drugs]: {
    collectionGroupId: CollectionGroupId.dictionary,
    collectionId: CollectionId.drugs,
    columnDefinitions: [
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Drug Name',
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          arrayType: 'object',
          dictionary: 'drug_company',
          field: 'companies',
          isMulti: true,
          itemField: 'name',
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.joinArrayField('drugs_companies'),
        field: 'companies',
        filter: 'agTextColumnFilter',
        filterParams: {
          valueGetter: valueGetter.getArrayFieldObjectNameValue('companies'),
        },
        headerName: 'Company/companies reference',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'indication_symptom',
        filter: 'agTextColumnFilter',
        headerName: 'Indication/symptom',
        valueSetter: valueSetter.setIndicationSymptoms('indication_symptom'),
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'generic',
        filter: 'agTextColumnFilter',
        headerName: 'Is it a generic drug?',
        valueGetter: valueGetter.booleanGetterHOF('generic'),
        valueSetter: valueSetter.booleanSetter('generic'),
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.dictionary,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [
      { field: DateField.CREATED, title: 'Creation Date' },
      { field: DateField.UPDATED, title: 'Modified Date' },
    ],
    defaultEditingColumn: 'id',
    icon: IconNames.GRID_VIEW,
    mongoCollectionId: 'drugs',
    name: 'Drugs',
    permissionResourceId: 'drugs-dictionary',
  },
  [CollectionId.statuses]: {
    collectionGroupId: CollectionGroupId.dictionary,
    collectionId: CollectionId.statuses,
    columnDefinitions: [
      {
        field: 'label',
        filter: 'agTextColumnFilter',
        headerName: 'Drug Status Label',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.dictionary,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',

        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [
      { field: DateField.CREATED, title: 'Creation Date' },
      { field: DateField.UPDATED, title: 'Modified Date' },
    ],
    defaultEditingColumn: 'label',
    icon: IconNames.GRID_VIEW,
    mongoCollectionId: 'drug_statuses',
    name: 'Drug Statuses',
    permissionResourceId: 'drugs-status-dictionary',
  },

  [CollectionId.conference]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.conference,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF(DateField.DATE),
      },
      {
        field: DateField.TIME,
        headerName: 'Time',
        type: 'numeric-field',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { name: 'cusip' },
            { name: 'exchange' },
            { name: 'isin' },
            { fill: 'name', name: 'name', priorityFill: 'shortName' },
          ],
          dictionary: 'tickers',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Ticker',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Company Name',
        type: 'width-medium',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'exchanges',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'isin',
        filter: 'agTextColumnFilter',
        headerName: 'ISIN',
        initialHide: true,
      },
      {
        field: 'cusip',
        filter: 'agTextColumnFilter',
        headerName: 'CUSIP',
        initialHide: true,
      },
      {
        field: 'start_time',
        filter: 'agTextColumnFilter',
        headerName: 'Start Time',
        type: 'numeric-field',
      },
      {
        field: 'phone_num',
        filter: 'agTextColumnFilter',
        headerName: 'Phone #',
        type: 'numeric-field',
      },
      {
        field: 'international_num',
        filter: 'agTextColumnFilter',
        headerName: 'International #',
        type: 'numeric-field',
      },
      {
        field: 'reservation_num',
        filter: 'agTextColumnFilter',
        headerName: 'Reservation #',
        type: 'numeric-field',
      },
      {
        field: 'access_code',
        filter: 'agTextColumnFilter',
        headerName: 'Access Code	',
        type: 'numeric-field',
      },

      {
        field: 'webcast_url',
        filter: 'agTextColumnFilter',
        headerName: 'Webcast URL',
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'confirmed',
        filter: 'agSetColumnFilter',
        headerClass: 'split-header',
        headerName: 'Confirmed',
        minWidth: 50,
        valueGetter: valueGetter.booleanGetterHOF('confirmed'),
        valueSetter: valueSetter.booleanSetter('confirmed'),
      },
      {
        field: 'url',
        filter: 'agTextColumnFilter',
        headerName: 'Info URL',
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: TranscriptActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
          transcriptOnly: true,
        },
        colId: 'transcript',
        editable: false,
        filter: 'agTextColumnFilter',
        headerName: 'Transcript',
        type: 'width-small',
      },
      {
        cellRenderer: TranscriptActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
          summaryOnly: true,
        },
        colId: 'summary',
        editable: false,
        filter: 'agTextColumnFilter',
        headerName: 'Summary',
        type: 'width-small',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',
        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Date' }],
    defaultEditingColumn: 'ticker',
    icon: IconNames.PHONE,
    mongoCollectionId: 'conference',
    name: 'Conference Call',
    permissionResourceId: 'conference-calendar',
  },
  [CollectionId.dividend]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.dividend,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF(DateField.DATE),
      },
      {
        field: DateField.TIME,
        headerName: 'Time',
        type: 'numeric-field',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { name: 'cusip' },
            { name: 'exchange' },
            { name: 'isin' },
            { fill: 'name', name: 'name', priorityFill: 'shortName' },
          ],
          dictionary: 'tickers',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        // cellEditorPopupPosition: 'over',
        // editable: true,
        field: 'ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Ticker',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'isin',
        filter: 'agTextColumnFilter',
        headerName: 'ISIN',
        initialHide: true,
      },
      {
        field: 'cusip',
        filter: 'agTextColumnFilter',
        headerName: 'CUSIP',
        initialHide: true,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'exchanges',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'currency_codes',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'currency',
        filter: 'agTextColumnFilter',
        headerName: 'Currency',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Company Name',
        type: 'width-medium',
      },
      {
        field: 'period',
        filter: 'agTextColumnFilter',
        headerName: 'Period',
        valueSetter: valueSetter.periodSetter('period'),
      },
      {
        field: 'year',
        filter: 'agTextColumnFilter',
        headerName: 'Year',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'frequency',
        filter: 'agSetColumnFilter',
        headerName: 'Frequency',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'current_price',
        filter: 'agTextColumnFilter',
        headerName: 'Price',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        field: 'dividend',
        filter: 'agTextColumnFilter',
        headerName: 'Dividend',
        type: 'numeric-field',
      },
      {
        cellEditor: DatePickerPopup,
        field: 'dividend_suspended',
        filter: 'agTextColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Dividend Suspended',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateFromMillies('dividend_suspended'),
      },
      {
        field: 'dividend_prior',
        filter: 'agTextColumnFilter',
        headerName: 'Dividend Prior',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'dividend_types',
        },

        cellEditorPopup: true,
        editable: true,
        field: 'dividend_type',
        filter: 'agTextColumnFilter',
        headerName: 'Type',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'dividend_yield',
        filter: 'agTextColumnFilter',
        headerName: 'Yield',
        type: 'numeric-field',
      },
      {
        field: 'record_date',
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Recorded',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('record_date'),
      },
      {
        field: 'ex_dividend_date',
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Ex Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('ex_dividend_date'),
      },
      {
        field: 'payable_date',
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Pay Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('payable_date'),
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'confirmed',
        filter: 'agSetColumnFilter',
        headerClass: 'split-header',
        headerName: 'Confirmed',
        minWidth: 50,
        valueGetter: valueGetter.booleanGetterHOF('confirmed'),
        valueSetter: valueSetter.booleanSetter('confirmed'),
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',

        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [
      { field: DateField.DATE, title: 'Date' },
      { field: 'ex_dividend_date', title: 'Ex Date' },
      { field: 'payable_date', title: 'Payable Date' },
      { field: 'record_date', title: 'Recorded Date' },
    ],
    defaultEditingColumn: 'ticker',
    icon: IconNames.NEW_GRID_ITEM,
    mongoCollectionId: 'dividend',
    name: 'Dividend',
    permissionResourceId: 'dividend-calendar',
    skippableFields: [
      'currency_code',
      'cusip',
      'dividend_type',
      'dividend_yield',
      'ex_dividend_date',
      'exchange',
      'isin',
      'name',
      'time',
    ],
    storyGenerator: dividendGenerator,
  },
  [CollectionId.earning]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.earning,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        sort: 'desc',
        sortIndex: 1,
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF(DateField.DATE),
      },
      {
        field: DateField.TIME,
        headerName: 'Time',
        sort: 'desc',
        sortIndex: 2,
        type: 'numeric-field',
      },
      {
        field: 'time_period',
        headerClass: 'split-header',
        headerName: 'Release\nSession',
        initialHide: true,
        valueSetter: valueSetter.releaseSetter('time_period'),
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { name: 'cusip' },
            { name: 'exchange' },
            { name: 'isin' },
            { fill: 'name', name: 'name', priorityFill: 'shortName' },
          ],
          dictionary: 'tickers',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Ticker',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'exchanges',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'isin',
        filter: 'agTextColumnFilter',
        headerName: 'ISIN',
        initialHide: true,
      },
      {
        field: 'cusip',
        filter: 'agTextColumnFilter',
        headerName: 'CUSIP',
        initialHide: true,
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Name',
        type: 'width-medium',
      },
      {
        field: 'period',
        filter: 'agTextColumnFilter',
        headerName: 'Period',
        valueSetter: valueSetter.periodSetter('period'),
      },
      {
        field: 'period_year',
        filter: 'agTextColumnFilter',
        headerName: 'Year',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'currency_codes',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'currency',
        filter: 'agTextColumnFilter',
        headerName: 'Currency',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'date_confirmed',
        filter: 'agSetColumnFilter',
        headerClass: 'split-header',
        headerName: 'Date\nConfirmed',
        type: 'width-small',
        valueGetter: valueGetter.booleanGetterHOF('date_confirmed'),
        valueSetter: valueSetter.booleanSetter('date_confirmed'),
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'date_verified',
        filter: 'agSetColumnFilter',
        headerClass: 'split-header',
        headerName: 'Date\nVerified',
        initialHide: true,
        type: 'width-small-hidden-field',
        valueGetter: valueGetter.booleanGetterHOF('date_verified'),
        valueSetter: valueSetter.booleanSetter('date_verified'),
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'actuals_verified',
        filter: 'agSetColumnFilter',
        headerClass: 'split-header',
        headerName: 'Actuals\nVerified',
        initialHide: true,
        type: 'width-small-hidden-field',
        valueGetter: valueGetter.booleanGetterHOF('actuals_verified'),
        valueSetter: valueSetter.booleanSetter('actuals_verified'),
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'eps_type',
        },
        cellEditorPopup: true,
        cellStyle: { backgroundColor: '#39a6ff4d' },
        editable: true,
        field: 'eps_type',
        filter: 'agTextColumnFilter', // Inline style for cell background
        headerName: 'EPS Type',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellClass: ['bold-text', 'numeric-cell'],
        cellStyle: { backgroundColor: '#39a6ff4d' },
        field: 'eps',
        filter: 'agTextColumnFilter',
        headerName: 'EPS',
        type: 'numeric-field',
        valueParser: valueParser.floatParserWithFixed,
      },
      {
        field: 'eps_est',
        filter: 'agTextColumnFilter',
        headerName: 'EPS Est',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        editable: false,
        filter: 'agTextColumnFilter',
        headerName: 'RF.EPS-EST-GAP',
        type: 'numeric-field',
        valueGetter: params =>
          params.data.refinitiv_estimate && params.data.refinitiv_estimate.currency === 'USD'
            ? params.data.refinitiv_estimate.gps
            : null,
      },
      {
        editable: false,
        filter: 'agTextColumnFilter',
        headerName: 'RF.FFO-EST',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: params =>
          params.data.refinitiv_estimate && params.data.refinitiv_estimate.currency === 'USD'
            ? params.data.refinitiv_estimate.ffo_est
            : null,
      },
      {
        editable: false,
        filter: 'agTextColumnFilter',
        headerName: 'RF.EPS-EST-ADJ',
        type: 'numeric-field',
        valueGetter: params =>
          params.data.refinitiv_estimate && params.data.refinitiv_estimate.currency === 'USD'
            ? params.data.refinitiv_estimate.eps_est
            : null,
      },
      {
        field: 'eps_surprise',
        filter: 'agTextColumnFilter',
        headerName: 'EPS Surprise',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        field: 'eps_surprise_percent',
        filter: 'agTextColumnFilter',
        headerName: 'Surprise % EPS',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        field: 'eps_prior',
        filter: 'agTextColumnFilter',
        headerName: 'EPS Prior',
        type: 'numeric-field',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'revs_type',
        },
        cellEditorPopup: true,
        cellStyle: { backgroundColor: '#39a6ff4d' },
        editable: true,
        field: 'revenue_type',
        filter: 'agTextColumnFilter',
        headerName: 'Revs Type	',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellClass: ['bold-text', 'numeric-cell'],
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        cellStyle: { backgroundColor: '#39a6ff4d' },
        field: 'revenue',
        filter: 'agTextColumnFilter',
        headerName: 'Revs',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('revenue'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        filter: 'agTextColumnFilter',
        headerName: 'RF.REV-EST',
        type: 'numeric-field',
        valueGetter: params =>
          params.data.refinitiv_estimate && params.data.refinitiv_estimate.currency === 'USD'
            ? params.data.refinitiv_estimate.revenue_est
            : null,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'revenue_est',
        filter: 'agTextColumnFilter',
        headerName: 'Revs Est',
        initialHide: true,
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('revenue_est'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'revenue_surprise',
        filter: 'agTextColumnFilter',
        headerName: 'Revs Surprise',
        initialHide: true,
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('revenue_surprise'),
      },
      {
        field: 'revenue_surprise_percent',
        filter: 'agTextColumnFilter',
        headerName: 'Surprise % Revs',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'revenue_prior',
        filter: 'agTextColumnFilter',
        headerName: 'Revs Prior',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('revenue_prior'),
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CheckboxField,
        cellRendererParams: { onlyCheckbox: true },
        editable: false,
        field: 'calEst',
        headerName: 'Calendar Estimates',
        width: 50,
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',
        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Date' }],
    defaultEditingColumn: 'ticker',
    icon: IconNames.CHART,
    mongoCollectionId: 'earning',
    name: 'Earnings',
    permissionResourceId: 'earnings-calendar',
    skippableFields: [
      'actuals_verified',
      'cusip',
      'date_verified',
      'eps_est',
      'eps_prior',
      'exchange',
      'importance',
      'isin',
      'name',
      'revenue_est',
      'revenue_est',
      'revenue_prior',
      'time_period',
    ],
    storyGenerator: generateStoryEarning,
  },
  [CollectionId.economic]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.economic,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        filter: true,
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        comparator: comparators.dateTimeComparator() as any,
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF(DateField.DATE),
      },
      {
        comparator: comparators.dateTimeComparator() as any,
        field: DateField.TIME,
        headerName: 'Time',
        type: 'numeric-field',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'country_codes',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'country',
        filter: 'agTextColumnFilter',
        headerName: 'Country',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          autoFill: [{ name: 'event_category' }],
          dictionary: 'event_name',
          field: 'name',
          filterField: 'country',
          isMulti: false,
          itemField: 'name',
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.renderNameWithValidation,
        field: 'event',
        filter: 'agTextColumnFilter',
        headerName: 'Event Name',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'event_category',
        filter: 'agTextColumnFilter',
        headerName: 'Event Category',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'currency_codes',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'currency',
        filter: 'agTextColumnFilter',
        headerName: 'Currency',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'event_period',
        filter: 'agTextColumnFilter',
        headerName: 'Period',
      },
      {
        field: 'period_year',
        filter: 'agTextColumnFilter',
        headerName: 'Year',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'actual',
        filter: 'agTextColumnFilter',
        headerName: 'Actual',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('actual'),
      },
      {
        field: 'actual_t',
        filter: 'agTextColumnFilter',
        headerClass: 'split-header',
        headerName: 'Actual\nType',
        type: 'width-small',
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'consensus',
        filter: 'agTextColumnFilter',
        headerName: 'Consensus',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('consensus'),
      },
      {
        field: 'consensus_t',
        filter: 'agTextColumnFilter',
        headerClass: 'split-header',
        headerName: 'Consensus\nType',
        type: 'width-small',
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'prior',
        filter: 'agTextColumnFilter',
        headerName: 'Prior',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('prior'),
      },
      {
        field: 'prior_t',
        filter: 'agTextColumnFilter',
        headerName: 'Prior Type',
        type: 'width-small',
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'economics_surprise',
        filter: 'agTextColumnFilter',
        headerName: 'Eco Surprise',
        initialHide: true,
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('economics_surprise'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'revised',
        filter: 'agTextColumnFilter',
        headerName: 'Revised From',
        initialHide: true,
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('revised'),
      },
      {
        field: 'revised_t',
        filter: 'agTextColumnFilter',
        headerClass: 'split-header',
        headerName: 'Revised\nType',
        initialHide: true,
        type: 'width-small',
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'confirmed',
        filter: 'agSetColumnFilter',
        headerClass: 'split-header',
        headerName: 'Confirmed',
        minWidth: 50,
        valueGetter: valueGetter.booleanGetterHOF('confirmed'),
        valueSetter: valueSetter.booleanSetter('confirmed'),
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',
        type: 'action-field',
      },
    ] as ColDef[],
    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Date' }],
    defaultEditingColumn: 'period_year',
    icon: IconNames.GLOBE,
    mongoCollectionId: 'economic',
    name: 'Economic',
    permissionResourceId: 'economic-calendar',
    skippableFields: ['event_category'],
    storyGenerator: economicGenerator,
  },
  [CollectionId.economics]: {
    collectionGroupId: CollectionGroupId.dictionary,
    collectionId: CollectionId.economics,
    columnDefinitions: [
      {
        editable: false,
        field: DateField.CREATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Created',
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.CREATED),
      },
      {
        editable: false,
        field: DateField.UPDATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Last Updated',
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.UPDATED),
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'hidden',
        filter: 'agSetColumnFilter',
        headerName: 'Hidden',
        valueGetter: valueGetter.booleanGetterHOF('hidden'),
        valueSetter: valueSetter.booleanSetter('hidden'),
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          dictionary: 'event_category',
          field: 'name',
          isMulti: false,
          itemField: 'name',
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.renderNameWithValidation,
        field: 'category',
        filter: 'agTextColumnFilter',
        headerName: 'Category',

        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'country_codes',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'country',
        filter: 'agTextColumnFilter',
        headerName: 'Country',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'description',
        headerName: 'Description',
      },
      {
        field: 'name',
        headerName: 'Name',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'event_category_period',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'period',
        filter: 'agTextColumnFilter',
        headerName: 'Period',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.dictionary,
        },
        colId: 'action',
        editable: false,
        headerName: 'Action',

        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [
      { field: DateField.CREATED, title: 'Creation Date' },
      { field: DateField.UPDATED, title: 'Modified Date' },
    ],
    defaultEditingColumn: 'category',
    icon: IconNames.GLOBE_NETWORK,
    mongoCollectionId: 'calendar_economic_event',
    name: 'Event Economics',
    permissionResourceId: 'event-dictionary',
  },
  [CollectionId.event]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.event,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: 'created',
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: 'updated',
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: 'date_start',
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Start Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('date_start'),
        valueSetter: valueSetter.dateSetter('date_start'),
      },
      {
        field: 'date_end',
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'End Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('date_end'),
        valueSetter: valueSetter.dateSetter('date_end'),
      },
      {
        field: 'start_time',
        filter: 'agTextColumnFilter',
        headerName: 'Start Time',
        type: 'numeric-field',
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          arrayType: 'object',
          collectionId: CollectionId.event,
          dictionary: 'tickers',
          field: 'symbol',
          fullDetails: true,
          isMulti: true,
          itemField: 'symbol',
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.stringifyTickers,
        field: 'securities',
        headerName: 'Securities',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
        valueSetter: valueSetter.tickersSetter('securities'),
      },
      {
        field: 'event_name',
        filter: 'agTextColumnFilter',
        headerName: 'Event Name',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'event',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'event_type',
        filter: 'agTextColumnFilter',
        headerName: 'Event Type',
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          dictionary: 'tags',
          isMulti: true,
        },
        cellEditorPopup: true,
        editable: true,
        field: 'tags',
        filter: 'agTextColumnFilter',
        headerName: 'Tags',
      },
      {
        field: 'source_link',
        filter: 'agTextColumnFilter',
        headerName: 'Source Link',
      },
      {
        field: 'webcast_link',
        filter: 'agTextColumnFilter',
        headerName: 'Webcast URL',
      },
      {
        field: 'phone_number',
        filter: 'agTextColumnFilter',
        headerName: 'Phone Number',
        type: 'numeric-field',
      },
      {
        field: 'international_number',
        filter: 'agTextColumnFilter',
        headerName: 'Intl Phone No',
        type: 'numeric-field',
      },
      {
        field: 'location',
        filter: 'agTextColumnFilter',
        headerName: 'Location',
      },
      {
        field: 'type',
        filter: 'agTextColumnFilter',
        headerName: 'Type',
        hide: true,
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',
        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Start Date' }],
    defaultEditingColumn: 'date',
    icon: IconNames.BRIEFCASE,
    mongoCollectionId: 'event',
    name: 'Event',
    permissionResourceId: 'event-calendar',
  },
  [CollectionId.firm]: {
    collectionGroupId: CollectionGroupId.dictionary,
    collectionId: CollectionId.firm,
    columnDefinitions: [
      {
        editable: false,
        field: DateField.CREATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Created',
        initialHide: false,
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.CREATED),
      },
      {
        editable: false,
        field: DateField.UPDATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Last Updated',
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.UPDATED),
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Name',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'currency_codes',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'currency',
        filter: 'agTextColumnFilter',
        headerName: 'Currency',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'url',
        filter: 'agTextColumnFilter',
        headerName: 'URL',
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'hidden',
        filter: 'agSetColumnFilter',
        headerName: 'Hidden',
        valueGetter: valueGetter.booleanGetterHOF('hidden'),
        valueSetter: valueSetter.booleanSetter('hidden'),
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.dictionary,
        },
        colId: 'action',
        editable: false,
        headerName: 'Action',

        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [
      { field: DateField.CREATED, title: 'Creation Date' },
      { field: DateField.UPDATED, title: 'Modified Date' },
    ],
    defaultEditingColumn: 'name',
    icon: IconNames.DIAGRAM_TREE,
    mongoCollectionId: 'calendar_ratings_firm',
    name: 'Firm',
    permissionResourceId: 'firm-dictionary',
  },
  [CollectionId.guidance]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.guidance,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF(DateField.DATE),
      },
      {
        field: DateField.TIME,
        headerName: 'Time',
        type: 'numeric-field',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { name: 'cusip' },
            { name: 'exchange' },
            { name: 'isin' },
            { fill: 'name', name: 'name', priorityFill: 'shortName' },
          ],
          dictionary: 'tickers',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Ticker',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Company Name	',
        type: 'medium-width',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'exchanges',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange',
        initialHide: true,
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'currency_codes',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'currency',
        filter: 'agTextColumnFilter',
        headerName: 'Currency',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'isin',
        filter: 'agTextColumnFilter',
        headerName: 'ISIN',
        initialHide: true,
      },
      {
        field: 'cusip',
        filter: 'agTextColumnFilter',
        headerName: 'CUSIP',
        initialHide: true,
      },
      {
        field: 'period',
        filter: 'agSetColumnFilter',
        headerName: 'Period',
      },
      {
        field: 'period_year',
        filter: 'agTextColumnFilter',
        headerName: 'Year',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'prelim',
        filter: 'agSetColumnFilter',
        headerClass: 'split-header',
        headerName: 'Preliminary\nGuidance',
        valueGetter: valueGetter.booleanGetterHOF('prelim'),
        valueSetter: valueSetter.booleanSetter('prelim'),
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'eps_type',
        },
        cellEditorPopup: true,
        cellStyle: { backgroundColor: '#39a6ff4d' },
        editable: true,
        field: 'eps_type',
        filter: 'agTextColumnFilter', // Inline style for cell background
        headerName: 'EPS Type',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellClass: ['bold-text', 'numeric-cell'],
        cellStyle: { backgroundColor: '#39a6ff4d' },
        field: 'eps_guidance_min',
        filter: 'agTextColumnFilter',
        headerName: 'EPS Min',
        type: 'numeric-field',
      },
      {
        cellClass: ['bold-text', 'numeric-cell'],
        cellStyle: { backgroundColor: '#39a6ff4d' },
        field: 'eps_guidance_max',
        filter: 'agTextColumnFilter',
        headerName: 'EPS Max',
        type: 'numeric-field',
      },
      {
        editable: false,
        filter: 'agTextColumnFilter',
        headerName: 'RF.EPS-EST-GAP',
        type: 'numeric-field',
        valueGetter: params =>
          params.data.refinitiv_estimate && params.data.refinitiv_estimate.currency === 'USD'
            ? params.data.refinitiv_estimate.gps
            : null,
      },
      {
        editable: false,
        filter: 'agTextColumnFilter',
        headerName: 'RF.FFO-EST',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: params =>
          params.data.refinitiv_estimate && params.data.refinitiv_estimate.currency === 'USD'
            ? params.data.refinitiv_estimate.ffo_est
            : null,
      },
      {
        editable: false,
        filter: 'agTextColumnFilter',
        headerName: 'RF.EPS-EST-ADJ',
        type: 'numeric-field',
        valueGetter: params =>
          params.data.refinitiv_estimate && params.data.refinitiv_estimate.currency === 'USD'
            ? params.data.refinitiv_estimate.eps_est
            : null,
      },
      {
        field: 'eps_guidance_est',
        filter: 'agTextColumnFilter',
        headerName: 'EPS Est',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'eps_guidance_prior_min',
        filter: 'agTextColumnFilter',
        headerClass: 'split-header',
        headerName: 'EPS\nPrior Min',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('eps_guidance_prior_min'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'eps_guidance_prior_max',
        filter: 'agTextColumnFilter',
        headerClass: 'split-header',
        headerName: 'EPS\nPrior Max',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('eps_guidance_prior_max'),
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'revs_type',
        },
        cellEditorPopup: true,
        cellStyle: { backgroundColor: '#39a6ff4d' },
        editable: true,
        field: 'revenue_type',
        filter: 'agTextColumnFilter',
        headerName: 'Revs Type	',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellClass: ['bold-text', 'numeric-cell'],
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        cellStyle: { backgroundColor: '#39a6ff4d' },
        field: 'revenue_guidance_min',
        filter: 'agTextColumnFilter',
        headerName: 'Revs Min',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('revenue_guidance_min'),
      },
      {
        cellClass: ['bold-text', 'numeric-cell'],
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        cellStyle: { backgroundColor: '#39a6ff4d' },
        field: 'revenue_guidance_max',
        filter: 'agTextColumnFilter',
        headerName: 'Revs Max',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('revenue_guidance_max'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        filter: 'agTextColumnFilter',
        headerName: 'RF.REV-EST',
        type: 'numeric-field',
        valueGetter: params =>
          params.data.refinitiv_estimate && params.data.refinitiv_estimate.currency === 'USD'
            ? params.data.refinitiv_estimate.revenue_est
            : null,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'revenue_guidance_est',
        filter: 'agTextColumnFilter',
        headerName: 'Revs Est',
        initialHide: true,
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('revenue_guidance_est'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'revenue_guidance_prior_min',
        filter: 'agTextColumnFilter',
        headerClass: 'split-header',
        headerName: 'Revs\nPrior Min',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('revenue_guidance_prior_min'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'revenue_guidance_prior_max',
        filter: 'agTextColumnFilter',
        headerClass: 'split-header',
        headerName: 'Revs\nPrior Max',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('revenue_guidance_prior_max'),
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoComplete: isPrimaryAutoCompleteItems,
        },
        cellEditorPopup: true,
        editable: true,
        field: 'is_primary',
        filter: 'agSetColumnFilter',
        headerClass: 'split-header',
        headerName: 'Is\nPrimary',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
        valueGetter: valueGetter.booleanWithNullGetterHOF('is_primary'),
        valueSetter: valueSetter.booleanWithNullSetter('is_primary'),
      },
      {
        field: 'bullbear',
        filter: 'agTextColumnFilter',
        headerName: 'Bullbear',
        initialHide: true,
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'url',
        filter: 'agTextColumnFilter',
        headerName: 'URL',
        initialHide: true,
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CheckboxField,
        cellRendererParams: { onlyCheckbox: true },
        editable: false,
        field: 'calEst',
        headerName: 'Calendar Estimates',
        width: 50, // Centers content in the cell
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',
        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Date' }],
    defaultEditingColumn: 'ticker',
    icon: IconNames.ENDORSED,
    mongoCollectionId: 'guidance',
    name: 'Guidance',
    permissionResourceId: 'guidance-calendar',
    skippableFields: ['cusip', 'isin', 'name'],
    storyGenerator: guidanceGenerator,
  },
  [CollectionId.ipo]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.ipo,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Open Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF(DateField.DATE),
      },
      {
        field: DateField.TIME,
        headerName: 'Time',
        type: 'numeric-field',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { name: 'cusip' },
            { name: 'exchange' },
            { name: 'isin' },
            { fill: 'name', name: 'name', priorityFill: 'shortName' },
            { name: 'type' },
          ],
          dictionary: 'tickers',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Ticker',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'exchanges',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'currency_codes',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'currency',
        filter: 'agTextColumnFilter',
        headerName: 'Currency',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Company Name',
        type: 'width-medium',
      },
      {
        field: 'tax_id',
        filter: 'agTextColumnFilter',
        headerName: 'Tax ID',
        type: 'numeric-field',
      },
      {
        field: 'isin',
        filter: 'agTextColumnFilter',
        headerName: 'ISIN',
      },
      {
        field: 'cusip',
        filter: 'agTextColumnFilter',
        headerName: 'CUSIP',
        type: 'numeric-field',
      },
      {
        field: 'sedol',
        filter: 'agTextColumnFilter',
        headerName: 'SEDOL',
        type: 'numeric-field',
      },
      {
        field: 'sec_file_no',
        filter: 'agTextColumnFilter',
        headerName: 'SEC File No.',
        type: 'numeric-field',
      },
      {
        field: 'initial_filing_date',
        filter: 'agTextColumnFilter',
        headerName: 'Initial filing date',
        type: 'numeric-field',
      },
      {
        editable: false,
        field: 'insider_lockup_date',
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Ins Lockup Date.',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('insider_lockup_date'),
      },
      {
        field: 'pricing_date',
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Pricing Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('pricing_date'),
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'pricing_date_verified',
        filter: 'agSetColumnFilter',
        headerName: 'Pricing Date Verified',
        valueGetter: valueGetter.booleanGetterHOF('pricing_date_verified'),
        valueSetter: valueSetter.booleanSetter('pricing_date_verified'),
      },
      {
        field: 'number_locked_up',
        filter: 'agTextColumnFilter',
        headerName: 'Num Locked Up',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'ipo_deal_status',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'deal_status',
        filter: 'agTextColumnFilter',
        headerName: 'Deal',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'ipo_type',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'ipo_type',
        filter: 'agTextColumnFilter',
        headerName: 'IPO Type',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'spac_converted_to_target',
        filter: 'agSetColumnFilter',
        headerName: 'Has a SPAC converted to its target ticker?',
        valueGetter: valueGetter.booleanGetterHOF('spac_converted_to_target'),
        valueSetter: valueSetter.booleanSetter('spac_converted_to_target'),
      },
      {
        field: 'price_min',
        filter: 'agTextColumnFilter',
        headerName: 'Price Min',
        type: 'numeric-field',
      },
      {
        field: 'price_max',
        filter: 'agTextColumnFilter',
        headerName: 'Price Max',
        type: 'numeric-field',
      },
      {
        field: 'price_public_offering',
        filter: 'agTextColumnFilter',
        headerName: 'Public offering price (POP)',
        type: 'numeric-field',
      },
      {
        field: 'price_open',
        filter: 'agTextColumnFilter',
        headerName: 'Open Price',
        type: 'numeric-field',
      },
      {
        field: 'insider_lockup_days',
        filter: 'agTextColumnFilter',
        headerName: 'Ins Lockup Days',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'open_date_verified',
        filter: 'agSetColumnFilter',
        headerName: 'Open Verified',
        valueGetter: valueGetter.booleanGetterHOF('open_date_verified'),
        valueSetter: valueSetter.booleanSetter('open_date_verified'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'shares_outstanding',
        filter: 'agTextColumnFilter',
        headerName: 'Shares Out',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('shares_outstanding'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'offering_shares',
        filter: 'agTextColumnFilter',
        headerName: 'Offering Shares',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('offering_shares'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'offering_value',
        filter: 'agTextColumnFilter',
        headerName: 'Offering Value',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('offering_value'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'ord_shares_out_after_offer',
        filter: 'agTextColumnFilter',
        headerName: 'Total ordinary shares out after the offering',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('ord_shares_out_after_offer'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'market_cap_at_offer',
        filter: 'agTextColumnFilter',
        headerName: 'Total market cap at the moment of the offer',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('market_cap_at_offer'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'offering_shares_ord_adr',
        filter: 'agTextColumnFilter',
        headerName: 'For ADRs, the total amount of ordinary shares that the ADRs represent',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('offering_shares_ord_adr'),
      },
      {
        field: 'shares_authorized',
        filter: 'agTextColumnFilter',
        headerName: 'Authorized Shares',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          arrayType: 'object',
          dictionary: 'firm_name',
          field: 'name',
          isMulti: true,
          itemField: 'name',
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.joinArrayField('name'),
        field: 'lead_underwriters',
        headerName: 'Lead Underwriters',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          arrayType: 'object',
          dictionary: 'firm_name',
          field: 'name',
          isMulti: true,
          itemField: 'name',
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.joinArrayField('name'),
        field: 'other_underwriters',
        headerName: 'Other Underwriters',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'sec_filing_url',
        filter: 'agTextColumnFilter',
        headerName: 'F1/S1/other link information is pulled from',
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'last_year_revenue',
        filter: 'agTextColumnFilter',
        headerName: "Last year's revenue (in USD)",
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('last_year_revenue'),
      },
      {
        field: 'last_year_revenue_year',
        filter: 'agTextColumnFilter',
        headerName: 'The year the "last year\'s revenue" refers to',
        valueParser: valueParser.integerParser,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'last_year_income',
        filter: 'agTextColumnFilter',
        headerName: "Last year's income (in USD)",
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('last_year_income'),
      },
      {
        field: 'last_year_income_year',
        filter: 'agTextColumnFilter',
        headerName: 'The year the "last year\'s income" refers to',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'description',
        filter: 'agTextColumnFilter',
        headerName: 'Company description, including description of target for SPAC',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'sic',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'sic',
        filter: 'agTextColumnFilter',
        headerName: 'SIC Code',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'state_code',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'state_location',
        filter: 'agTextColumnFilter',
        headerName: 'State location code',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'sec_accession_number',
        filter: 'agTextColumnFilter',
        headerName: 'SEC Accession Number',
        type: 'numeric-field',
      },
      {
        field: 'underwriter_quiet_expiration_date',
        filter: 'agTextColumnFilter',
        headerName: 'Quiet Expiration Date',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        field: 'underwriter_quiet_expiration_days',
        filter: 'agTextColumnFilter',
        headerName: 'Quiet Expiration Days',
        initialHide: true,
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        headerName: 'Action',

        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [
      { field: DateField.DATE, title: 'Open Date' },
      { field: 'insider_lockup_date', title: 'Ins Lockup Date' },
      { field: 'pricing_date', title: 'Pricing Date' },
      {
        field: 'underwriter_quiet_expiration_date',
        title: 'Quiet Experiation Date',
      },
    ],
    defaultEditingColumn: 'ticker',
    icon: IconNames.SERIES_ADD,
    mongoCollectionId: 'ipo',
    name: 'Initial Public Offering',
    permissionResourceId: 'ipo-calendar',
    skippableFields: ['exchange', 'isin', 'name'],
  },
  [CollectionId.offering]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.offering,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,

        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF(DateField.DATE),
      },
      {
        field: DateField.TIME,
        headerName: 'Time',
        type: 'numeric-field',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { name: 'cusip' },
            { name: 'exchange' },
            { name: 'isin' },
            { fill: 'name', name: 'name', priorityFill: 'shortName' },
          ],
          dictionary: 'tickers',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Ticker',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { name: 'cusip' },
            { name: 'exchange' },
            { name: 'isin' },
            { fill: 'name', name: 'name', priorityFill: 'shortName' },
          ],
          dictionary: 'offering_type',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'offering_type',
        filter: 'agTextColumnFilter',
        headerName: 'Offering Type',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'exchanges',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'currency_codes',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'currency',
        filter: 'agTextColumnFilter',
        headerName: 'Currency',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'isin',
        filter: 'agTextColumnFilter',
        headerName: 'ISIN',
        initialHide: true,
      },
      {
        field: 'cusip',
        filter: 'agTextColumnFilter',
        headerName: 'CUSIP',
        initialHide: true,
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Company Name',
        type: 'width-medium',
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'dollar_shares',
        filter: 'agTextColumnFilter',
        headerName: 'Dollar Shares',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('dollar_shares'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'price',
        filter: 'agTextColumnFilter',
        headerName: 'Price',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('price'),
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'shelf',
        filter: 'agSetColumnFilter',
        headerName: 'Shelf',
        valueGetter: valueGetter.booleanGetterHOF('shelf'),
        valueSetter: valueSetter.booleanSetter('shelf'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'number_shares',
        filter: 'agSetColumnFilter',
        headerName: 'Number Shares',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('number_shares'),
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'proceeds',
        filter: 'agTextColumnFilter',
        headerName: 'Proceeds',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('proceeds'),
      },
      {
        field: 'url',
        filter: 'agTextColumnFilter',
        headerName: 'URL',
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',

        type: 'action-field',
      },
    ],

    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Date' }],
    defaultEditingColumn: 'ticker',
    icon: IconNames.GIT_NEW_BRANCH,
    mongoCollectionId: 'offering',
    name: 'Secondary Offering',
    permissionResourceId: 'offering-calendar',
  },
  [CollectionId.rating]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.rating,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF(DateField.DATE),
      },
      {
        field: DateField.TIME,
        headerName: 'Time',
        type: 'numeric-field',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { name: 'cusip' },
            { name: 'exchange' },
            { name: 'isin' },
            { fill: 'name', name: 'name', priorityFill: 'shortName' },
          ],
          dictionary: 'tickers',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Ticker',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'exchanges',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'currency_codes',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'currency',
        filter: 'agTextColumnFilter',
        headerName: 'Currency',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'isin',
        filter: 'agTextColumnFilter',
        headerName: 'ISIN',
        initialHide: true,
      },
      {
        field: 'cusip',
        filter: 'agTextColumnFilter',
        headerName: 'CUSIP',
        initialHide: true,
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerClass: 'split-header',
        headerName: 'Company\nName',
        type: 'width-medium',
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          autoFll: ['analyst_obj'],
          dictionary: 'firm_name',
          field: 'name',
          isMulti: false,
          itemField: 'name',
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.renderNameWithValidation,
        comparator: (valueA, valueB) => {
          if (valueA === null) {
            return 1;
          } else if (valueB === null) {
            return -1;
          } else {
            if (valueA.name === valueB.name) return 0;
            return valueA.name > valueB.name ? 1 : -1;
          }
        },
        field: 'firm',
        filter: 'agTextColumnFilter',
        headerClass: 'split-header',
        headerName: 'Analyst\nFirm',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          dictionary: 'analyst_name',
          field: 'full_name',
          isMulti: false,
          itemField: 'full_name',
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.renderFullName,
        comparator: (valueA, valueB) => {
          if (valueA === null) {
            return 1;
          } else if (valueB === null) {
            return -1;
          } else {
            if (valueA.full_name === valueB.full_name) return 0;
            return valueA.full_name > valueB.full_name ? 1 : -1;
          }
        },
        field: 'analyst_obj',
        filter: 'agTextColumnFilter',
        headerClass: 'split-header',
        headerName: 'Lead\nAnalyst',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'action_companies',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'action_company',
        filter: 'agTextColumnFilter',
        headerClass: 'split-header',
        headerName: 'Action\nCompany',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'rating_prior',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'rating_prior',
        filter: 'agTextColumnFilter',
        headerClass: 'split-header',
        headerName: 'Prior\nRating',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'rating_current',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'rating_current',
        filter: 'agTextColumnFilter',
        headerName: 'Rating',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'action_pts',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'action_pt',
        filter: 'agTextColumnFilter',
        headerName: 'Action PT',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'pt_prior',
        filter: 'agTextColumnFilter',
        headerName: 'PT Prior',
        type: 'numeric-field',
      },
      {
        field: 'pt_current',
        filter: 'agTextColumnFilter',
        headerName: 'PT',
        type: 'numeric-field',
      },
      {
        field: 'adjusted_pt_prior',
        filter: 'agTextColumnFilter',
        headerName: 'Adjusted PT Prior',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        field: 'adjusted_pt_current',
        filter: 'agTextColumnFilter',
        headerName: 'Adjusted PT',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'verified',
        filter: 'agSetColumnFilter',
        headerClass: 'split-header',
        headerName: 'Rating\nVerified',
        initialHide: true,
        valueGetter: valueGetter.booleanGetterHOF('verified'),
        valueSetter: valueSetter.booleanSetter('verified'),
      },
      {
        field: 'url',
        filter: 'agTextColumnFilter',
        headerName: 'URL',
        initialHide: true,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'focus_lists',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'focus_list',
        headerName: 'Focus List',
        initialHide: true,
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        initialHide: true,
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
        initialHide: true,
      },
      {
        cellRenderer: RatingsFileUpload,
        editable: false,
        field: 'pdf_file',
        filter: 'agTextColumnFilter',
        headerName: 'PDF File',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',
        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Date' }],
    defaultEditingColumn: 'ticker',
    icon: IconNames.COMMENT,
    mongoCollectionId: 'rating',
    name: 'Analyst Rating',
    permissionResourceId: 'rating-calendar',
    skippableFields: ['analyst_obj'],
    storyGenerator: generateStoryRating,
  },
  [CollectionId.retail]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.retail,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF(DateField.DATE),
      },
      {
        field: DateField.TIME,
        headerName: 'Time',
        type: 'numeric-field',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { name: 'cusip' },
            { name: 'exchange' },
            { name: 'isin' },
            { fill: 'name', name: 'name', priorityFill: 'shortName' },
          ],
          dictionary: 'tickers',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Ticker',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'exchanges',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'currency_codes',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'currency',
        filter: 'agTextColumnFilter',
        headerName: 'Currency',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'isin',
        filter: 'agTextColumnFilter',
        headerName: 'ISIN',
        initialHide: true,
      },
      {
        field: 'cusip',
        filter: 'agTextColumnFilter',
        headerName: 'CUSIP',
        initialHide: true,
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Company Name',
        type: 'width-medium',
      },
      {
        field: 'url',
        filter: 'agTextColumnFilter',
        headerName: 'URL',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          alwaysOpen: true,
          autoComplete: [
            { data: 'H1', label: 'H1', value: 'H1' },
            { data: 'H2', label: 'H2', value: 'H2' },
            { data: 'Q1', label: 'Q1', value: 'Q1' },
            { data: 'Q2', label: 'Q2', value: 'Q2' },
            { data: 'Q3', label: 'Q3', value: 'Q3' },
            { data: 'Q4', label: 'Q4', value: 'Q4' },
            { data: 'FY', label: 'FY', value: 'FY' },
            { data: 'Jan', label: 'Jan', value: 'Jan' },
            { data: 'Feb', label: 'Feb', value: 'Feb' },
            { data: 'Mar', label: 'Mar', value: 'Mar' },
            { data: 'Apr', label: 'Apr', value: 'Apr' },
            { data: 'May', label: 'May', value: 'May' },
            { data: 'Jun', label: 'Jun', value: 'Jun' },
            { data: 'Jul', label: 'Jul', value: 'Jul' },
            { data: 'Aug', label: 'Aug', value: 'Aug' },
            { data: 'Sep', label: 'Sep', value: 'Sep' },
            { data: 'Oct', label: 'Oct', value: 'Oct' },
            { data: 'Nov', label: 'Nov', value: 'Nov' },
            { data: 'Dec', label: 'Dec', value: 'Dec' },
          ],
        },
        cellEditorPopup: true,
        editable: true,
        field: 'period',
        filter: 'agSetColumnFilter',
        headerName: 'Period',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'period_year',
        filter: 'agTextColumnFilter',
        headerName: 'Period Year',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'sss',
        filter: 'agTextColumnFilter',
        headerName: 'SSS',
        type: 'numeric-field',
      },
      {
        field: 'sss_est',
        filter: 'agTextColumnFilter',
        headerName: 'SSS Est',
        type: 'numeric-field',
      },
      {
        field: 'retail_surprise',
        filter: 'agTextColumnFilter',
        headerName: 'Surprise',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        field: 'bullbear',
        filter: 'agTextColumnFilter',
        headerName: 'Bullbear',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',

        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Date' }],
    defaultEditingColumn: 'ticker',
    icon: IconNames.SHOP,
    mongoCollectionId: 'retail',
    name: 'Retail Sales',
    permissionResourceId: 'retail-calendar',
    skippableFields: ['sss_est'],
  },
  [CollectionId.sec]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.sec,
    columnDefinitions: [
      {
        cellStyle: { 'text-align': 'right' },
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date Accepted',
        valueGetter: valueGetter.dateHOF(DateField.DATE),
      },
      {
        field: DateField.TIME,
        headerName: 'Time Accepted',
      },
      {
        field: 'date_filed',
        headerName: 'Date Filed',
      },
      {
        field: 'date_filing_changed',
        headerName: 'Date Changed',
      },
      {
        field: 'ticker',
        headerName: 'Ticker',
      },
      {
        field: 'form_type',
        headerName: 'Form Type',
      },
      {
        field: 'amendment',
        headerName: 'Amended',
      },
      {
        field: 'issuer_name',
        headerName: 'Issuer Name',
      },
      {
        field: 'issuer_cik',
        headerName: 'Issuer CIK',
      },
      {
        field: 'reporter_cik',
        headerName: 'Reporter CIK',
      },
      {
        field: 'owner_cik',
        headerName: 'Owner CIK',
      },
      {
        field: 'accenssion_number',
        headerName: 'Accession Number',
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',

        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Date Accepted' }],
    defaultEditingColumn: 'ticker',
    icon: IconNames.BADGE,
    mongoCollectionId: 'sec',
    name: 'SEC',
    permissionResourceId: 'sec-calendar',
  },
  [CollectionId.split]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.split,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF(DateField.DATE),
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { name: 'cusip' },
            { name: 'exchange' },
            { name: 'isin' },
            { fill: 'name', name: 'name', priorityFill: 'shortName' },
          ],
          dictionary: 'tickers',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Ticker',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'exchanges',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'isin',
        filter: 'agTextColumnFilter',
        headerName: 'ISIN',
        initialHide: true,
      },
      {
        field: 'cusip',
        filter: 'agTextColumnFilter',
        headerName: 'CUSIP',
        initialHide: true,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'split_type',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'split_type',
        filter: 'agTextColumnFilter',
        headerName: 'Split Type',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
        type: 'width-medium',
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Company Name',
        type: 'width-medium',
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'optionable',
        filter: 'agSetColumnFilter',
        headerName: 'Optionable',
        valueGetter: valueGetter.booleanGetterHOF('optionable'),
        valueSetter: valueSetter.booleanSetter('optionable'),
      },
      {
        field: 'ratio',
        filter: 'agTextColumnFilter',
        headerName: 'Ratio',
        type: 'numeric-field',
      },
      {
        field: 'date_announced',
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Announced',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('date_announced'),
      },
      {
        field: 'date_distribution',
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Distribution',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('date_distribution'),
      },
      {
        field: 'date_recorded',
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Recorded',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('date_recorded'),
      },
      {
        field: 'url',
        filter: 'agTextColumnFilter',
        headerName: 'URL',
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',
        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Date' }],
    defaultEditingColumn: 'ticker',
    icon: IconNames.FORK,
    mongoCollectionId: 'split',
    name: 'Split',
    permissionResourceId: 'split-calendar',
  },
  [CollectionId.wiim]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.wiim,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF(DateField.DATE),
      },
      {
        field: DateField.TIME,
        headerName: 'Time',
        type: 'numeric-field',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          autoFill: [
            { name: 'cusip' },
            { name: 'exchange' },
            { name: 'isin' },
            { fill: 'name', name: 'name', priorityFill: 'shortName' },
          ],
          collectionId: CollectionId.wiim,
          dictionary: 'tickers',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Ticker',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'exchanges',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'isin',
        filter: 'agTextColumnFilter',
        headerName: 'ISIN',
        initialHide: true,
      },
      {
        field: 'cusip',
        filter: 'agTextColumnFilter',
        headerName: 'CUSIP',
        initialHide: true,
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Company Name',
        type: 'width-medium',
      },
      {
        cellEditor: DatePickerPopup,
        field: 'expired',
        filter: 'agTextColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Expired',
        type: 'numeric-field',
        valueGetter: valueGetter.dateFromMillies('expired'),
      },
      {
        field: 'price_start',
        filter: 'agTextColumnFilter',
        headerName: 'Price Start',
        type: 'numeric-field',
      },
      {
        field: 'price_end',
        filter: 'agTextColumnFilter',
        headerName: 'Price Trigger',
        type: 'numeric-field',
      },
      {
        field: 'change',
        filter: 'agTextColumnFilter',
        headerName: 'Change',
        type: 'numeric-field',
      },
      {
        field: 'change_percent',
        filter: 'agTextColumnFilter',
        headerName: 'Change %',
        type: 'numeric-field',
      },
      {
        field: 'country',
        filter: 'agTextColumnFilter',
        headerName: 'Country Code',
        minWidth: 200,
      },
      {
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: ['N', 'Y'],
        },
        cellEditorPopup: true,
        field: 'optionable_stock',
        filter: 'agSetColumnFilter',
        headerClass: 'split-header',
        headerName: 'Optionable Stock',
        minWidth: 50,
        valueGetter: valueGetter.booleanGetterHOF('optionable_stock'),
        valueSetter: valueSetter.booleanSetter('optionable_stock'),
      },
      {
        field: 'summary',
        filter: 'agTextColumnFilter',
        headerName: 'Summary',
        minWidth: 600,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',
        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Date' }],
    defaultEditingColumn: 'ticker',
    icon: IconNames.CHANGES,
    mongoCollectionId: 'wiim',
    name: 'WIIM',
    permissionResourceId: 'wiim-calendar',
    storyGenerator: generateStoryWiim,
  },
  [CollectionId.tradeIdeas]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.tradeIdeas,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        colId: DateField.CREATED,
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF(DateField.DATE),
      },
      {
        field: DateField.TIME,
        headerName: 'Time',
        type: 'numeric-field',
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          arrayType: 'string',
          dictionary: 'tickers',
          field: 'symbol',
          isMulti: true,
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.stringifyTickers,
        field: 'ticker',
        headerName: 'Tickers',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
        valueSetter: valueSetter.tickersSetter('ticker'),
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'trader_profile',
          field: 'symbol',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'trader',
        filter: 'agTextColumnFilter',
        headerName: 'Trader',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
        valueSetter: valueSetter.traderSetter('trader'),
      },
      {
        cellEditor: 'agSelectCellEditor',
        cellEditorParams: {
          values: ['Short', 'Long'],
        },
        field: 'long_short',
        filter: 'agTextColumnFilter',
        headerName: 'Long/Short',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'theory',
        filter: 'agTextColumnFilter',
        headerName: 'Theory',
      },
      {
        field: 'platform',
        filter: 'agTextColumnFilter',
        headerName: 'Platform',
      },
      {
        field: 'idea_url',
        filter: 'agTextColumnFilter',
        headerName: 'Idea URL',
        type: 'width-medium',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',

        type: 'action-field',
      },
    ],

    defaultColumnDefinition,
    defaultDateFieldList: [{ field: DateField.DATE, title: 'Date' }],
    defaultEditingColumn: 'ticker',
    icon: IconNames.GIT_NEW_BRANCH,
    mongoCollectionId: 'tradeidea',
    name: 'Trade Ideas',
    permissionResourceId: 'trade-ideas-calendar',
  },
  [CollectionId.fda]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.fda,
    columnDefinitions: [
      {
        editable: false,
        field: 'user_id',
        headerName: 'User Id',
        initialHide: true,
        type: 'numeric-field',
      },
      {
        editable: false,
        field: DateField.CREATED,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateHOF(DateField.CREATED),
      },
      {
        colId: DateField.UPDATED,
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Last Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('date'),
      },
      {
        field: 'time',
        filter: 'agTextColumnFilter',
        headerName: 'Time',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'fda_event_type',
          field: 'event_type',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'event_type',
        filter: 'agTextColumnFilter',
        headerName: 'Event Type',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'drug',
          field: 'drug',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'drug',
        filter: 'agTextColumnFilter',
        filterParams: {
          valueGetter: valueGetter.getFieldObjectNameValue('drug'),
        },
        headerName: 'Drug',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
        valueSetter: valueSetter.stringWithNullSetter('drug'),
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          arrayType: 'object',
          dictionary: 'drug_company',
          field: 'companies',
          isMulti: true,
          itemField: 'name',
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.joinArrayField('drugs_companies'),
        field: 'companies',
        filter: 'agTextColumnFilter',
        filterParams: {
          valueGetter: valueGetter.getArrayFieldObjectNameValue('companies'),
        },
        headerName: 'Company/companies reference',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'drug_status',
          field: 'status',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'status',
        filter: 'agTextColumnFilter',
        headerName: 'Drug Status',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
      },
      {
        field: 'nic_number',
        filter: 'agTextColumnFilter',
        headerName: 'NIC Number',
        initialHide: true,
      },
      {
        field: 'target_date',
        filter: 'agTextColumnFilter',
        headerName: 'Target Date',
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'fda_outcome_brief',
          field: 'outcome_brief',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'outcome_brief',
        filter: 'agTextColumnFilter',
        headerName: 'Categorized outcome',
      },
      {
        field: 'outcome',
        filter: 'agTextColumnFilter',
        headerName: 'Significance/outcome',
      },
      {
        field: 'commentary',
        filter: 'agTextColumnFilter',
        headerName: 'Commentary',
        initialHide: true,
      },
      {
        cellEditor: AutoComplete,
        cellEditorParams: {
          dictionary: 'fda_source_type',
          field: 'source_type',
        },
        cellEditorPopup: true,
        editable: true,
        field: 'source_type',
        filter: 'agTextColumnFilter',
        headerName: 'Source Type',
      },
      {
        field: 'source_link',
        filter: 'agTextColumnFilter',
        headerName: 'Source link',
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        valueParser: valueParser.integerParser,
      },
      {
        field: 'notes',
        filter: 'agTextColumnFilter',
        headerName: 'Notes',
      },
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',

        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [],
    defaultEditingColumn: 'event_type',
    icon: IconNames.GIT_NEW_BRANCH,
    mongoCollectionId: 'fda',
    name: 'FDA',
    permissionResourceId: 'fda-calendar',
    skippableFields: ['companies'],
  },
  [CollectionId.refinitivEstimates]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.refinitivEstimates,
    columnDefinitions: [
      {
        cellRenderer: CollectionActions,
        cellRendererParams: {
          collectionGroupId: CollectionGroupId.calendar,
        },
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',
        pinned: 'left',
        type: 'action-field',
      },
      {
        field: DateField.DATE,
        filter: 'agDateColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date',
        sort: 'desc',
        type: 'numeric-field',
        valueGetter: valueGetter.nullGetterHOF('date'),
        width: 100,
      },
      {
        field: 'time',
        filter: 'agTextColumnFilter',
        headerName: 'Time',
      },
      {
        field: 'ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Symbol',
        width: 120,
      },
      {
        field: 'exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange',
        width: 100,
      },
      {
        field: 'name',
        filter: 'agTextColumnFilter',
        headerName: 'Name',
        width: 120,
      },
      {
        field: 'period',
        filter: 'agTextColumnFilter',
        headerName: 'Period',
      },
      {
        editable: false,
        field: 'period_year',
        filter: 'agTextColumnFilter',
        headerName: 'Year',
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'currency',
        filter: 'agTextColumnFilter',
        headerName: 'Currency',
        width: 120,
      },
      {
        //cellRenderer: cellRenderer.renderShortHandWithValidation,
        cellRenderer: CheckboxField,
        editable: false,
        field: 'gps',
        headerName: 'GAAP EPS EST',
        width: 120,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'gaap_eps_prior',
        filter: 'agTextColumnFilter',
        headerName: 'GAAP EPS Prior',
        type: 'numeric-field',
        width: 150,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'gaap_eps',
        filter: 'agTextColumnFilter',
        headerName: 'GAAP EPS',
        type: 'numeric-field',
        width: 120,
      },
      {
        cellRenderer: CheckboxField,
        editable: false,
        field: 'eps_est',
        filter: 'agTextColumnFilter',
        headerName: 'Non GAAP EPS EST',
        width: 150,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'non_gaap_eps_prior',
        filter: 'agTextColumnFilter',
        headerName: 'Non GAAP EPS Prior',
        type: 'numeric-field',
        width: 170,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'non_gaap_eps',
        filter: 'agTextColumnFilter',
        headerName: 'Non GAAP EPS',
        type: 'numeric-field',
        width: 150,
      },
      {
        cellRenderer: CheckboxField,
        editable: false,
        field: 'ffo_est',
        filter: 'agTextColumnFilter',
        headerName: 'FFO EST',
        width: 120,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'ffo_prior',
        filter: 'agTextColumnFilter',
        headerName: 'FFO Prior',
        type: 'numeric-field',
        width: 120,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'ffo',
        filter: 'agTextColumnFilter',
        headerName: 'FFO',
        width: 100,
      },
      {
        cellRenderer: CheckboxField,
        editable: false,
        field: 'revenue_est',
        filter: 'agTextColumnFilter',
        headerName: 'Revenue EST',
        valueSetter: valueSetter.setShortHandField('revenue_est'),
        width: 150,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'revenue_prior',
        filter: 'agTextColumnFilter',
        headerName: 'Revenue Prior',
        type: 'numeric-field',
        valueSetter: valueSetter.setShortHandField('revenue_prior'),
        width: 150,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        field: 'revenue',
        filter: 'agTextColumnFilter',
        headerName: 'Revenue',
        type: 'numeric-field',
        width: 100,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'ebt',
        filter: 'agTextColumnFilter',
        headerName: 'EBITDA',
        initialHide: true,
        width: 100,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'ebi',
        filter: 'agTextColumnFilter',
        headerName: 'EBIT',
        initialHide: true,
        width: 100,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'grm',
        filter: 'agTextColumnFilter',
        headerName: 'Gross Margin',
        initialHide: true,
        width: 120,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'cpx',
        filter: 'agTextColumnFilter',
        headerName: 'CapEx',
        initialHide: true,
        width: 120,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'bps',
        filter: 'agTextColumnFilter',
        headerName: 'Book Value',
        initialHide: true,
        width: 120,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'cps',
        filter: 'agTextColumnFilter',
        headerName: 'CPS',
        initialHide: true,
        width: 120,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'dps',
        filter: 'agTextColumnFilter',
        headerName: 'DPS',
        initialHide: true,
        width: 120,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'nav',
        filter: 'agTextColumnFilter',
        headerName: 'NAV',
        initialHide: true,
        width: 120,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'ndt',
        filter: 'agTextColumnFilter',
        headerName: 'NDT',
        initialHide: true,
        width: 120,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'net',
        filter: 'agTextColumnFilter',
        headerName: 'NET',
        initialHide: true,
        width: 100,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'pre',
        filter: 'agTextColumnFilter',
        headerName: 'PRE',
        initialHide: true,
        width: 100,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'roa',
        filter: 'agTextColumnFilter',
        headerName: 'ROA',
        initialHide: true,
        width: 100,
      },
      {
        cellRenderer: cellRenderer.renderShortHandWithValidation,
        editable: false,
        field: 'roe',
        filter: 'agTextColumnFilter',
        headerName: 'ROE',
        initialHide: true,
        width: 100,
      },
      {
        field: 'importance',
        filter: 'agTextColumnFilter',
        headerName: 'Importance',
        type: 'numeric-field',
        width: 120,
      },
      {
        editable: false,
        field: DateField.CREATED,
        headerName: 'Created',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.CREATED),
      },
      {
        editable: false,
        field: DateField.UPDATED,
        headerName: 'Updated',
        initialHide: true,
        type: 'numeric-field',
        valueGetter: valueGetter.dateTimeHOF(DateField.UPDATED),
      },
      {
        editable: false,
        field: 'isin',
        filter: 'agTextColumnFilter',
        headerName: 'ISIN',
        initialHide: true,
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [],
    defaultEditingColumn: '',
    icon: IconNames.GIT_NEW_BRANCH,
    mongoCollectionId: 'refinitiv_estimate',
    name: 'Refinitiv Estimates',
    permissionResourceId: 'refinitiv-calendar',
    skippableFields: [],
    storyGenerator: generateStoryRefinitiv,
  },
  [CollectionId.bullsBearsSay]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.bullsBearsSay,
    columnDefinitions: [
      {
        editable: false,
        field: 'date',
        filter: 'agTextColumnFilter',
        headerName: 'Date',
        width: 100,
      },
      {
        editable: false,
        field: 'ticker',
        filter: 'agTextColumnFilter',
        headerName: 'Symbol',
        width: 100,
      },
      {
        editable: false,
        field: 'exchange',
        filter: 'agTextColumnFilter',
        headerName: 'Exchange',
        width: 100,
      },
      {
        cellEditor: 'agLargeTextCellEditor',
        cellEditorParams: {
          maxLength: 1000000,
        },
        cellEditorPopup: true,
        editable: false,
        field: 'bear_case',
        headerName: 'Bear Case',
        width: 550,
      },
      {
        cellEditor: 'agLargeTextCellEditor',
        cellEditorParams: {
          maxLength: 1000000,
        },
        cellEditorPopup: true,
        editable: false,
        field: 'bull_case',
        headerName: 'Bull Case',
        width: 550,
      },
      {
        cellRenderer: CollectionActions,
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',

        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [],
    defaultEditingColumn: 'bear_case',
    icon: IconNames.GIT_NEW_BRANCH,
    mongoCollectionId: 'bulls_bears_say',
    name: 'Bulls Say Bears Say',
    permissionResourceId: 'bullsBearsSay-calendar',
    skippableFields: [],
  },
  [CollectionId.squawk_transcription]: {
    collectionGroupId: CollectionGroupId.calendar,
    collectionId: CollectionId.squawk_transcription,
    columnDefinitions: [
      {
        cellEditor: DatePickerPopup,
        editable: false,
        field: 'timestamp',
        filter: 'agTextColumnFilter',
        filterParams: filterParams.dateComparator,
        headerName: 'Date Time',
        sort: 'desc',
        type: 'numeric-field',
        valueGetter: valueGetter.dateFromMillies('timestamp'),
      },
      {
        cellEditor: MultiselectField,
        cellEditorParams: {
          arrayType: 'object',
          dictionary: 'tickers',
          field: 'symbol',
          fullDetails: true,
          isMulti: true,
          itemField: 'symbol',
        },
        cellEditorPopup: true,
        cellRenderer: cellRenderer.stringifyTickers,
        field: 'securities',
        headerName: 'Securities',
        suppressKeyboardEvent: suppressKeyboardNavigation.supressAutocomplete,
        valueSetter: valueSetter.tickersSetter('securities'),
        width: 100,
      },
      {
        cellEditor: 'agLargeTextCellEditor',
        cellEditorParams: {
          maxLength: 5000,
        },
        cellEditorPopup: true,
        field: 'text',
        headerName: 'Text',
        singleClickEdit: true,
        width: 850,
      },
      {
        editable: false,
        field: 'channel_name',
        filter: 'agTextColumnFilter',
        headerName: 'Channel Name',
        width: 100,
      },
      {
        cellRenderer: CollectionActions,
        colId: 'action',
        editable: false,
        field: 'errors',
        headerName: 'Action',
        type: 'action-field',
      },
    ],
    defaultColumnDefinition,
    defaultDateFieldList: [],
    defaultEditingColumn: 'text',
    icon: IconNames.COMMENT,
    mongoCollectionId: 'squawk_transcription',
    name: 'Squawk Transcription',
    permissionResourceId: 'squawk-transcription-calendar',
    skippableFields: [],
  },
};
